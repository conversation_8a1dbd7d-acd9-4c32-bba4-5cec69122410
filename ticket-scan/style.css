/* style.css - UPDATED for dark theme and primary color */
:root {
    --primary-color: #CE5F27;
    --primary-text-color-on-dark: #FFFFFF;
    --secondary-text-color-on-dark: #CCCCCC;
    --background-color: #010101;
    --section-background-color: #1A1A1A; /* Darker gray for sections */
    --input-background-color: #2C2C2C;
    --input-border-color: #444444;
    --input-text-color: #E0E0E0;
    --button-text-color: #FFFFFF;
    --error-bg: #5c1f1f; /* Darker error background */
    --error-border: #a73c3c;
    --error-text: #ffdddd;
    --success-bg: #224b22; /* Darker success background */
    --success-border: #4CAF50;
    --success-text: #ddffdd;
    --info-bg: #1f3a5c; /* Darker info background */
    --info-border: #3c76a7;
    --info-text: #ddeeff;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    margin: 0;
    background-color: var(--background-color);
    color: var(--primary-text-color-on-dark);
    display: flex;
    justify-content: center;
    align-items: flex-start;
    min-height: 100vh;
    padding: 15px;
    box-sizing: border-box;
}

.app-container {
    width: 100%;
    max-width: 600px;
    box-sizing: border-box;
}

header {
    text-align: center;
    margin-bottom: 25px;
}

header h1 {
    color: var(--primary-color); /* Use primary color */
    margin: 0;
    font-size: 1.8em;
}

h2 { /* Section titles */
    color: var(--primary-text-color-on-dark);
    font-size: 1.4em;
    margin-top: 0;
    margin-bottom: 15px;
}

#result-section-title {
    color: var(--primary-text-color-on-dark);
    font-size: 1.4em;
    margin-top: 0;
    margin-bottom: 15px;
    text-align: center;
}

section {
    margin-bottom: 25px;
    padding: 15px;
    border: 1px solid var(--input-border-color);
    border-radius: 6px;
    background-color: var(--section-background-color); /* Keep sections slightly distinct or same as app-container */
}

#qr-reader {
    border: 1px dashed #555555; /* Darker dash */
    border-radius: 5px;
    margin-bottom: 15px;
    background-color: #000000; /* Black background for QR reader area */
    min-height: 250px;
    position: relative;
}

#qr-reader video {
    width: 100% !important;
    height: auto !important;
    border-radius: 4px;
}

.status-message-container {
    margin-top: 5px;
    margin-bottom: 10px;
    text-align: center;
}

.status-message-item {
    padding: 8px 12px;
    margin-bottom: 6px;
    border-radius: 4px;
    font-weight: 500;
    word-wrap: break-word;
}
.status-message-item:last-child {
    margin-bottom: 0;
}

.status-message-item.info { background-color: var(--info-bg); border: 1px solid var(--info-border); color: var(--info-text); }
.status-message-item.success { background-color: var(--success-bg); border: 1px solid var(--success-border); color: var(--success-text); }
.status-message-item.error { background-color: var(--error-bg); border: 1px solid var(--error-border); color: var(--error-text); }


.customer-details p {
    margin: 8px 0;
    font-size: 1.0em;
    line-height: 1.5;
    word-wrap: break-word;
    color: var(--primary-text-color-on-dark);
}

.customer-details strong {
    color: var(--secondary-text-color-on-dark); /* Lighter than main text for emphasis */
    display: inline-block;
    min-width: 90px;
}

/* Session Selector Styles */
.session-selector {
    margin-top: 20px;
    margin-bottom: 15px;
}
.session-selector label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: var(--secondary-text-color-on-dark);
}
.form-select { /* Dropdown style */
    width: 100%;
    padding: 10px;
    border: 1px solid var(--input-border-color);
    border-radius: 5px;
    background-color: var(--input-background-color);
    color: var(--input-text-color);
    font-size: 1em;
    box-sizing: border-box;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cpath%20d%3D%22M5%208%20l5%205%205-5z%22%20fill%3D%22%23cccccc%22/%3E%3C/svg%3E'); /* Lighter arrow for dark theme */
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 12px;
}
.form-select:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(206, 95, 39, 0.3); /* Primary color focus ring */
}


.btn {
    display: inline-block;
    color: var(--button-text-color);
    padding: 12px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1em;
    text-align: center;
    transition: background-color 0.2s ease-in-out, opacity 0.2s ease-in-out;
    margin: 5px;
    width: calc(100% - 10px);
    box-sizing: border-box;
    font-weight: 500;
}
.btn:hover {
    opacity: 0.85;
}

/* Primary themed buttons */
.btn.btn-primary, .btn.btn-primary-themed {
    background-color: var(--primary-color);
}
.btn.btn-primary:hover, .btn.btn-primary-themed:hover {
    background-color: #b34d1e; /* Darker shade of primary for hover */
}

/* Success button (can also be primary themed if desired) */
.btn.btn-success {
    background-color: #28a745; /* Keep green for success or change to primary */
}
.btn.btn-success:hover {
    background-color: #218838;
}
/* If you want success button to also use primary theme: */
/* .btn.btn-success.btn-primary-themed { background-color: var(--primary-color); } */
/* .btn.btn-success.btn-primary-themed:hover { background-color: #b34d1e; } */


.btn-secondary {
    background-color: #484848; /* Darker secondary button */
}
.btn-secondary:hover {
    background-color: #3a3a3a;
}

.btn-danger {
    background-color: #dc3545;
}
.btn-danger:hover {
    background-color: #c82333;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
}

@media (min-width: 480px) {
    .action-buttons {
        flex-direction: row;
        justify-content: space-around;
    }
    .btn {
        width: auto;
        min-width: 150px;
    }
}

footer {
    text-align: center;
    margin-top: 30px;
    font-size: 0.9em;
    color: var(--secondary-text-color-on-dark);
}