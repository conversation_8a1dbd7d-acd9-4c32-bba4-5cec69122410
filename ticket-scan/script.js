$(document).ready(function() {
    // --- Configuration ---
    const WORDPRESS_API_URL_BASE = 'https://horizons2025.vinuni.edu.vn/wp-json/ticket/v1';
    const API_INFO_ENDPOINT = `${WORDPRESS_API_URL_BASE}/check-in-info`;
    const API_CHECKIN_ENDPOINT = `${WORDPRESS_API_URL_BASE}/check-in`;
    // ** UPDATED SESSIONS_JSON_PATH **
    const SESSIONS_JSON_PATH = 'https://horizons2025.vinuni.edu.vn/ticket-scan/session.json';

    // --- DOM Elements ---
    const $scannerSection = $('#scanner-section');
    const $qrReaderDiv = $('#qr-reader');
    const $qrReaderStatus = $('#qr-reader-status');
    const $serverApiMessage = $('#server-api-message');
    const $startScanBtn = $('#start-scan-btn');
    const $stopScanBtn = $('#stop-scan-btn');

    const $confirmationSection = $('#confirmation-section');
    const $customerOrderId = $('#customer-order-id');
    const $customerName = $('#customer-name');
    const $customerAddress = $('#customer-address');
    const $customerEmail = $('#customer-email');
    const $customerPhone = $('#customer-phone');
    const $customerCountry = $('#customer-country');
    const $sessionDropdown = $('#session-dropdown');
    const $sessionErrorMessage = $('#session-error-message');
    const $scannedQrDataInput = $('#scanned-qr-data');
    const $parsedOrderIdInput = $('#parsed-order-id');

    const $confirmCheckinBtn = $('#confirm-checkin-btn');
    const $cancelCheckinBtn = $('#cancel-checkin-btn');

    const $resultSection = $('#result-section');
    const $resultSectionTitle = $('#result-section-title');
    const $resultMessage = $('#result-message');
    const $scanAnotherBtn = $('#scan-another-btn');

    let html5QrCode = null;
    let conferenceSessions = [];

    function loadSessions() {
        $.getJSON(SESSIONS_JSON_PATH)
            .done(function(data) {
                conferenceSessions = data;
                populateSessionDropdown(conferenceSessions);
                console.log("Sessions loaded successfully from:", SESSIONS_JSON_PATH);
            })
            .fail(function(jqxhr, textStatus, error) {
                const err = textStatus + ", " + error;
                console.error("Error loading " + SESSIONS_JSON_PATH + ": " + err);
                showStatus("Error: Could not load session data. Please check the session data URL or file.", 'error', $serverApiMessage);
                $sessionDropdown.prop('disabled', true).html('<option value="">Error loading sessions</option>');
                // $confirmCheckinBtn.prop('disabled', true); // Keep enabled to show session selection error
            });
    }

    function populateSessionDropdown(sessions) {
        $sessionDropdown.empty();
        $sessionDropdown.append($('<option>', { value: '', text: '-- Please select a session --' }));
        if (sessions && sessions.length > 0) {
            $.each(sessions, function(index, session) {
                $sessionDropdown.append($('<option>', {
                    value: session.id,
                    text: session.session_name
                }));
            });
            $sessionDropdown.prop('disabled', false);
        } else {
             $sessionDropdown.append($('<option>', { value: '', text: 'No sessions available' }));
             $sessionDropdown.prop('disabled', true);
        }
    }

    loadSessions();

    function showStatus(message, type = 'info', targetContainer = $qrReaderStatus) {
        const $messageDiv = $('<div></div>')
            .addClass('status-message-item ' + type)
            .html(message);
        targetContainer.append($messageDiv);
        targetContainer.show();
        if (targetContainer.attr('id') === 'result-message') {
            $resultSection.show();
            // $resultSectionTitle.show();
        }
    }

    function onScanSuccess(decodedText, decodedResult) {
        console.log(`QR Code Scanned: ${decodedText}`);
        $scannedQrDataInput.val(decodedText);
        $serverApiMessage.html('').hide();
        $qrReaderStatus.html(''); // Clear previous general scan messages for this new scan attempt
        const parts = decodedText.split('-');
        if (parts.length < 2) {
            showStatus('Invalid QR code format. Expected "customer_id - order_id".', 'error', $qrReaderStatus);
            stopScanning(); return;
        }
        const orderId = parts.pop().trim();
        if (!orderId || isNaN(parseInt(orderId))) {
            showStatus('Invalid Order ID in QR code.', 'error', $qrReaderStatus);
            stopScanning(); return;
        }
        $parsedOrderIdInput.val(orderId);
        showStatus('QR Code detected! Fetching order information...', 'info', $qrReaderStatus);
        stopScanning();
        fetchOrderInformation(orderId);
    }

    function onScanFailure(error) { /* console.warn(`QR Scan Error: ${error}`); */ }

    function startScanning() {
        $qrReaderStatus.html('');
        $serverApiMessage.html('').hide();
        $sessionErrorMessage.hide().text('');
        if (!html5QrCode) {
            try { html5QrCode = new Html5Qrcode("qr-reader"); }
            catch (e) { showStatus('Error initializing QR Scanner.', 'error', $qrReaderStatus); return; }
        }
        const qrConfig = { fps: 10, qrbox: (w, h) => ({ width: Math.floor(Math.min(w,h)*0.7), height: Math.floor(Math.min(w,h)*0.7) }), rememberLastUsedCamera: true, supportedScanTypes: [Html5QrcodeScanType.SCAN_TYPE_CAMERA, Html5QrcodeScanType.SCAN_TYPE_FILE] };
        showStatus('Initializing scanner...', 'info', $qrReaderStatus);
        $startScanBtn.hide(); $stopScanBtn.show(); $qrReaderDiv.show();
        html5QrCode.start({ facingMode: "environment" }, qrConfig, onScanSuccess, onScanFailure)
            .catch(err => {
                showStatus(`Error starting scanner: ${err.message || err}.`, 'error', $qrReaderStatus);
                $startScanBtn.show(); $stopScanBtn.hide();
            });
    }

    function stopScanning() {
        if (html5QrCode && html5QrCode.isScanning) {
            html5QrCode.stop().then(() => {
                showStatus('Scanner stopped.', 'info', $qrReaderStatus);
                $stopScanBtn.hide(); $startScanBtn.show();
            }).catch(err => showStatus('Failed to stop scanner.', 'error', $qrReaderStatus));
        } else { $stopScanBtn.hide(); $startScanBtn.show(); }
    }

    $startScanBtn.on('click', startScanning);
    $stopScanBtn.on('click', stopScanning);

    function getErrorMessageFromJqXHR(jqXHR, textStatus, errorThrown) {
        let serverMessage = null;
        if (jqXHR.responseJSON && jqXHR.responseJSON.message) {
            serverMessage = jqXHR.responseJSON.message;
        } else if (jqXHR.responseText) {
            try {
                const parsedResponse = JSON.parse(jqXHR.responseText);
                if (parsedResponse && parsedResponse.message) { serverMessage = parsedResponse.message; }
            } catch (e) { /* Ignore */ }
        }
        return serverMessage || `Server error (Status: ${jqXHR.status || 'N/A'}${errorThrown ? ' - ' + errorThrown : ''}). Please try again or contact support.`;
    }

    function fetchOrderInformation(orderId) {
        showStatus('Fetching order data...', 'info', $qrReaderStatus);
        $serverApiMessage.html('').hide();
        $confirmCheckinBtn.prop('disabled', true);
        $.ajax({
            url: API_INFO_ENDPOINT, method: 'GET', data: { order_id: orderId }, dataType: 'json',
            success: function(response) {
                if (response && response.success === true && response.data) {
                    const data = response.data;
                    $customerOrderId.text(data.order_id || orderId);
                    $customerName.text(data.name || 'N/A');
                    $customerAddress.text(data.address || 'N/A');
                    $customerEmail.text(data.email || 'N/A');
                    $customerPhone.text(data.phone || 'N/A');
                    $customerCountry.text(data.country || 'N/A');
                    $scannerSection.hide(); $confirmationSection.show(); $resultSection.hide();
                    $qrReaderStatus.html('');
                    showStatus('Order details loaded. Please confirm session.', 'info', $qrReaderStatus);
                    $sessionDropdown.val('');
                    $sessionErrorMessage.hide().text('');
                } else {
                    showStatus(response.message || 'Could not retrieve valid order data.', 'error', $serverApiMessage);
                    showStatus('Failed to retrieve order details. Try again.', 'error', $qrReaderStatus);
                    resetToScanView(false);
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                const finalErrorMessage = getErrorMessageFromJqXHR(jqXHR, textStatus, errorThrown);
                showStatus(finalErrorMessage, 'error', $serverApiMessage);
                showStatus('Error fetching order details. Try again.', 'error', $qrReaderStatus);
                resetToScanView(false);
            },
            complete: function() { $confirmCheckinBtn.prop('disabled', false); }
        });
    }

    $confirmCheckinBtn.on('click', function() {
        const orderId = $parsedOrderIdInput.val();
        const selectedSessionId = $sessionDropdown.val();
        const selectedSessionName = $sessionDropdown.find('option:selected').text();
        $sessionErrorMessage.hide().text('');

        if (!selectedSessionId) {
            $sessionErrorMessage.text('Please select a session.').show();
            return;
        }
        if (!orderId) {
            $resultMessage.html('');
            showStatus('Error: Order ID is missing. Please scan again.', 'error', $resultMessage);
            $confirmationSection.hide(); $scanAnotherBtn.show(); return;
        }
        const checkInData = {
            order_id: orderId, name: $customerName.text(), address: $customerAddress.text(),
            email: $customerEmail.text(), phone: $customerPhone.text(), country: $customerCountry.text(),
            raw_qr_data: $scannedQrDataInput.val(), session_id: selectedSessionId, session_name: selectedSessionName
        };
        $resultMessage.html('');
        showStatus('Processing check-in...', 'info', $resultMessage);
        // $resultSection.show(); // showStatus for $resultMessage will handle this
        $confirmationSection.hide();
        $(this).prop('disabled', true); $cancelCheckinBtn.prop('disabled', true);

        $.ajax({
            url: API_CHECKIN_ENDPOINT, method: 'POST', contentType: 'application/json', data: JSON.stringify(checkInData), dataType: 'json',
            success: function(response) {
                if (response && response.success) {
                    showStatus(response.message || 'Checked-in successfully!', 'success', $resultMessage);
                } else {
                    showStatus(response.message || 'Check-in failed.', 'error', $resultMessage);
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                const finalErrorMessage = getErrorMessageFromJqXHR(jqXHR, textStatus, errorThrown);
                showStatus(finalErrorMessage, 'error', $resultMessage);
            },
            complete: function() {
                $scanAnotherBtn.show();
                $('#confirm-checkin-btn').prop('disabled', false);
                $('#cancel-checkin-btn').prop('disabled', false);
            }
        });
    });

    $cancelCheckinBtn.on('click', function() { resetToScanView(true); });
    $scanAnotherBtn.on('click', function() { resetToScanView(true); });

    function resetToScanView(fullReset = true) {
        $confirmationSection.hide();
        $resultSection.hide();
        $resultMessage.html('');
        $scanAnotherBtn.hide();
        $scannerSection.show();
        $startScanBtn.show(); $stopScanBtn.hide();
        $qrReaderStatus.html('');
        showStatus('Please scan a QR code.', 'info', $qrReaderStatus);
        $sessionErrorMessage.hide().text('');
        if (fullReset) {
            $serverApiMessage.html('').hide();
            $scannedQrDataInput.val(''); $parsedOrderIdInput.val('');
            $customerOrderId.text(''); $customerName.text(''); $customerAddress.text('');
            $customerEmail.text(''); $customerPhone.text(''); $customerCountry.text('');
            $sessionDropdown.val('');
        }
        $confirmCheckinBtn.prop('disabled', false); $cancelCheckinBtn.prop('disabled', false);
        if (html5QrCode && html5QrCode.isScanning) {
            // html5QrCode.stop().catch(err => console.error("Error stopping scanner during reset", err));
        }
    }
    resetToScanView(true);
});
