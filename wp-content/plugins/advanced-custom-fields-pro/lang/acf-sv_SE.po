# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-08-01T09:50:26+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: sv_SE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/api/api-template.php:1054
msgid "[The ACF shortcode cannot display fields from non-public posts]"
msgstr ""

#: includes/api/api-template.php:1011
msgid "[The ACF shortcode is disabled on this site]"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:451
msgid "Businessman Icon"
msgstr "Ikon för affärsman"

#: includes/fields/class-acf-field-icon_picker.php:443
msgid "Forums Icon"
msgstr "Forum-ikon"

#: includes/fields/class-acf-field-icon_picker.php:722
msgid "YouTube Icon"
msgstr "YouTube-ikon"

#: includes/fields/class-acf-field-icon_picker.php:721
msgid "Yes (alt) Icon"
msgstr "Alternativ ja-ikon"

#: includes/fields/class-acf-field-icon_picker.php:719
msgid "Xing Icon"
msgstr "Xing-ikon"

#: includes/fields/class-acf-field-icon_picker.php:718
msgid "WordPress (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:716
msgid "WhatsApp Icon"
msgstr "WhatsApp-ikon"

#: includes/fields/class-acf-field-icon_picker.php:715
msgid "Write Blog Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:714
msgid "Widgets Menus Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:713
msgid "View Site Icon"
msgstr "Visa webbplats-ikon"

#: includes/fields/class-acf-field-icon_picker.php:712
msgid "Learn More Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:710
msgid "Add Page Icon"
msgstr "Ikon för lägg till sida"

#: includes/fields/class-acf-field-icon_picker.php:707
msgid "Video (alt3) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:706
msgid "Video (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:705
msgid "Video (alt) Icon"
msgstr "Alternativ video-ikon"

#: includes/fields/class-acf-field-icon_picker.php:702
msgid "Update (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:699
msgid "Universal Access (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:696
msgid "Twitter (alt) Icon"
msgstr "Alternativ Twitter-ikon"

#: includes/fields/class-acf-field-icon_picker.php:694
msgid "Twitch Icon"
msgstr "Twitch-ikon"

#: includes/fields/class-acf-field-icon_picker.php:691
msgid "Tide Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:690
msgid "Tickets (alt) Icon"
msgstr "Alternativ biljettikon"

#: includes/fields/class-acf-field-icon_picker.php:686
msgid "Text Page Icon"
msgstr "Ikon för textsida"

#: includes/fields/class-acf-field-icon_picker.php:680
msgid "Table Row Delete Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:679
msgid "Table Row Before Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:678
msgid "Table Row After Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:677
msgid "Table Col Delete Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:676
msgid "Table Col Before Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:675
msgid "Table Col After Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:674
msgid "Superhero (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:673
msgid "Superhero Icon"
msgstr "Ikon för superhjälte"

#: includes/fields/class-acf-field-icon_picker.php:667
msgid "Spotify Icon"
msgstr "Spotify-ikon"

#: includes/fields/class-acf-field-icon_picker.php:661
msgid "Shortcode Icon"
msgstr "Ikon för kortkod"

#: includes/fields/class-acf-field-icon_picker.php:660
msgid "Shield (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:658
msgid "Share (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:657
msgid "Share (alt) Icon"
msgstr "Alternativ dela-ikon"

#: includes/fields/class-acf-field-icon_picker.php:652
msgid "Saved Icon"
msgstr "Ikon för sparat"

#: includes/fields/class-acf-field-icon_picker.php:651
msgid "RSS Icon"
msgstr "RSS-ikon"

#: includes/fields/class-acf-field-icon_picker.php:650
msgid "REST API Icon"
msgstr "REST API-ikon"

#: includes/fields/class-acf-field-icon_picker.php:649
msgid "Remove Icon"
msgstr "Ikon för att ta bort"

#: includes/fields/class-acf-field-icon_picker.php:647
msgid "Reddit Icon"
msgstr "Reddit-ikon"

#: includes/fields/class-acf-field-icon_picker.php:644
msgid "Privacy Icon"
msgstr "Integritetsikon"

#: includes/fields/class-acf-field-icon_picker.php:643
msgid "Printer Icon"
msgstr "Ikon för skrivare"

#: includes/fields/class-acf-field-icon_picker.php:639
msgid "Podio Icon"
msgstr "Podio-ikon"

#: includes/fields/class-acf-field-icon_picker.php:638
msgid "Plus (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:637
msgid "Plus (alt) Icon"
msgstr "Alternativ plus-ikon"

#: includes/fields/class-acf-field-icon_picker.php:635
msgid "Plugins Checked Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:632
msgid "Pinterest Icon"
msgstr "Pinterest-ikon"

#: includes/fields/class-acf-field-icon_picker.php:630
msgid "Pets Icon"
msgstr "Ikon för husdjur"

#: includes/fields/class-acf-field-icon_picker.php:628
msgid "PDF Icon"
msgstr "PDF-ikon"

#: includes/fields/class-acf-field-icon_picker.php:626
msgid "Palm Tree Icon"
msgstr "Ikon för palmträd"

#: includes/fields/class-acf-field-icon_picker.php:625
msgid "Open Folder Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:624
msgid "No (alt) Icon"
msgstr "Alternativ nej-ikon"

#: includes/fields/class-acf-field-icon_picker.php:619
msgid "Money (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:614
msgid "Menu (alt3) Icon"
msgstr "Alternativ menyikon 3"

#: includes/fields/class-acf-field-icon_picker.php:613
msgid "Menu (alt2) Icon"
msgstr "Alternativ menyikon 2"

#: includes/fields/class-acf-field-icon_picker.php:612
msgid "Menu (alt) Icon"
msgstr "Alternativ menyikon"

#: includes/fields/class-acf-field-icon_picker.php:607
msgid "Spreadsheet Icon"
msgstr "Ikon för kalkylark"

#: includes/fields/class-acf-field-icon_picker.php:606
msgid "Interactive Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:605
msgid "Document Icon"
msgstr "Dokumentikon"

#: includes/fields/class-acf-field-icon_picker.php:604
msgid "Default Icon"
msgstr "Standardikon"

#: includes/fields/class-acf-field-icon_picker.php:598
msgid "Location (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:595
msgid "LinkedIn Icon"
msgstr "LinkedIn-ikon"

#: includes/fields/class-acf-field-icon_picker.php:590
msgid "Instagram Icon"
msgstr "Instagram-ikon"

#: includes/fields/class-acf-field-icon_picker.php:589
msgid "Insert Before Icon"
msgstr "Ikon för infoga före"

#: includes/fields/class-acf-field-icon_picker.php:588
msgid "Insert After Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:587
msgid "Insert Icon"
msgstr "Infoga-ikon"

#: includes/fields/class-acf-field-icon_picker.php:586
msgid "Info Outline Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:583
msgid "Images (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:582
msgid "Images (alt) Icon"
msgstr "Alternativ inom för bilder"

#: includes/fields/class-acf-field-icon_picker.php:581
msgid "Rotate Right Icon"
msgstr "Ikon för rotera höger"

#: includes/fields/class-acf-field-icon_picker.php:580
msgid "Rotate Left Icon"
msgstr "Ikon för rotera vänster"

#: includes/fields/class-acf-field-icon_picker.php:579
msgid "Rotate Icon"
msgstr "Ikon för att rotera"

#: includes/fields/class-acf-field-icon_picker.php:578
msgid "Flip Vertical Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:577
msgid "Flip Horizontal Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:575
msgid "Crop Icon"
msgstr "Beskärningsikon"

#: includes/fields/class-acf-field-icon_picker.php:574
msgid "Id (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:572
msgid "HTML Icon"
msgstr "HTML-ikon"

#: includes/fields/class-acf-field-icon_picker.php:571
msgid "Hourglass Icon"
msgstr "Timglas-ikon"

#: includes/fields/class-acf-field-icon_picker.php:568
msgid "Heading Icon"
msgstr "Ikon för rubrik"

#: includes/fields/class-acf-field-icon_picker.php:564
msgid "Google Icon"
msgstr "Google-ikon"

#: includes/fields/class-acf-field-icon_picker.php:563
msgid "Games Icon"
msgstr "Spelikon"

#: includes/fields/class-acf-field-icon_picker.php:562
msgid "Fullscreen Exit (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:561
msgid "Fullscreen (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:558
msgid "Status Icon"
msgstr "Statusikon"

#: includes/fields/class-acf-field-icon_picker.php:556
msgid "Image Icon"
msgstr "Bildikon"

#: includes/fields/class-acf-field-icon_picker.php:555
msgid "Gallery Icon"
msgstr "Ikon för galleri"

#: includes/fields/class-acf-field-icon_picker.php:554
msgid "Chat Icon"
msgstr "Chatt-ikon"

#: includes/fields/class-acf-field-icon_picker.php:553
#: includes/fields/class-acf-field-icon_picker.php:602
msgid "Audio Icon"
msgstr "Ljudikon"

#: includes/fields/class-acf-field-icon_picker.php:552
msgid "Aside Icon"
msgstr "Notisikon"

#: includes/fields/class-acf-field-icon_picker.php:551
msgid "Food Icon"
msgstr "Matikon"

#: includes/fields/class-acf-field-icon_picker.php:544
msgid "Exit Icon"
msgstr "Avsluta-ikon"

#: includes/fields/class-acf-field-icon_picker.php:543
msgid "Excerpt View Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:542
msgid "Embed Video Icon"
msgstr "Ikon för inbäddning av videoklipp"

#: includes/fields/class-acf-field-icon_picker.php:541
msgid "Embed Post Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:540
msgid "Embed Photo Icon"
msgstr "Ikon för inbäddning av foto"

#: includes/fields/class-acf-field-icon_picker.php:539
msgid "Embed Generic Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:538
msgid "Embed Audio Icon"
msgstr "Ikon för inbäddning av ljud"

#: includes/fields/class-acf-field-icon_picker.php:537
msgid "Email (alt2) Icon"
msgstr "Alternativ e-post-ikon 2"

#: includes/fields/class-acf-field-icon_picker.php:534
msgid "Ellipsis Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:530
msgid "Unordered List Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:525
msgid "RTL Icon"
msgstr "RTL-ikon"

#: includes/fields/class-acf-field-icon_picker.php:518
msgid "Ordered List RTL Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:517
msgid "Ordered List Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:516
msgid "LTR Icon"
msgstr "LTR-ikon"

#: includes/fields/class-acf-field-icon_picker.php:508
msgid "Custom Character Icon"
msgstr "Ikon för anpassat tecken"

#: includes/fields/class-acf-field-icon_picker.php:500
msgid "Edit Page Icon"
msgstr "Ikon för redigera sida"

#: includes/fields/class-acf-field-icon_picker.php:499
msgid "Edit Large Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:497
msgid "Drumstick Icon"
msgstr "Ikon för trumstock"

#: includes/fields/class-acf-field-icon_picker.php:493
msgid "Database View Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:492
msgid "Database Remove Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:491
msgid "Database Import Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:490
msgid "Database Export Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:489
msgid "Database Add Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:488
msgid "Database Icon"
msgstr "Databasikon"

#: includes/fields/class-acf-field-icon_picker.php:486
msgid "Cover Image Icon"
msgstr "Ikon för omslagsbild"

#: includes/fields/class-acf-field-icon_picker.php:485
msgid "Volume On Icon"
msgstr "Ikon för volym på"

#: includes/fields/class-acf-field-icon_picker.php:484
msgid "Volume Off Icon"
msgstr "Ikon för volymavstängning"

#: includes/fields/class-acf-field-icon_picker.php:483
msgid "Skip Forward Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:482
msgid "Skip Back Icon"
msgstr "Ikon för att hoppa tillbaka"

#: includes/fields/class-acf-field-icon_picker.php:481
msgid "Repeat Icon"
msgstr "Ikon för upprepning"

#: includes/fields/class-acf-field-icon_picker.php:480
msgid "Play Icon"
msgstr "Ikon för uppspelning"

#: includes/fields/class-acf-field-icon_picker.php:479
msgid "Pause Icon"
msgstr "Paus-ikon"

#: includes/fields/class-acf-field-icon_picker.php:478
msgid "Forward Icon"
msgstr "Framåt-ikon"

#: includes/fields/class-acf-field-icon_picker.php:477
msgid "Back Icon"
msgstr "Ikon för tillbaka"

#: includes/fields/class-acf-field-icon_picker.php:476
msgid "Columns Icon"
msgstr "Ikon för kolumner"

#: includes/fields/class-acf-field-icon_picker.php:475
msgid "Color Picker Icon"
msgstr "Ikon för färgväljare"

#: includes/fields/class-acf-field-icon_picker.php:474
msgid "Coffee Icon"
msgstr "Kaffeikon"

#: includes/fields/class-acf-field-icon_picker.php:473
msgid "Code Standards Icon"
msgstr "Ikon för kodstandarder"

#: includes/fields/class-acf-field-icon_picker.php:472
msgid "Cloud Upload Icon"
msgstr "Ikon för molnuppladdning"

#: includes/fields/class-acf-field-icon_picker.php:471
msgid "Cloud Saved Icon"
msgstr "Ikon för sparat i moln"

#: includes/fields/class-acf-field-icon_picker.php:460
msgid "Car Icon"
msgstr "Bilikon"

#: includes/fields/class-acf-field-icon_picker.php:459
msgid "Camera (alt) Icon"
msgstr "Alternativ kameraikon"

#: includes/fields/class-acf-field-icon_picker.php:455
msgid "Calculator Icon"
msgstr "Ikon för kalkylator"

#: includes/fields/class-acf-field-icon_picker.php:454
msgid "Button Icon"
msgstr "Knappikon"

#: includes/fields/class-acf-field-icon_picker.php:452
msgid "Businessperson Icon"
msgstr "Ikon för affärsperson"

#: includes/fields/class-acf-field-icon_picker.php:449
msgid "Tracking Icon"
msgstr "Spårningsikon"

#: includes/fields/class-acf-field-icon_picker.php:448
msgid "Topics Icon"
msgstr "Ämnesikon"

#: includes/fields/class-acf-field-icon_picker.php:447
msgid "Replies Icon"
msgstr "Ikon för svar"

#: includes/fields/class-acf-field-icon_picker.php:446
msgid "Pm Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:444
msgid "Friends Icon"
msgstr "Vännerikon"

#: includes/fields/class-acf-field-icon_picker.php:442
msgid "Community Icon"
msgstr "Community-ikon"

#: includes/fields/class-acf-field-icon_picker.php:441
msgid "BuddyPress Icon"
msgstr "BuddyPress-ikon"

#: includes/fields/class-acf-field-icon_picker.php:440
msgid "BbPress Icon"
msgstr "BbPress-ikon"

#: includes/fields/class-acf-field-icon_picker.php:439
msgid "Activity Icon"
msgstr "Aktivitetsikon"

#: includes/fields/class-acf-field-icon_picker.php:438
msgid "Book (alt) Icon"
msgstr "Alternativ bok-ikon"

#: includes/fields/class-acf-field-icon_picker.php:436
msgid "Block Default Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:435
msgid "Bell Icon"
msgstr "Ikon för klocka"

#: includes/fields/class-acf-field-icon_picker.php:434
msgid "Beer Icon"
msgstr "Ikon för öl"

#: includes/fields/class-acf-field-icon_picker.php:433
msgid "Bank Icon"
msgstr "Bankikon"

#: includes/fields/class-acf-field-icon_picker.php:429
msgid "Arrow Up (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:428
msgid "Arrow Up (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:426
msgid "Arrow Right (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:425
msgid "Arrow Right (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:423
msgid "Arrow Left (alt2) Icon"
msgstr "Alternativ ”pil vänster”-ikon 2"

#: includes/fields/class-acf-field-icon_picker.php:422
msgid "Arrow Left (alt) Icon"
msgstr "Alternativ ”pil vänster”-ikon"

#: includes/fields/class-acf-field-icon_picker.php:420
msgid "Arrow Down (alt2) Icon"
msgstr "Alternativ ”pil ned”-ikon 2"

#: includes/fields/class-acf-field-icon_picker.php:419
msgid "Arrow Down (alt) Icon"
msgstr "Alternativ ”pil ned”-ikon"

#: includes/fields/class-acf-field-icon_picker.php:415
msgid "Amazon Icon"
msgstr "Amazon-ikon"

#: includes/fields/class-acf-field-icon_picker.php:414
msgid "Align Wide Icon"
msgstr "Ikon för bred justering"

#: includes/fields/class-acf-field-icon_picker.php:412
msgid "Align Pull Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:411
msgid "Align Pull Left Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:408
msgid "Align Full Width Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:405
msgid "Airplane Icon"
msgstr "Flygplansikon"

#: includes/fields/class-acf-field-icon_picker.php:402
msgid "Site (alt3) Icon"
msgstr "Alternativ webbplatsikon 3"

#: includes/fields/class-acf-field-icon_picker.php:401
msgid "Site (alt2) Icon"
msgstr "Alternativ webbplatsikon 2"

#: includes/fields/class-acf-field-icon_picker.php:400
msgid "Site (alt) Icon"
msgstr "Alternativ webbplatsikon"

#: includes/admin/views/options-page-preview.php:26
msgid "Upgrade to ACF PRO to create options pages in just a few clicks"
msgstr ""
"Uppgradera till ACF PRO för att skapa alternativsidor med bara några få klick"

#: includes/ajax/class-acf-ajax-upgrade.php:24
msgid "Sorry, you don't have permission to do that."
msgstr "Du har inte behörighet att göra det."

#: includes/ajax/class-acf-ajax-query-users.php:24
msgid "Invalid request args."
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:37
msgid "Sorry, you are not allowed to do that."
msgstr "Du har inte behörighet att göra det."

#: includes/ajax/class-acf-ajax-check-screen.php:37
#: includes/ajax/class-acf-ajax-query-users.php:32
#: includes/ajax/class-acf-ajax-user-setting.php:38
msgid "Sorry, you do not have permission to do that."
msgstr "Du har inte behörighet att göra det."

#: includes/class-acf-site-health.php:643
msgid "Blocks Using Post Meta"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:25
#: includes/admin/views/acf-field-group/pro-features.php:27
#: includes/admin/views/global/header.php:27
msgid "ACF PRO logo"
msgstr "ACF PRO-logga"

#: includes/admin/views/acf-field-group/field.php:37
msgid "ACF PRO Logo"
msgstr "ACF PRO-logga"

#. translators: %s - field/param name
#: includes/fields/class-acf-field-icon_picker.php:788
msgid "%s requires a valid attachment ID when type is set to media_library."
msgstr ""
"%s kräver ett giltigt bilage-ID när typen är angiven till media_library."

#. translators: %s - field name
#: includes/fields/class-acf-field-icon_picker.php:772
msgid "%s is a required property of acf."
msgstr "%s är en obligatorisk egenskap för acf."

#: includes/fields/class-acf-field-icon_picker.php:748
msgid "The value of icon to save."
msgstr "Värdet på ikonen som ska sparas."

#: includes/fields/class-acf-field-icon_picker.php:742
msgid "The type of icon to save."
msgstr "Den typ av ikon som ska sparas."

#: includes/fields/class-acf-field-icon_picker.php:720
msgid "Yes Icon"
msgstr "Ja-ikon"

#: includes/fields/class-acf-field-icon_picker.php:717
msgid "WordPress Icon"
msgstr "WordPress-ikon"

#: includes/fields/class-acf-field-icon_picker.php:709
msgid "Warning Icon"
msgstr "Varningsikon"

#: includes/fields/class-acf-field-icon_picker.php:708
msgid "Visibility Icon"
msgstr "Ikon för synlighet"

#: includes/fields/class-acf-field-icon_picker.php:704
msgid "Vault Icon"
msgstr "Ikon för valv"

#: includes/fields/class-acf-field-icon_picker.php:703
msgid "Upload Icon"
msgstr "Ladda upp-ikon"

#: includes/fields/class-acf-field-icon_picker.php:701
msgid "Update Icon"
msgstr "Uppdatera-ikon"

#: includes/fields/class-acf-field-icon_picker.php:700
msgid "Unlock Icon"
msgstr "Lås upp-ikon"

#: includes/fields/class-acf-field-icon_picker.php:698
msgid "Universal Access Icon"
msgstr "Ikon för universell åtkomst"

#: includes/fields/class-acf-field-icon_picker.php:697
msgid "Undo Icon"
msgstr "Ångra-ikon"

#: includes/fields/class-acf-field-icon_picker.php:695
msgid "Twitter Icon"
msgstr "Twitter-ikon"

#: includes/fields/class-acf-field-icon_picker.php:693
msgid "Trash Icon"
msgstr "Papperskorgsikon"

#: includes/fields/class-acf-field-icon_picker.php:692
msgid "Translation Icon"
msgstr "Översättningsikon"

#: includes/fields/class-acf-field-icon_picker.php:689
msgid "Tickets Icon"
msgstr "Biljettikon"

#: includes/fields/class-acf-field-icon_picker.php:688
msgid "Thumbs Up Icon"
msgstr "Tummen upp-ikon"

#: includes/fields/class-acf-field-icon_picker.php:687
msgid "Thumbs Down Icon"
msgstr "Tummen ner-ikon"

#: includes/fields/class-acf-field-icon_picker.php:608
#: includes/fields/class-acf-field-icon_picker.php:685
msgid "Text Icon"
msgstr "Textikon"

#: includes/fields/class-acf-field-icon_picker.php:684
msgid "Testimonial Icon"
msgstr "Omdömesikon"

#: includes/fields/class-acf-field-icon_picker.php:683
msgid "Tagcloud Icon"
msgstr "Ikon för etikettmoln"

#: includes/fields/class-acf-field-icon_picker.php:682
msgid "Tag Icon"
msgstr "Etikett-ikon"

#: includes/fields/class-acf-field-icon_picker.php:681
msgid "Tablet Icon"
msgstr "Ikon för surfplatta"

#: includes/fields/class-acf-field-icon_picker.php:672
msgid "Store Icon"
msgstr "Butiksikon"

#: includes/fields/class-acf-field-icon_picker.php:671
msgid "Sticky Icon"
msgstr "Klistra-ikon"

#: includes/fields/class-acf-field-icon_picker.php:670
msgid "Star Half Icon"
msgstr "Halvfylld stjärnikon"

#: includes/fields/class-acf-field-icon_picker.php:669
msgid "Star Filled Icon"
msgstr "Fylld stjärnikon"

#: includes/fields/class-acf-field-icon_picker.php:668
msgid "Star Empty Icon"
msgstr "Tom stjärnikon"

#: includes/fields/class-acf-field-icon_picker.php:666
msgid "Sos Icon"
msgstr "SOS-ikon"

#: includes/fields/class-acf-field-icon_picker.php:665
msgid "Sort Icon"
msgstr "Sortera-ikon"

#: includes/fields/class-acf-field-icon_picker.php:664
msgid "Smiley Icon"
msgstr "Smiley-ikon"

#: includes/fields/class-acf-field-icon_picker.php:663
msgid "Smartphone Icon"
msgstr "Ikon för smarta telefoner"

#: includes/fields/class-acf-field-icon_picker.php:662
msgid "Slides Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:659
msgid "Shield Icon"
msgstr "Sköld-ikon"

#: includes/fields/class-acf-field-icon_picker.php:656
msgid "Share Icon"
msgstr "Delningsikon"

#: includes/fields/class-acf-field-icon_picker.php:655
msgid "Search Icon"
msgstr "Sökikon"

#: includes/fields/class-acf-field-icon_picker.php:654
msgid "Screen Options Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:653
msgid "Schedule Icon"
msgstr "Schema-ikon"

#: includes/fields/class-acf-field-icon_picker.php:648
msgid "Redo Icon"
msgstr "”Göra om”-ikon"

#: includes/fields/class-acf-field-icon_picker.php:646
msgid "Randomize Icon"
msgstr "Ikon för slumpmässighet"

#: includes/fields/class-acf-field-icon_picker.php:645
msgid "Products Icon"
msgstr "Ikon för produkter"

#: includes/fields/class-acf-field-icon_picker.php:642
msgid "Pressthis Icon"
msgstr "Pressthis-ikon"

#: includes/fields/class-acf-field-icon_picker.php:641
msgid "Post Status Icon"
msgstr "Ikon för inläggsstatus"

#: includes/fields/class-acf-field-icon_picker.php:640
msgid "Portfolio Icon"
msgstr "Portfölj-ikon"

#: includes/fields/class-acf-field-icon_picker.php:636
msgid "Plus Icon"
msgstr "Plus-ikon"

#: includes/fields/class-acf-field-icon_picker.php:634
msgid "Playlist Video Icon"
msgstr "Ikon för spellista, för videoklipp"

#: includes/fields/class-acf-field-icon_picker.php:633
msgid "Playlist Audio Icon"
msgstr "Ikon för spellista, för ljud"

#: includes/fields/class-acf-field-icon_picker.php:631
msgid "Phone Icon"
msgstr "Telefonikon"

#: includes/fields/class-acf-field-icon_picker.php:629
msgid "Performance Icon"
msgstr "Ikon för prestanda"

#: includes/fields/class-acf-field-icon_picker.php:627
msgid "Paperclip Icon"
msgstr "Pappersgem-ikon"

#: includes/fields/class-acf-field-icon_picker.php:623
msgid "No Icon"
msgstr "Ingen ikon"

#: includes/fields/class-acf-field-icon_picker.php:622
msgid "Networking Icon"
msgstr "Nätverksikon"

#: includes/fields/class-acf-field-icon_picker.php:621
msgid "Nametag Icon"
msgstr "Namnskylt-ikon"

#: includes/fields/class-acf-field-icon_picker.php:620
msgid "Move Icon"
msgstr "Flytta-ikon"

#: includes/fields/class-acf-field-icon_picker.php:618
msgid "Money Icon"
msgstr "Pengar-ikon"

#: includes/fields/class-acf-field-icon_picker.php:617
msgid "Minus Icon"
msgstr "Minus-ikon"

#: includes/fields/class-acf-field-icon_picker.php:616
msgid "Migrate Icon"
msgstr "Migrera-ikon"

#: includes/fields/class-acf-field-icon_picker.php:615
msgid "Microphone Icon"
msgstr "Mikrofon-ikon"

#: includes/fields/class-acf-field-icon_picker.php:610
msgid "Megaphone Icon"
msgstr "Megafon-ikon"

#: includes/fields/class-acf-field-icon_picker.php:600
msgid "Marker Icon"
msgstr "Markörikon"

#: includes/fields/class-acf-field-icon_picker.php:599
msgid "Lock Icon"
msgstr "Låsikon"

#: includes/fields/class-acf-field-icon_picker.php:597
msgid "Location Icon"
msgstr "Plats-ikon"

#: includes/fields/class-acf-field-icon_picker.php:596
msgid "List View Icon"
msgstr "Ikon för listvy"

#: includes/fields/class-acf-field-icon_picker.php:594
msgid "Lightbulb Icon"
msgstr "Glödlampeikon"

#: includes/fields/class-acf-field-icon_picker.php:593
msgid "Left Right Icon"
msgstr "Vänster-höger-ikon"

#: includes/fields/class-acf-field-icon_picker.php:592
msgid "Layout Icon"
msgstr "Layout-ikon"

#: includes/fields/class-acf-field-icon_picker.php:591
msgid "Laptop Icon"
msgstr "Ikon för bärbar dator"

#: includes/fields/class-acf-field-icon_picker.php:585
msgid "Info Icon"
msgstr "Info-ikon"

#: includes/fields/class-acf-field-icon_picker.php:584
msgid "Index Card Icon"
msgstr "Ikon för indexkort"

#: includes/fields/class-acf-field-icon_picker.php:573
msgid "Id Icon"
msgstr "ID-ikon"

#: includes/fields/class-acf-field-icon_picker.php:570
msgid "Hidden Icon"
msgstr "Döljikon"

#: includes/fields/class-acf-field-icon_picker.php:569
msgid "Heart Icon"
msgstr "Hjärt-ikon"

#: includes/fields/class-acf-field-icon_picker.php:567
msgid "Hammer Icon"
msgstr "Hammarikon"

#: includes/fields/class-acf-field-icon_picker.php:445
#: includes/fields/class-acf-field-icon_picker.php:566
msgid "Groups Icon"
msgstr "Gruppikon"

#: includes/fields/class-acf-field-icon_picker.php:565
msgid "Grid View Icon"
msgstr "Ikon för rutnätsvy"

#: includes/fields/class-acf-field-icon_picker.php:560
msgid "Forms Icon"
msgstr "Ikon för formulär"

#: includes/fields/class-acf-field-icon_picker.php:550
msgid "Flag Icon"
msgstr "Flagg-ikon"

#: includes/fields/class-acf-field-icon_picker.php:549
#: includes/fields/class-acf-field-icon_picker.php:576
msgid "Filter Icon"
msgstr "Filterikon"

#: includes/fields/class-acf-field-icon_picker.php:548
msgid "Feedback Icon"
msgstr "Ikon för återkoppling"

#: includes/fields/class-acf-field-icon_picker.php:547
msgid "Facebook (alt) Icon"
msgstr "Alternativ ikon för Facebook"

#: includes/fields/class-acf-field-icon_picker.php:546
msgid "Facebook Icon"
msgstr "Facebook-ikon"

#: includes/fields/class-acf-field-icon_picker.php:545
msgid "External Icon"
msgstr "Extern ikon"

#: includes/fields/class-acf-field-icon_picker.php:536
msgid "Email (alt) Icon"
msgstr "Alternativ e-post-ikon"

#: includes/fields/class-acf-field-icon_picker.php:535
msgid "Email Icon"
msgstr "E-postikon"

#: includes/fields/class-acf-field-icon_picker.php:533
#: includes/fields/class-acf-field-icon_picker.php:559
#: includes/fields/class-acf-field-icon_picker.php:609
msgid "Video Icon"
msgstr "Videoikon"

#: includes/fields/class-acf-field-icon_picker.php:532
msgid "Unlink Icon"
msgstr "Ta bort länk-ikon"

#: includes/fields/class-acf-field-icon_picker.php:531
msgid "Underline Icon"
msgstr "Understrykningsikon"

#: includes/fields/class-acf-field-icon_picker.php:529
msgid "Text Color Icon"
msgstr "Ikon för textfärg"

#: includes/fields/class-acf-field-icon_picker.php:528
msgid "Table Icon"
msgstr "Tabellikon"

#: includes/fields/class-acf-field-icon_picker.php:527
msgid "Strikethrough Icon"
msgstr "Ikon för genomstrykning"

#: includes/fields/class-acf-field-icon_picker.php:526
msgid "Spellcheck Icon"
msgstr "Ikon för stavningskontroll"

#: includes/fields/class-acf-field-icon_picker.php:524
msgid "Remove Formatting Icon"
msgstr "Ikon för ta bort formatering"

#: includes/fields/class-acf-field-icon_picker.php:523
#: includes/fields/class-acf-field-icon_picker.php:557
msgid "Quote Icon"
msgstr "Citatikon"

#: includes/fields/class-acf-field-icon_picker.php:522
msgid "Paste Word Icon"
msgstr "Ikon för att klistra in ord"

#: includes/fields/class-acf-field-icon_picker.php:521
msgid "Paste Text Icon"
msgstr "Ikon för att klistra in text"

#: includes/fields/class-acf-field-icon_picker.php:520
msgid "Paragraph Icon"
msgstr "Ikon för textstycke"

#: includes/fields/class-acf-field-icon_picker.php:519
msgid "Outdent Icon"
msgstr "Ikon för minskat indrag"

#: includes/fields/class-acf-field-icon_picker.php:515
msgid "Kitchen Sink Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:514
msgid "Justify Icon"
msgstr "Justera-ikon"

#: includes/fields/class-acf-field-icon_picker.php:513
msgid "Italic Icon"
msgstr "Kursiv-ikon"

#: includes/fields/class-acf-field-icon_picker.php:512
msgid "Insert More Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:511
msgid "Indent Icon"
msgstr "Ikon för indrag"

#: includes/fields/class-acf-field-icon_picker.php:510
msgid "Help Icon"
msgstr "Hjälpikon"

#: includes/fields/class-acf-field-icon_picker.php:509
msgid "Expand Icon"
msgstr "Expandera-ikon"

#: includes/fields/class-acf-field-icon_picker.php:507
msgid "Contract Icon"
msgstr "Avtalsikon"

#: includes/fields/class-acf-field-icon_picker.php:506
#: includes/fields/class-acf-field-icon_picker.php:603
msgid "Code Icon"
msgstr "Kodikon"

#: includes/fields/class-acf-field-icon_picker.php:505
msgid "Break Icon"
msgstr "Bryt-ikon"

#: includes/fields/class-acf-field-icon_picker.php:504
msgid "Bold Icon"
msgstr "Fet-ikon"

#: includes/fields/class-acf-field-icon_picker.php:498
msgid "Edit Icon"
msgstr "Redigera-ikon"

#: includes/fields/class-acf-field-icon_picker.php:496
msgid "Download Icon"
msgstr "Ladda ner-ikon"

#: includes/fields/class-acf-field-icon_picker.php:495
msgid "Dismiss Icon"
msgstr "Avfärda-ikon"

#: includes/fields/class-acf-field-icon_picker.php:494
msgid "Desktop Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:487
msgid "Dashboard Icon"
msgstr "Adminpanel-ikon"

#: includes/fields/class-acf-field-icon_picker.php:470
msgid "Cloud Icon"
msgstr "Molnikon"

#: includes/fields/class-acf-field-icon_picker.php:469
msgid "Clock Icon"
msgstr "Klockikon"

#: includes/fields/class-acf-field-icon_picker.php:468
msgid "Clipboard Icon"
msgstr "Ikon för urklipp"

#: includes/fields/class-acf-field-icon_picker.php:467
msgid "Chart Pie Icon"
msgstr "Cirkeldiagram-ikon"

#: includes/fields/class-acf-field-icon_picker.php:466
msgid "Chart Line Icon"
msgstr "Ikon för diagramlinje"

#: includes/fields/class-acf-field-icon_picker.php:465
msgid "Chart Bar Icon"
msgstr "Ikon för diagramfält"

#: includes/fields/class-acf-field-icon_picker.php:464
msgid "Chart Area Icon"
msgstr "Ikon för diagramområde"

#: includes/fields/class-acf-field-icon_picker.php:463
msgid "Category Icon"
msgstr "Kategoriikon"

#: includes/fields/class-acf-field-icon_picker.php:462
msgid "Cart Icon"
msgstr "Varukorgsikon"

#: includes/fields/class-acf-field-icon_picker.php:461
msgid "Carrot Icon"
msgstr "Morotsikon"

#: includes/fields/class-acf-field-icon_picker.php:458
msgid "Camera Icon"
msgstr "Kamera-ikon"

#: includes/fields/class-acf-field-icon_picker.php:457
msgid "Calendar (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:456
msgid "Calendar Icon"
msgstr "Kalender-ikon"

#: includes/fields/class-acf-field-icon_picker.php:453
msgid "Businesswoman Icon"
msgstr "Ikon för affärskvinna"

#: includes/fields/class-acf-field-icon_picker.php:450
msgid "Building Icon"
msgstr "Byggnadsikon"

#: includes/fields/class-acf-field-icon_picker.php:437
msgid "Book Icon"
msgstr "Bok-ikon"

#: includes/fields/class-acf-field-icon_picker.php:432
msgid "Backup Icon"
msgstr "Ikon för säkerhetskopiering"

#: includes/fields/class-acf-field-icon_picker.php:431
msgid "Awards Icon"
msgstr "Ikon för utmärkelser"

#: includes/fields/class-acf-field-icon_picker.php:430
msgid "Art Icon"
msgstr "Konst-ikon"

#: includes/fields/class-acf-field-icon_picker.php:427
msgid "Arrow Up Icon"
msgstr "”Pil upp”-ikon"

#: includes/fields/class-acf-field-icon_picker.php:424
msgid "Arrow Right Icon"
msgstr "”Pil höger”-ikon"

#: includes/fields/class-acf-field-icon_picker.php:421
msgid "Arrow Left Icon"
msgstr "”Pil vänster”-ikon"

#: includes/fields/class-acf-field-icon_picker.php:418
msgid "Arrow Down Icon"
msgstr "”Pil ned”-ikon"

#: includes/fields/class-acf-field-icon_picker.php:417
#: includes/fields/class-acf-field-icon_picker.php:601
msgid "Archive Icon"
msgstr "Arkiv-ikon"

#: includes/fields/class-acf-field-icon_picker.php:416
msgid "Analytics Icon"
msgstr "Analysikon"

#: includes/fields/class-acf-field-icon_picker.php:413
#: includes/fields/class-acf-field-icon_picker.php:503
msgid "Align Right Icon"
msgstr "Justera höger-ikon"

#: includes/fields/class-acf-field-icon_picker.php:410
msgid "Align None Icon"
msgstr "Justera inte-ikon"

#: includes/fields/class-acf-field-icon_picker.php:409
#: includes/fields/class-acf-field-icon_picker.php:502
msgid "Align Left Icon"
msgstr "Justera vänster-ikon"

#: includes/fields/class-acf-field-icon_picker.php:407
#: includes/fields/class-acf-field-icon_picker.php:501
msgid "Align Center Icon"
msgstr "Centrera-ikon"

#: includes/fields/class-acf-field-icon_picker.php:406
msgid "Album Icon"
msgstr "Album-ikon"

#: includes/fields/class-acf-field-icon_picker.php:404
msgid "Users Icon"
msgstr "Ikon för användare"

#: includes/fields/class-acf-field-icon_picker.php:403
msgid "Tools Icon"
msgstr "Verktygsikon"

#: includes/fields/class-acf-field-icon_picker.php:399
msgid "Site Icon"
msgstr "Webbplatsikon"

#: includes/fields/class-acf-field-icon_picker.php:398
msgid "Settings Icon"
msgstr "Ikon för inställningar"

#: includes/fields/class-acf-field-icon_picker.php:397
msgid "Post Icon"
msgstr "Inläggsikon"

#: includes/fields/class-acf-field-icon_picker.php:396
msgid "Plugins Icon"
msgstr "Tilläggsikon"

#: includes/fields/class-acf-field-icon_picker.php:395
msgid "Page Icon"
msgstr "Sidikon"

#: includes/fields/class-acf-field-icon_picker.php:394
msgid "Network Icon"
msgstr "Nätverksikon"

#: includes/fields/class-acf-field-icon_picker.php:393
msgid "Multisite Icon"
msgstr "Multisite-ikon"

#: includes/fields/class-acf-field-icon_picker.php:392
msgid "Media Icon"
msgstr "Mediaikon"

#: includes/fields/class-acf-field-icon_picker.php:391
msgid "Links Icon"
msgstr "Länkikon"

#: includes/fields/class-acf-field-icon_picker.php:390
msgid "Home Icon"
msgstr "Hemikon"

#: includes/fields/class-acf-field-icon_picker.php:388
msgid "Customizer Icon"
msgstr "Ikon för anpassaren"

#: includes/fields/class-acf-field-icon_picker.php:387
#: includes/fields/class-acf-field-icon_picker.php:711
msgid "Comments Icon"
msgstr "Ikon för kommentarer"

#: includes/fields/class-acf-field-icon_picker.php:386
msgid "Collapse Icon"
msgstr "Minimera-ikon"

#: includes/fields/class-acf-field-icon_picker.php:385
msgid "Appearance Icon"
msgstr "Utseende-ikon"

#: includes/fields/class-acf-field-icon_picker.php:389
msgid "Generic Icon"
msgstr "Generisk ikon"

#: includes/fields/class-acf-field-icon_picker.php:321
msgid "Icon picker requires a value."
msgstr "Ikonväljaren kräver ett värde."

#: includes/fields/class-acf-field-icon_picker.php:316
msgid "Icon picker requires an icon type."
msgstr "Ikonväljaren kräver en ikontyp."

#: includes/fields/class-acf-field-icon_picker.php:285
msgid ""
"The available icons matching your search query have been updated in the icon "
"picker below."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:284
msgid "No results found for that search term"
msgstr "Inga resultat hittades för den söktermen"

#: includes/fields/class-acf-field-icon_picker.php:266
msgid "Array"
msgstr "Array"

#: includes/fields/class-acf-field-icon_picker.php:265
msgid "String"
msgstr "Sträng"

#. translators: %s - link to documentation
#: includes/fields/class-acf-field-icon_picker.php:253
msgid "Specify the return format for the icon. %s"
msgstr "Ange returformat för ikonen. %s"

#: includes/fields/class-acf-field-icon_picker.php:238
msgid "Select where content editors can choose the icon from."
msgstr "Välj var innehållsredaktörer kan välja ikonen från."

#: includes/fields/class-acf-field-icon_picker.php:211
msgid "The URL to the icon you'd like to use, or svg as Data URI"
msgstr "URL till ikonen du vill använda, eller SVG som data-URI"

#: includes/fields/class-acf-field-icon_picker.php:194
msgid "Browse Media Library"
msgstr "Bläddra i mediabiblioteket"

#: includes/fields/class-acf-field-icon_picker.php:185
msgid "The currently selected image preview"
msgstr "Förhandsgranskning av den aktuella valda bilden"

#: includes/fields/class-acf-field-icon_picker.php:176
msgid "Click to change the icon in the Media Library"
msgstr "Klicka för att ändra ikonen i mediabiblioteket"

#: includes/fields/class-acf-field-icon_picker.php:142
msgid "Search icons..."
msgstr "Sök ikoner …"

#: includes/fields/class-acf-field-icon_picker.php:53
msgid "Media Library"
msgstr "Mediabibliotek"

#: includes/fields/class-acf-field-icon_picker.php:49
msgid "Dashicons"
msgstr "Dashicons"

#: includes/fields/class-acf-field-icon_picker.php:26
msgid ""
"An interactive UI for selecting an icon. Select from Dashicons, the media "
"library, or a standalone URL input."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:23
msgid "Icon Picker"
msgstr "Ikonväljare"

#: includes/class-acf-site-health.php:704
msgid "JSON Load Paths"
msgstr "Sökvägar där JSON-filer laddas från"

#: includes/class-acf-site-health.php:698
msgid "JSON Save Paths"
msgstr "Sökvägar där JSON-filer sparas"

#: includes/class-acf-site-health.php:689
msgid "Registered ACF Forms"
msgstr "Registrerade ACF-formulär"

#: includes/class-acf-site-health.php:683
msgid "Shortcode Enabled"
msgstr "Kortkod aktiverad"

#: includes/class-acf-site-health.php:675
msgid "Field Settings Tabs Enabled"
msgstr "Flikar för fältinställningar aktiverat"

#: includes/class-acf-site-health.php:667
msgid "Field Type Modal Enabled"
msgstr "Modal för fälttyp aktiverat"

#: includes/class-acf-site-health.php:659
msgid "Admin UI Enabled"
msgstr ""

#: includes/class-acf-site-health.php:650
msgid "Block Preloading Enabled"
msgstr "Förladdning av block aktiverat"

#: includes/class-acf-site-health.php:638
msgid "Blocks Per ACF Block Version"
msgstr ""

#: includes/class-acf-site-health.php:633
msgid "Blocks Per API Version"
msgstr ""

#: includes/class-acf-site-health.php:606
msgid "Registered ACF Blocks"
msgstr "Registrerade ACF-block"

#: includes/class-acf-site-health.php:600
msgid "Light"
msgstr "Ljus"

#: includes/class-acf-site-health.php:600
msgid "Standard"
msgstr "Standard"

#: includes/class-acf-site-health.php:599
msgid "REST API Format"
msgstr "REST API-format"

#: includes/class-acf-site-health.php:591
msgid "Registered Options Pages (PHP)"
msgstr "Registrerade alternativsidor (PHP)"

#: includes/class-acf-site-health.php:577
msgid "Registered Options Pages (JSON)"
msgstr "Registrerade alternativsidor (JSON)"

#: includes/class-acf-site-health.php:572
msgid "Registered Options Pages (UI)"
msgstr "Registrerade alternativsidor (UI)"

#: includes/class-acf-site-health.php:542
msgid "Options Pages UI Enabled"
msgstr "Användargränssnitt för alternativsidor aktiverat"

#: includes/class-acf-site-health.php:534
msgid "Registered Taxonomies (JSON)"
msgstr "Registrerade taxonomier (JSON)"

#: includes/class-acf-site-health.php:522
msgid "Registered Taxonomies (UI)"
msgstr "Registrerade taxonomier (UI)"

#: includes/class-acf-site-health.php:510
msgid "Registered Post Types (JSON)"
msgstr "Registrerade inläggstyper (JSON)"

#: includes/class-acf-site-health.php:498
msgid "Registered Post Types (UI)"
msgstr "Registrerade inläggstyper (UI)"

#: includes/class-acf-site-health.php:485
msgid "Post Types and Taxonomies Enabled"
msgstr "Inläggstyper och taxonomier aktiverade"

#: includes/class-acf-site-health.php:478
msgid "Number of Third Party Fields by Field Type"
msgstr "Antal tredjepartsfält per fälttyp"

#: includes/class-acf-site-health.php:473
msgid "Number of Fields by Field Type"
msgstr "Antal fält per fälttyp"

#: includes/class-acf-site-health.php:440
msgid "Field Groups Enabled for GraphQL"
msgstr "Fältgrupper aktiverade för GraphQL"

#: includes/class-acf-site-health.php:427
msgid "Field Groups Enabled for REST API"
msgstr "Fältgrupper aktiverade för REST API"

#: includes/class-acf-site-health.php:415
msgid "Registered Field Groups (JSON)"
msgstr "Registrerade fältgrupper (JSON)"

#: includes/class-acf-site-health.php:403
msgid "Registered Field Groups (PHP)"
msgstr "Registrerade fältgrupper (PHP)"

#: includes/class-acf-site-health.php:391
msgid "Registered Field Groups (UI)"
msgstr "Registrerade fältgrupper (UI)"

#: includes/class-acf-site-health.php:379
msgid "Active Plugins"
msgstr "Aktiva tillägg"

#: includes/class-acf-site-health.php:353
msgid "Parent Theme"
msgstr "Huvudtema"

#: includes/class-acf-site-health.php:342
msgid "Active Theme"
msgstr "Aktivt tema"

#: includes/class-acf-site-health.php:333
msgid "Is Multisite"
msgstr "Är multisite"

#: includes/class-acf-site-health.php:328
msgid "MySQL Version"
msgstr "MySQL-version"

#: includes/class-acf-site-health.php:323
msgid "WordPress Version"
msgstr "WordPress-version"

#: includes/class-acf-site-health.php:316
msgid "Subscription Expiry Date"
msgstr "Prenumerationens utgångsdatum"

#: includes/class-acf-site-health.php:308
msgid "License Status"
msgstr "Licensstatus"

#: includes/class-acf-site-health.php:303
msgid "License Type"
msgstr "Licenstyp"

#: includes/class-acf-site-health.php:298
msgid "Licensed URL"
msgstr "Licensierad URL"

#: includes/class-acf-site-health.php:292
msgid "License Activated"
msgstr "Licens aktiverad"

#: includes/class-acf-site-health.php:286
msgid "Free"
msgstr "Gratis"

#: includes/class-acf-site-health.php:285
msgid "Plugin Type"
msgstr "Typ av tillägg"

#: includes/class-acf-site-health.php:280
msgid "Plugin Version"
msgstr "Tilläggets version"

#: includes/class-acf-site-health.php:251
msgid ""
"This section contains debug information about your ACF configuration which "
"can be useful to provide to support."
msgstr ""
"Denna sektion innehåller felsökningsinformation om din ACF-konfiguration som "
"kan vara användbar för support."

#: includes/assets.php:373 assets/build/js/acf-input.js:11312
#: assets/build/js/acf-input.js:12394
msgid "An ACF Block on this page requires attention before you can save."
msgstr "Ett ACF-block på denna sida kräver uppmärksamhet innan du kan spara."

#. translators: %s - The clear log button opening HTML tag. %s - The closing
#. HTML tag.
#: includes/admin/views/escaped-html-notice.php:63
msgid ""
"This data is logged as we detect values that have been changed during "
"output. %1$sClear log and dismiss%2$s after escaping the values in your "
"code. The notice will reappear if we detect changed values again."
msgstr ""

#: includes/admin/views/escaped-html-notice.php:25
msgid "Dismiss permanently"
msgstr "Avfärda permanent"

#: includes/admin/views/acf-field-group/field.php:220
msgid "Instructions for content editors. Shown when submitting data."
msgstr "Instruktioner för innehållsredaktörer. Visas när data skickas"

#: includes/admin/post-types/admin-field-group.php:143
#: assets/build/js/acf-input.js:1461 assets/build/js/acf-input.js:1559
msgid "Has no term selected"
msgstr "Har ingen term vald"

#: includes/admin/post-types/admin-field-group.php:142
#: assets/build/js/acf-input.js:1438 assets/build/js/acf-input.js:1535
msgid "Has any term selected"
msgstr "Har någon term vald"

#: includes/admin/post-types/admin-field-group.php:141
#: assets/build/js/acf-input.js:1413 assets/build/js/acf-input.js:1508
msgid "Terms do not contain"
msgstr "Termerna innehåller inte"

#: includes/admin/post-types/admin-field-group.php:140
#: assets/build/js/acf-input.js:1388 assets/build/js/acf-input.js:1482
msgid "Terms contain"
msgstr "Termerna innehåller"

#: includes/admin/post-types/admin-field-group.php:139
#: assets/build/js/acf-input.js:1369 assets/build/js/acf-input.js:1462
msgid "Term is not equal to"
msgstr "Termen är inte lika med"

#: includes/admin/post-types/admin-field-group.php:138
#: assets/build/js/acf-input.js:1350 assets/build/js/acf-input.js:1442
msgid "Term is equal to"
msgstr "Termen är lika med"

#: includes/admin/post-types/admin-field-group.php:137
#: assets/build/js/acf-input.js:1053 assets/build/js/acf-input.js:1117
msgid "Has no user selected"
msgstr "Har ingen användare vald"

#: includes/admin/post-types/admin-field-group.php:136
#: assets/build/js/acf-input.js:1030 assets/build/js/acf-input.js:1093
msgid "Has any user selected"
msgstr "Har någon användare vald"

#: includes/admin/post-types/admin-field-group.php:135
#: assets/build/js/acf-input.js:1004 assets/build/js/acf-input.js:1065
msgid "Users do not contain"
msgstr "Användarna innehåller inte"

#: includes/admin/post-types/admin-field-group.php:134
#: assets/build/js/acf-input.js:977 assets/build/js/acf-input.js:1036
msgid "Users contain"
msgstr "Användarna innehåller"

#: includes/admin/post-types/admin-field-group.php:133
#: assets/build/js/acf-input.js:958 assets/build/js/acf-input.js:1016
msgid "User is not equal to"
msgstr "Användare är inte lika med"

#: includes/admin/post-types/admin-field-group.php:132
#: assets/build/js/acf-input.js:939 assets/build/js/acf-input.js:996
msgid "User is equal to"
msgstr "Användare är lika med"

#: includes/admin/post-types/admin-field-group.php:131
#: assets/build/js/acf-input.js:916 assets/build/js/acf-input.js:972
msgid "Has no page selected"
msgstr "Har ingen sida vald"

#: includes/admin/post-types/admin-field-group.php:130
#: assets/build/js/acf-input.js:893 assets/build/js/acf-input.js:948
msgid "Has any page selected"
msgstr "Har någon sida vald"

#: includes/admin/post-types/admin-field-group.php:129
#: assets/build/js/acf-input.js:866 assets/build/js/acf-input.js:919
msgid "Pages do not contain"
msgstr "Sidorna innehåller inte"

#: includes/admin/post-types/admin-field-group.php:128
#: assets/build/js/acf-input.js:839 assets/build/js/acf-input.js:890
msgid "Pages contain"
msgstr "Sidorna innehåller"

#: includes/admin/post-types/admin-field-group.php:127
#: assets/build/js/acf-input.js:820 assets/build/js/acf-input.js:870
msgid "Page is not equal to"
msgstr "Sidan är inte lika med"

#: includes/admin/post-types/admin-field-group.php:126
#: assets/build/js/acf-input.js:801 assets/build/js/acf-input.js:850
msgid "Page is equal to"
msgstr "Sidan är lika med"

#: includes/admin/post-types/admin-field-group.php:125
#: assets/build/js/acf-input.js:1189 assets/build/js/acf-input.js:1260
msgid "Has no relationship selected"
msgstr "Har ingen relation vald"

#: includes/admin/post-types/admin-field-group.php:124
#: assets/build/js/acf-input.js:1166 assets/build/js/acf-input.js:1236
msgid "Has any relationship selected"
msgstr "Har någon relation vald"

#: includes/admin/post-types/admin-field-group.php:123
#: assets/build/js/acf-input.js:1327 assets/build/js/acf-input.js:1416
msgid "Has no post selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:122
#: assets/build/js/acf-input.js:1304 assets/build/js/acf-input.js:1390
msgid "Has any post selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:121
#: assets/build/js/acf-input.js:1277 assets/build/js/acf-input.js:1359
msgid "Posts do not contain"
msgstr "Inlägg innehållet inte"

#: includes/admin/post-types/admin-field-group.php:120
#: assets/build/js/acf-input.js:1250 assets/build/js/acf-input.js:1328
msgid "Posts contain"
msgstr "Inlägg innehåller"

#: includes/admin/post-types/admin-field-group.php:119
#: assets/build/js/acf-input.js:1231 assets/build/js/acf-input.js:1306
msgid "Post is not equal to"
msgstr "Inlägget är inte lika med"

#: includes/admin/post-types/admin-field-group.php:118
#: assets/build/js/acf-input.js:1212 assets/build/js/acf-input.js:1284
msgid "Post is equal to"
msgstr "Inlägget är lika med"

#: includes/admin/post-types/admin-field-group.php:117
#: assets/build/js/acf-input.js:1140 assets/build/js/acf-input.js:1208
msgid "Relationships do not contain"
msgstr "Relationer innehåller inte"

#: includes/admin/post-types/admin-field-group.php:116
#: assets/build/js/acf-input.js:1114 assets/build/js/acf-input.js:1181
msgid "Relationships contain"
msgstr "Relationer innehåller"

#: includes/admin/post-types/admin-field-group.php:115
#: assets/build/js/acf-input.js:1095 assets/build/js/acf-input.js:1161
msgid "Relationship is not equal to"
msgstr "Relation är inte lika med"

#: includes/admin/post-types/admin-field-group.php:114
#: assets/build/js/acf-input.js:1076 assets/build/js/acf-input.js:1141
msgid "Relationship is equal to"
msgstr "Relation är lika med"

#: includes/Blocks/Bindings.php:35
msgctxt "The core ACF block binding source name for fields on the current page"
msgid "ACF Fields"
msgstr "ACF-fält"

#: includes/admin/views/browse-fields-modal.php:14
msgid "ACF PRO Feature"
msgstr "ACF PRO-funktion"

#: includes/admin/views/browse-fields-modal.php:10
msgid "Renew PRO to Unlock"
msgstr "Förnya PRO för att låsa upp"

#: includes/admin/views/browse-fields-modal.php:8
msgid "Renew PRO License"
msgstr "Förnya PRO-licens"

#: includes/admin/views/acf-field-group/field.php:41
msgid "PRO fields cannot be edited without an active license."
msgstr "PRO-fält kan inte redigeras utan en aktiv licens."

#: includes/admin/admin-internal-post-type-list.php:232
msgid ""
"Please activate your ACF PRO license to edit field groups assigned to an ACF "
"Block."
msgstr ""
"Aktivera din ACF PRO-licens för att redigera fältgrupper som tilldelats ett "
"ACF-block."

#: includes/admin/admin-internal-post-type-list.php:231
msgid "Please activate your ACF PRO license to edit this options page."
msgstr "Aktivera din ACF PRO-licens för att redigera denna alternativsida."

#: includes/api/api-template.php:385 includes/api/api-template.php:439
msgid ""
"Returning escaped HTML values is only possible when format_value is also "
"true. The field values have not been returned for security."
msgstr ""

#: includes/api/api-template.php:46 includes/api/api-template.php:251
#: includes/api/api-template.php:947
msgid ""
"Returning an escaped HTML value is only possible when format_value is also "
"true. The field value has not been returned for security."
msgstr ""

#. translators: %1$s - name of the ACF plugin. %2$s - Link to documentation.
#. %3$s - Link to show more details about the error
#: includes/admin/views/escaped-html-notice.php:32
msgid ""
"%1$s ACF now automatically escapes unsafe HTML when rendered by "
"<code>the_field</code> or the ACF shortcode. We've detected the output of "
"some of your fields has been modified by this change, but this may not be a "
"breaking change. %2$s."
msgstr ""

#: includes/admin/views/escaped-html-notice.php:27
msgid "Please contact your site administrator or developer for more details."
msgstr ""
"Kontakta din webbplatsadministratör eller utvecklare för mer information."

#: includes/admin/views/escaped-html-notice.php:5
msgid "Learn&nbsp;more"
msgstr "Lär dig&nbsp;mer"

#: includes/admin/admin.php:63
msgid "Hide&nbsp;details"
msgstr "Dölj&nbsp;detaljer"

#: includes/admin/admin.php:62 includes/admin/views/escaped-html-notice.php:24
msgid "Show&nbsp;details"
msgstr "Visa&nbsp;detaljer"

#. translators: %1$s - The selector used  %2$s The field name  3%$s The parent
#. function name
#: includes/admin/views/escaped-html-notice.php:49
msgid "%1$s (%2$s) - rendered via %3$s"
msgstr "%1$s (%2$s) -– återgiven via %3$s"

#: includes/admin/views/global/navigation.php:226
msgid "Renew ACF PRO License"
msgstr "Förnya ACF PRO-licens"

#: includes/admin/views/acf-field-group/pro-features.php:17
msgid "Renew License"
msgstr "Förnya licens"

#: includes/admin/views/acf-field-group/pro-features.php:14
msgid "Manage License"
msgstr "Hantera licens"

#: includes/admin/views/acf-field-group/options.php:102
msgid "'High' position not supported in the Block Editor"
msgstr "Positionen ”Hög” stöds inte i blockredigeraren"

#: includes/admin/views/options-page-preview.php:30
msgid "Upgrade to ACF PRO"
msgstr "Uppgradera till ACF PRO"

#. translators: %s URL to ACF options pages documentation
#: includes/admin/views/options-page-preview.php:7
msgid ""
"ACF <a href=\"%s\" target=\"_blank\">options pages</a> are custom admin "
"pages for managing global settings via fields. You can create multiple pages "
"and sub-pages."
msgstr ""

#: includes/admin/views/global/header.php:35
msgid "Add Options Page"
msgstr "Lägg till alternativsida"

#: includes/admin/views/acf-post-type/advanced-settings.php:708
msgid "In the editor used as the placeholder of the title."
msgstr "Används som platshållare för rubriken i redigeraren."

#: includes/admin/views/acf-post-type/advanced-settings.php:707
msgid "Title Placeholder"
msgstr "Platshållare för rubrik"

#: includes/admin/views/global/navigation.php:97
msgid "4 Months Free"
msgstr "4 månader gratis"

#. translators: %s - A singular label for a post type or taxonomy.
#: includes/admin/views/global/form-top.php:59
msgid "(Duplicated from %s)"
msgstr "(Duplicerad från %s)"

#: includes/admin/tools/class-acf-admin-tool-export.php:289
msgid "Select Options Pages"
msgstr "﻿Välj alternativsidor"

#: includes/admin/post-types/admin-taxonomy.php:107
msgid "Duplicate taxonomy"
msgstr "Duplicera taxonomi"

#: includes/admin/post-types/admin-post-type.php:106
#: includes/admin/post-types/admin-taxonomy.php:106
msgid "Create taxonomy"
msgstr "Skapa taxonomi"

#: includes/admin/post-types/admin-post-type.php:105
msgid "Duplicate post type"
msgstr "Duplicera inläggstyp"

#: includes/admin/post-types/admin-post-type.php:104
#: includes/admin/post-types/admin-taxonomy.php:108
msgid "Create post type"
msgstr "Skapa inläggstyp"

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:105
msgid "Link field groups"
msgstr "Länka fältgrupper"

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:104
msgid "Add fields"
msgstr "Lägg till fält"

#: includes/admin/post-types/admin-field-group.php:147
#: assets/build/js/acf-field-group.js:2803
#: assets/build/js/acf-field-group.js:3298
msgid "This Field"
msgstr "Detta fält"

#: includes/admin/admin.php:352
msgid "ACF PRO"
msgstr "ACF PRO"

#: includes/admin/admin.php:350
msgid "Feedback"
msgstr "Feedback"

#: includes/admin/admin.php:348
msgid "Support"
msgstr "Support"

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:323
msgid "is developed and maintained by"
msgstr "utvecklas och underhålls av"

#. translators: %s - either "post type" or "taxonomy"
#: includes/admin/admin-internal-post-type.php:313
msgid "Add this %s to the location rules of the selected field groups."
msgstr "Lägg till denna %s i platsreglerna för de valda fältgrupperna."

#. translators: %s the URL to ACF's bidirectional relationship documentation
#: includes/acf-bidirectional-functions.php:272
msgid ""
"Enabling the bidirectional setting allows you to update a value in the "
"target fields for each value selected for this field, adding or removing the "
"Post ID, Taxonomy ID or User ID of the item being updated. For more "
"information, please read the <a href=\"%s\" target=\"_blank\">documentation</"
"a>."
msgstr ""

#: includes/acf-bidirectional-functions.php:248
msgid ""
"Select field(s) to store the reference back to the item being updated. You "
"may select this field. Target fields must be compatible with where this "
"field is being displayed. For example, if this field is displayed on a "
"Taxonomy, your target field should be of type Taxonomy"
msgstr ""

#: includes/acf-bidirectional-functions.php:247
msgid "Target Field"
msgstr "Målfält"

#: includes/acf-bidirectional-functions.php:221
msgid "Update a field on the selected values, referencing back to this ID"
msgstr ""
"Uppdatera ett fält med de valda värdena och hänvisa tillbaka till detta ID"

#: includes/acf-bidirectional-functions.php:220
msgid "Bidirectional"
msgstr "Dubbelriktad"

#. translators: %s A field type name, such as "Relationship"
#: includes/acf-bidirectional-functions.php:193
msgid "%s Field"
msgstr "%s-fält"

#: includes/fields/class-acf-field-page_link.php:498
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-select.php:380
#: includes/fields/class-acf-field-user.php:111
msgid "Select Multiple"
msgstr "Välj flera"

#: includes/admin/views/global/navigation.php:238
msgid "WP Engine logo"
msgstr "WP Engine-logga"

#: includes/admin/views/acf-taxonomy/basic-settings.php:58
msgid "Lower case letters, underscores and dashes only, Max 32 characters."
msgstr ""
"Endast gemena bokstäver, understreck och bindestreck, maximalt 32 tecken."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1136
msgid "The capability name for assigning terms of this taxonomy."
msgstr "Namn på behörigheten för tilldelning av termer i denna taxonomi."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1135
msgid "Assign Terms Capability"
msgstr "Behörighet att tilldela termer"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1119
msgid "The capability name for deleting terms of this taxonomy."
msgstr "Namn på behörigheten för borttagning av termer i denna taxonomi."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1118
msgid "Delete Terms Capability"
msgstr "Behörighet att ta bort termer"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1102
msgid "The capability name for editing terms of this taxonomy."
msgstr "Namn på behörigheten för redigering av termer i denna taxonomi."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1101
msgid "Edit Terms Capability"
msgstr "Behörighet att redigera termer"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1085
msgid "The capability name for managing terms of this taxonomy."
msgstr "Namn på behörigheten för hantering av termer i denna taxonomi."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1084
msgid "Manage Terms Capability"
msgstr "Behörighet att hantera termer"

#: includes/admin/views/acf-post-type/advanced-settings.php:914
msgid ""
"Sets whether posts should be excluded from search results and taxonomy "
"archive pages."
msgstr ""
"Anger om inlägg ska exkluderas från sökresultat och arkivsidor för "
"taxonomier."

#: includes/admin/views/acf-field-group/pro-features.php:78
msgid "More Tools from WP Engine"
msgstr "Fler verktyg från WP Engine"

#. translators: %s - WP Engine logo
#: includes/admin/views/acf-field-group/pro-features.php:73
msgid "Built for those that build with WordPress, by the team at %s"
msgstr "Byggt för dem som bygger med WordPress, av teamet på %s"

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "View Pricing & Upgrade"
msgstr "Visa priser och uppgradering"

#: includes/admin/views/acf-field-group/pro-features.php:3
#: includes/admin/views/options-page-preview.php:29
#: includes/fields/class-acf-field-icon_picker.php:248
msgid "Learn More"
msgstr "Lär dig mer"

#: includes/admin/views/acf-field-group/pro-features.php:28
msgid ""
"Speed up your workflow and develop better websites with features like ACF "
"Blocks and Options Pages, and sophisticated field types like Repeater, "
"Flexible Content, Clone, and Gallery."
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:2
msgid "Unlock Advanced Features and Build Even More with ACF PRO"
msgstr "Lås upp avancerade funktioner och bygg ännu mer med ACF PRO"

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:19
msgid "%s fields"
msgstr "%s-fält"

#: includes/admin/post-types/admin-taxonomies.php:267
msgid "No terms"
msgstr "Inga termer"

#: includes/admin/post-types/admin-taxonomies.php:240
msgid "No post types"
msgstr "Inga inläggstyper"

#: includes/admin/post-types/admin-post-types.php:264
msgid "No posts"
msgstr "Inga inlägg"

#: includes/admin/post-types/admin-post-types.php:238
msgid "No taxonomies"
msgstr "Inga taxonomier"

#: includes/admin/post-types/admin-post-types.php:183
#: includes/admin/post-types/admin-taxonomies.php:182
msgid "No field groups"
msgstr "Inga fältgrupper"

#: includes/admin/post-types/admin-field-groups.php:255
msgid "No fields"
msgstr "Inga fält"

#: includes/admin/post-types/admin-field-groups.php:128
#: includes/admin/post-types/admin-post-types.php:147
#: includes/admin/post-types/admin-taxonomies.php:146
msgid "No description"
msgstr "Ingen beskrivning"

#: includes/fields/class-acf-field-page_link.php:465
#: includes/fields/class-acf-field-post_object.php:374
#: includes/fields/class-acf-field-relationship.php:573
msgid "Any post status"
msgstr "Vilken inläggsstatus som helst"

#: includes/post-types/class-acf-taxonomy.php:288
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr ""
"Denna taxonominyckel används redan av en annan taxonomi som är registrerad "
"utanför ACF och kan inte användas."

#: includes/post-types/class-acf-taxonomy.php:284
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr ""
"Denna taxonominyckel används redan av en annan taxonomi i ACF och kan inte "
"användas."

#: includes/post-types/class-acf-taxonomy.php:256
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:251
msgid "The taxonomy key must be under 32 characters."
msgstr "Taxonominyckeln måste vara under 32 tecken."

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr "Inga taxonomier hittades i papperskorgen"

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr "Inga taxonomier hittades"

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr "Sök taxonomier"

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr "Visa taxonomi"

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr "Ny taxonomi"

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr "Redigera taxonomi"

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr "Lägg till ny taxonomi"

#: includes/post-types/class-acf-post-type.php:100
msgid "No Post Types found in Trash"
msgstr "Inga inläggstyper hittades i papperskorgen"

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found"
msgstr "Inga inläggstyper hittades"

#: includes/post-types/class-acf-post-type.php:98
msgid "Search Post Types"
msgstr "Sök inläggstyper"

#: includes/post-types/class-acf-post-type.php:97
msgid "View Post Type"
msgstr "Visa inläggstyp"

#: includes/post-types/class-acf-post-type.php:96
msgid "New Post Type"
msgstr "Ny inläggstyp"

#: includes/post-types/class-acf-post-type.php:95
msgid "Edit Post Type"
msgstr "Redigera inläggstyp"

#: includes/post-types/class-acf-post-type.php:94
msgid "Add New Post Type"
msgstr "Lägg till ny inläggstyp"

#: includes/post-types/class-acf-post-type.php:366
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr ""
"Denna nyckel för inläggstyp används redan av en annan inläggstyp som är "
"registrerad utanför ACF och kan inte användas."

#: includes/post-types/class-acf-post-type.php:361
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr ""
"Denna nyckel för inläggstyp används redan av en annan inläggstyp i ACF och "
"kan inte användas."

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:339
#: includes/post-types/class-acf-taxonomy.php:262
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr ""
"Detta fält får inte vara en av WordPress <a href=\"%s\" "
"target=\"_blank\">reserverad term</a>."

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""

#: includes/post-types/class-acf-post-type.php:328
msgid "The post type key must be under 20 characters."
msgstr "Nyckeln för inläggstypen måste vara kortare än 20 tecken."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid "We do not recommend using this field in ACF Blocks."
msgstr "Vi avråder från att använda detta fält i ACF-block."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:22
msgid "WYSIWYG Editor"
msgstr "WYSIWYG-redigerare"

#: includes/fields/class-acf-field-user.php:17
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr ""
"Tillåter val av en eller flera användare som kan användas för att skapa "
"relationer mellan dataobjekt."

#: includes/fields/class-acf-field-url.php:20
msgid "A text input specifically designed for storing web addresses."
msgstr "En textinmatning speciellt designad för att lagra webbadresser."

#: includes/fields/class-acf-field-icon_picker.php:56
#: includes/fields/class-acf-field-url.php:19
msgid "URL"
msgstr "URL"

#: includes/fields/class-acf-field-true_false.php:24
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""
"Ett reglage som låter dig välja ett av värdena 1 eller 0 (på eller av, sant "
"eller falskt osv.). Kan presenteras som ett stiliserat kontrollreglage eller "
"kryssruta."

#: includes/fields/class-acf-field-time_picker.php:24
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr ""
"Ett interaktivt användargränssnitt för att välja en tid. Tidsformatet kan "
"anpassas med hjälp av fältinställningarna."

#: includes/fields/class-acf-field-textarea.php:23
msgid "A basic textarea input for storing paragraphs of text."
msgstr "Ett enkelt textområde för lagring av textstycken."

#: includes/fields/class-acf-field-text.php:23
msgid "A basic text input, useful for storing single string values."
msgstr ""
"En grundläggande textinmatning, användbar för att lagra enskilda "
"strängvärden."

#: includes/fields/class-acf-field-taxonomy.php:22
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr ""

#: includes/fields/class-acf-field-tab.php:25
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""

#: includes/fields/class-acf-field-select.php:24
msgid "A dropdown list with a selection of choices that you specify."
msgstr "En rullgardinslista med ett urval av val som du anger."

#: includes/fields/class-acf-field-relationship.php:19
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""

#: includes/fields/class-acf-field-range.php:23
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr ""

#: includes/fields/class-acf-field-radio.php:24
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr ""

#: includes/fields/class-acf-field-post_object.php:17
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""

#: includes/fields/class-acf-field-password.php:23
msgid "An input for providing a password using a masked field."
msgstr ""
"Ett inmatningsfält för att ange ett lösenord med hjälp av ett maskerat fält."

#: includes/fields/class-acf-field-page_link.php:457
#: includes/fields/class-acf-field-post_object.php:366
#: includes/fields/class-acf-field-relationship.php:565
msgid "Filter by Post Status"
msgstr "Filtrera efter inläggsstatus"

#: includes/fields/class-acf-field-page_link.php:24
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""

#: includes/fields/class-acf-field-oembed.php:24
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""
"En interaktiv komponent för att bädda in videoklipp, bilder, tweets, ljud "
"och annat innehåll genom att använda den inbyggda WordPress oEmbed-"
"funktionen."

#: includes/fields/class-acf-field-number.php:23
msgid "An input limited to numerical values."
msgstr "Ett inmatningsfält begränsat till numeriska värden."

#: includes/fields/class-acf-field-message.php:25
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr ""

#: includes/fields/class-acf-field-link.php:24
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr ""
"Gör att du kan ange en länk och dess egenskaper som rubrik och mål med hjälp "
"av WordPress inbyggda länkväljare."

#: includes/fields/class-acf-field-image.php:24
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr ""
"Använder den inbyggda mediaväljaren i WordPress för att ladda upp eller "
"välja bilder."

#: includes/fields/class-acf-field-group.php:24
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr ""

#: includes/fields/class-acf-field-google-map.php:24
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""

#: includes/fields/class-acf-field-file.php:24
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr ""
"Använder den inbyggda mediaväljaren i WordPress för att ladda upp eller "
"välja filer."

#: includes/fields/class-acf-field-email.php:23
msgid "A text input specifically designed for storing email addresses."
msgstr "En textinmatning speciellt designad för att lagra e-postadresser."

#: includes/fields/class-acf-field-date_time_picker.php:24
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:24
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:24
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr ""
"Ett interaktivt användargränssnitt för att välja en färg eller ange ett HEX-"
"värde."

#: includes/fields/class-acf-field-checkbox.php:24
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr ""

#: includes/fields/class-acf-field-button-group.php:25
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr ""

#: includes/fields/class-acf-field-accordion.php:26
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""
"Gör att du kan gruppera och organisera anpassade fält i hopfällbara paneler "
"som visas när du redigerar innehåll. Användbart för att hålla ordning på "
"stora datauppsättningar."

#: includes/fields.php:449
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""

#: includes/fields.php:439
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""
"Detta tillhandahåller en interaktiv gränssnitt för att hantera en samling av "
"bilagor. De flesta inställningarna är liknande bildfälttypen. Ytterligare "
"inställningar låter dig specificera var nya bilagor ska läggas till i "
"galleriet och det minsta/maximala antalet bilagor som tillåts."

#: includes/fields.php:429
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""

#: includes/fields.php:419
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""
"Detta gör det möjligt för dig att välja och visa befintliga fält. Det "
"duplicerar inte några fält i databasen, utan laddar och visar de valda "
"fälten vid körtid. Klonfältet kan antingen ersätta sig själv med de valda "
"fälten eller visa de valda fälten som en grupp av underfält."

#: includes/fields.php:416
msgctxt "noun"
msgid "Clone"
msgstr "Klona"

#: includes/admin/views/global/navigation.php:86
#: includes/class-acf-site-health.php:286 includes/fields.php:331
msgid "PRO"
msgstr "PRO"

#: includes/fields.php:329 includes/fields.php:386
msgid "Advanced"
msgstr "Avancerad"

#: includes/ajax/class-acf-ajax-local-json-diff.php:90
msgid "JSON (newer)"
msgstr "JSON (nyare)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:86
msgid "Original"
msgstr "Original"

#: includes/ajax/class-acf-ajax-local-json-diff.php:60
msgid "Invalid post ID."
msgstr "Ogiltigt inläggs-ID."

#: includes/ajax/class-acf-ajax-local-json-diff.php:52
msgid "Invalid post type selected for review."
msgstr "Ogiltig inläggstyp har valts för granskning."

#: includes/admin/views/global/navigation.php:189
msgid "More"
msgstr "Mer"

#: includes/admin/views/browse-fields-modal.php:96
msgid "Tutorial"
msgstr "Handledning"

#: includes/admin/views/browse-fields-modal.php:73
msgid "Select Field"
msgstr "Välj fält"

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:60
msgid "Try a different search term or browse %s"
msgstr "Prova med en annan sökterm eller bläddra bland %s"

#: includes/admin/views/browse-fields-modal.php:57
msgid "Popular fields"
msgstr "Populära fält"

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:50
#: includes/fields/class-acf-field-icon_picker.php:155
msgid "No search results for '%s'"
msgstr "Inga sökresultat för ”%s”"

#: includes/admin/views/browse-fields-modal.php:23
msgid "Search fields..."
msgstr "Sök fält …"

#: includes/admin/views/browse-fields-modal.php:21
msgid "Select Field Type"
msgstr "Välj fälttyp"

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr "Populär"

#: includes/admin/views/acf-taxonomy/list-empty.php:15
msgid "Add Taxonomy"
msgstr "Lägg till taxonomi"

#: includes/admin/views/acf-taxonomy/list-empty.php:14
msgid "Create custom taxonomies to classify post type content"
msgstr "Skapa anpassade taxonomier för att klassificera typ av inläggsinnehåll"

#: includes/admin/views/acf-taxonomy/list-empty.php:13
msgid "Add Your First Taxonomy"
msgstr "Lägg till din första taxonomi"

#: includes/admin/views/acf-taxonomy/basic-settings.php:122
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr "Hierarkiska taxonomier kan ha ättlingar (som kategorier)."

#: includes/admin/views/acf-taxonomy/basic-settings.php:107
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr "Gör en taxonomi synlig på front-end och i adminpanelen."

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "One or many post types that can be classified with this taxonomy."
msgstr "En eller flera inläggstyper som kan klassificeras med denna taxonomi."

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:60
msgid "genre"
msgstr "genre"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Genre"
msgstr "Genre"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:25
msgid "Genres"
msgstr "Genrer"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1211
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1155
msgid "Expose this post type in the REST API."
msgstr "Exponera denna inläggstyp i REST API."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1055
msgid "Customize the query variable name"
msgstr "Anpassa namnet på ”query”-variabeln"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1028
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:981
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:941
msgid "Customize the slug used in the URL"
msgstr "Anpassa ”slug” som används i URL:en"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:924
msgid "Permalinks for this taxonomy are disabled."
msgstr "Permalänkar för denna taxonomi är inaktiverade."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:921
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr ""
"Skriv om URL:en med nyckeln för taxonomin som slug. Din permalänkstruktur "
"kommer att vara"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:913
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1030
#: includes/admin/views/acf-taxonomy/basic-settings.php:57
msgid "Taxonomy Key"
msgstr "Taxonominyckel"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:911
msgid "Select the type of permalink to use for this taxonomy."
msgstr "Välj typen av permalänk som ska användas för denna taxonomi."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:896
msgid "Display a column for the taxonomy on post type listing screens."
msgstr "Visa en kolumn för taxonomin på listningsvyer för inläggstyper."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:895
msgid "Show Admin Column"
msgstr "Visa adminkolumn"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:882
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr "Visa taxonomin i panelen för snabb-/massredigering."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:881
msgid "Quick Edit"
msgstr "Snabbredigera"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:868
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:867
msgid "Tag Cloud"
msgstr "Etikettmoln"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:824
msgid ""
"A PHP function name to be called for sanitizing taxonomy data saved from a "
"meta box."
msgstr ""
"Ett PHP-funktionsnamn som ska anropas för att säkerhetsfiltrera taxonomidata "
"som sparats från en metaruta."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:823
msgid "Meta Box Sanitization Callback"
msgstr "Säkerhetsfiltrerande återanrop för metaruta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:805
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:804
msgid "Register Meta Box Callback"
msgstr "Registrera återanrop för metaruta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:763
msgid "No Meta Box"
msgstr "Ingen metaruta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:762
msgid "Custom Meta Box"
msgstr "Anpassad metaruta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:758
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:757
msgid "Meta Box"
msgstr "Metaruta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:746
#: includes/admin/views/acf-taxonomy/advanced-settings.php:767
msgid "Categories Meta Box"
msgstr "Metaruta för kategorier"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:745
#: includes/admin/views/acf-taxonomy/advanced-settings.php:766
msgid "Tags Meta Box"
msgstr "Metaruta för etiketter"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:704
msgid "A link to a tag"
msgstr "En länk till en etikett"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:703
msgid "Describes a navigation link block variation used in the block editor."
msgstr ""
"Beskriver en blockvariant för navigeringslänkar som används i "
"blockredigeraren."

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "A link to a %s"
msgstr "En länk till en/ett %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:683
msgid "Tag Link"
msgstr "Etikettlänk"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:682
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:663
msgid "← Go to tags"
msgstr "← Gå till etiketter"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:662
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr ""
"Tilldelar den text som används för att länka tillbaka till huvudindexet "
"efter uppdatering av en term."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:661
msgid "Back To Items"
msgstr "Tillbaka till objekt"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "← Go to %s"
msgstr "← Gå till %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:642
msgid "Tags list"
msgstr "Eitkettlista"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:641
msgid "Assigns text to the table hidden heading."
msgstr "Tilldelar texten för tabellens dolda rubrik."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:622
msgid "Tags list navigation"
msgstr "Navigation för etikettlista"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:621
msgid "Assigns text to the table pagination hidden heading."
msgstr "Tilldelar texten för den dolda rubriken för tabellens sidonumrering."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:597
msgid "Filter by category"
msgstr "Filtrera efter kategori"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:596
msgid "Assigns text to the filter button in the posts lists table."
msgstr "Tilldelar texten för filterknappen i tabellen med inläggslistor."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:595
msgid "Filter By Item"
msgstr "Filtret efter objekt"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter by %s"
msgstr "Filtrera efter %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:575
#: includes/admin/views/acf-taxonomy/advanced-settings.php:576
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr ""
"Beskrivningen visas inte som standard, men går att visa med vissa teman."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:574
msgid "Describes the Description field on the Edit Tags screen."
msgstr "Beskriver fältet ”Beskrivning” i vyn ”Redigera etiketter”."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:573
msgid "Description Field Description"
msgstr "Beskrivning för fältbeskrivning"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:554
#: includes/admin/views/acf-taxonomy/advanced-settings.php:555
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""
"Ange en överordnad term för att skapa en hierarki. Till exempel skulle "
"termen Jazz kunna vara överordnad till Bebop och Storband"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:553
msgid "Describes the Parent field on the Edit Tags screen."
msgstr "Beskriver fältet ”Överordnad” i vyn ”Redigera etiketter”."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:552
msgid "Parent Field Description"
msgstr "Överordnad fältbeskrivning"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:538
#: includes/admin/views/acf-taxonomy/advanced-settings.php:539
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""
"”Slug” är den URL-vänliga versionen av namnet. Det är vanligtvis bara små "
"bokstäver och innehåller bara bokstäver, siffror och bindestreck."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:537
msgid "Describes the Slug field on the Edit Tags screen."
msgstr "Beskriver fältet ”Slug” i vyn ”Redigera etiketter”."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:536
msgid "Slug Field Description"
msgstr "Beskrivning av slugfält"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:523
msgid "The name is how it appears on your site"
msgstr "Namnet är hur det ser ut på din webbplats"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:521
msgid "Describes the Name field on the Edit Tags screen."
msgstr "Beskriver fältet ”Namn” i vyn ”Redigera etiketter”."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:520
msgid "Name Field Description"
msgstr "Beskrivning av namnfält"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:507
msgid "No tags"
msgstr "Inga etiketter"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:506
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:505
msgid "No Terms"
msgstr "Inga termer"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No %s"
msgstr "Inga %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:486
msgid "No tags found"
msgstr "Inga etiketter hittades"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:485
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:484
msgid "Not Found"
msgstr "Hittades inte"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:463
msgid "Assigns text to the Title field of the Most Used tab."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:462
#: includes/admin/views/acf-taxonomy/advanced-settings.php:464
#: includes/admin/views/acf-taxonomy/advanced-settings.php:465
msgid "Most Used"
msgstr "Mest använda"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:444
msgid "Choose from the most used tags"
msgstr "Välj från de mest använda etiketterna"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:443
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:442
msgid "Choose From Most Used"
msgstr "Välj från mest använda"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose from the most used %s"
msgstr "Välj från de mest använda %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:418
msgid "Add or remove tags"
msgstr "Lägg till eller ta bort etiketter"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:417
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:416
msgid "Add Or Remove Items"
msgstr "Lägg till eller ta bort objekt"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add or remove %s"
msgstr "Lägg till eller ta bort %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:392
msgid "Separate tags with commas"
msgstr "Separera etiketter med kommatecken"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:391
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:390
msgid "Separate Items With Commas"
msgstr "Separera objekt med kommatecken"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate %s with commas"
msgstr "Separera %s med kommatecken"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:366
msgid "Popular Tags"
msgstr "Populära etiketter"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:365
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr ""
"Tilldelar text till populära objekt. Används endast för icke-hierarkiska "
"taxonomier."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:364
msgid "Popular Items"
msgstr "Populära objekt"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Popular %s"
msgstr "Populär %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:347
msgid "Search Tags"
msgstr "Sök etiketter"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:346
msgid "Assigns search items text."
msgstr "Tilldelar texten för att söka objekt."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:323
msgid "Parent Category:"
msgstr "Överordnad kategori:"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:322
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr "Tilldelar text för överordnat objekt, men med ett kolon (:) i slutet."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:321
msgid "Parent Item With Colon"
msgstr "Överordnat objekt med kolon"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:298
msgid "Parent Category"
msgstr "Överordnad kategori"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:297
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr ""
"Tilldelar text för överordnat objekt. Används endast i hierarkiska "
"taxonomier."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:296
msgid "Parent Item"
msgstr "Överordnat objekt"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Parent %s"
msgstr "Överordnad %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:278
msgid "New Tag Name"
msgstr "Nytt etikettnamn"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:277
msgid "Assigns the new item name text."
msgstr "Tilldelar texten för nytt namn på objekt."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:276
msgid "New Item Name"
msgstr "Nytt objektnamn"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "New %s Name"
msgstr "Namn på ny %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:258
msgid "Add New Tag"
msgstr "Lägg till ny etikett"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:257
msgid "Assigns the add new item text."
msgstr "Tilldelar texten för att lägga till nytt objekt."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:238
msgid "Update Tag"
msgstr "Uppdatera etikett"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:237
msgid "Assigns the update item text."
msgstr "Tilldelar texten för uppdatera objekt."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:236
msgid "Update Item"
msgstr "Uppdatera objekt"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Update %s"
msgstr "Uppdatera %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:218
msgid "View Tag"
msgstr "Visa etikett"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:217
msgid "In the admin bar to view term during editing."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:198
msgid "Edit Tag"
msgstr "Redigera etikett"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:197
msgid "At the top of the editor screen when editing a term."
msgstr "Överst i redigeringsvyn när du redigerar en term."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:178
msgid "All Tags"
msgstr "Alla etiketter"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:177
msgid "Assigns the all items text."
msgstr "Tilldelar texten för alla objekt."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:158
msgid "Assigns the menu name text."
msgstr "Tilldelar texten för menynamn."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:157
msgid "Menu Label"
msgstr "Menyetikett"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:131
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr "Aktiva taxonomier är aktiverade och registrerade med WordPress."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:115
msgid "A descriptive summary of the taxonomy."
msgstr "En beskrivande sammanfattning av taxonomin."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:95
msgid "A descriptive summary of the term."
msgstr "En beskrivande sammanfattning av termen."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:94
msgid "Term Description"
msgstr "Termbeskrivning"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:76
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr "Enstaka ord, inga mellanslag. Understreck och bindestreck tillåtna."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:75
msgid "Term Slug"
msgstr "Termens slug"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:56
msgid "The name of the default term."
msgstr "Standardtermens namn."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:55
msgid "Term Name"
msgstr "Termnamn"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:41
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr ""
"Skapa en term för taxonomin som inte kan tas bort. Den kommer inte att "
"väljas för inlägg som standard."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:40
msgid "Default Term"
msgstr "Standardterm"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:28
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:27
msgid "Sort Terms"
msgstr "Sortera termer"

#: includes/admin/views/acf-post-type/list-empty.php:14
msgid "Add Post Type"
msgstr "Lägg till inläggstyp"

#: includes/admin/views/acf-post-type/list-empty.php:13
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr ""
"Utöka funktionaliteten i WordPress utöver vanliga inlägg och sidor med "
"anpassade inläggstyper."

#: includes/admin/views/acf-post-type/list-empty.php:12
msgid "Add Your First Post Type"
msgstr "Lägg till din första inläggstyp"

#: includes/admin/views/acf-post-type/basic-settings.php:136
#: includes/admin/views/acf-taxonomy/basic-settings.php:135
msgid "I know what I'm doing, show me all the options."
msgstr "Jag vet vad jag gör, visa mig alla alternativ."

#: includes/admin/views/acf-post-type/basic-settings.php:135
#: includes/admin/views/acf-taxonomy/basic-settings.php:134
msgid "Advanced Configuration"
msgstr "Avancerad konfiguration"

#: includes/admin/views/acf-post-type/basic-settings.php:123
msgid "Hierarchical post types can have descendants (like pages)."
msgstr "Hierarkiska inläggstyper kan ha ättlingar (som sidor)."

#: includes/admin/views/acf-post-type/basic-settings.php:122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:980
#: includes/admin/views/acf-taxonomy/basic-settings.php:121
msgid "Hierarchical"
msgstr "Hierarkisk"

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Visible on the frontend and in the admin dashboard."
msgstr "Synlig på front-end och i adminpanelen."

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Public"
msgstr "Offentlig"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:59
msgid "movie"
msgstr "film"

#: includes/admin/views/acf-post-type/basic-settings.php:57
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr "Endast gemener, understreck och bindestreck, maximalt 20 tecken."

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:41
msgid "Movie"
msgstr "Film"

#: includes/admin/views/acf-post-type/basic-settings.php:39
#: includes/admin/views/acf-taxonomy/basic-settings.php:40
msgid "Singular Label"
msgstr "Etikett i singular"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:24
msgid "Movies"
msgstr "Filmer"

#: includes/admin/views/acf-post-type/basic-settings.php:22
#: includes/admin/views/acf-taxonomy/basic-settings.php:23
msgid "Plural Label"
msgstr "Etikett i plural"

#: includes/admin/views/acf-post-type/advanced-settings.php:1298
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1297
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1210
msgid "Controller Class"
msgstr "”Controller”-klass"

#: includes/admin/views/acf-post-type/advanced-settings.php:1279
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1191
msgid "The namespace part of the REST API URL."
msgstr "Namnrymdsdelen av REST API-URL:en."

#: includes/admin/views/acf-post-type/advanced-settings.php:1278
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1190
msgid "Namespace Route"
msgstr "Route för namnrymd"

#: includes/admin/views/acf-post-type/advanced-settings.php:1260
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1172
msgid "The base URL for the post type REST API URLs."
msgstr "Bas-URL för inläggstypens REST API-URL:er."

#: includes/admin/views/acf-post-type/advanced-settings.php:1259
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1171
msgid "Base URL"
msgstr "Bas-URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1245
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr ""
"Exponerar denna inläggstyp i REST API:t. Krävs för att använda "
"blockredigeraren."

#: includes/admin/views/acf-post-type/advanced-settings.php:1244
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1154
msgid "Show In REST API"
msgstr "Visa i REST API"

#: includes/admin/views/acf-post-type/advanced-settings.php:1223
msgid "Customize the query variable name."
msgstr "Anpassa namnet på ”query”-variabeln."

#: includes/admin/views/acf-post-type/advanced-settings.php:1222
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1054
msgid "Query Variable"
msgstr "”Query”-variabel"

#: includes/admin/views/acf-post-type/advanced-settings.php:1200
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1032
msgid "No Query Variable Support"
msgstr "Inget stöd för ”query”-variabel"

#: includes/admin/views/acf-post-type/advanced-settings.php:1199
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1031
msgid "Custom Query Variable"
msgstr "Anpassad ”query”-variabel"

#: includes/admin/views/acf-post-type/advanced-settings.php:1196
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1195
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1027
msgid "Query Variable Support"
msgstr "Stöd för ”query”-variabel"

#: includes/admin/views/acf-post-type/advanced-settings.php:1170
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1003
msgid "URLs for an item and items can be accessed with a query string."
msgstr "URL:er för ett eller flera objekt kan nås med en ”query”-sträng."

#: includes/admin/views/acf-post-type/advanced-settings.php:1169
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1002
msgid "Publicly Queryable"
msgstr "Offentligt sökbar"

#: includes/admin/views/acf-post-type/advanced-settings.php:1148
msgid "Custom slug for the Archive URL."
msgstr "Anpassad slug för arkiv-URL:en."

#: includes/admin/views/acf-post-type/advanced-settings.php:1147
msgid "Archive Slug"
msgstr "Arkiv-slug"

#: includes/admin/views/acf-post-type/advanced-settings.php:1134
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1133
msgid "Archive"
msgstr "Arkiv"

#: includes/admin/views/acf-post-type/advanced-settings.php:1113
msgid "Pagination support for the items URLs such as the archives."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1112
msgid "Pagination"
msgstr "Sidonumrering"

#: includes/admin/views/acf-post-type/advanced-settings.php:1095
msgid "RSS feed URL for the post type items."
msgstr "URL för RSS-flöde, för objekten i inläggstypen."

#: includes/admin/views/acf-post-type/advanced-settings.php:1094
msgid "Feed URL"
msgstr "Flödes-URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1076
#: includes/admin/views/acf-taxonomy/advanced-settings.php:961
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1075
#: includes/admin/views/acf-taxonomy/advanced-settings.php:960
msgid "Front URL Prefix"
msgstr "Prefix för inledande URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1056
msgid "Customize the slug used in the URL."
msgstr "Anpassa slugen som används i webbadressen."

#: includes/admin/views/acf-post-type/advanced-settings.php:1055
#: includes/admin/views/acf-taxonomy/advanced-settings.php:940
msgid "URL Slug"
msgstr "URL-slug"

#: includes/admin/views/acf-post-type/advanced-settings.php:1039
msgid "Permalinks for this post type are disabled."
msgstr "Permalänkar för denna inläggstyp är inaktiverade."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1038
#: includes/admin/views/acf-taxonomy/advanced-settings.php:923
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr ""
"Skriv om URL:en med en anpassad slug som definieras i inmatningsfältet "
"nedan. Din permalänkstruktur kommer att vara"

#: includes/admin/views/acf-post-type/advanced-settings.php:1030
#: includes/admin/views/acf-taxonomy/advanced-settings.php:915
msgid "No Permalink (prevent URL rewriting)"
msgstr "Ingen permalänk (förhindra URL-omskrivning)"

#: includes/admin/views/acf-post-type/advanced-settings.php:1029
#: includes/admin/views/acf-taxonomy/advanced-settings.php:914
msgid "Custom Permalink"
msgstr "Anpassad permalänk"

#: includes/admin/views/acf-post-type/advanced-settings.php:1028
#: includes/admin/views/acf-post-type/advanced-settings.php:1198
#: includes/admin/views/acf-post-type/basic-settings.php:56
msgid "Post Type Key"
msgstr "Nyckel för inläggstyp"

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1026
#: includes/admin/views/acf-post-type/advanced-settings.php:1036
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr ""
"Skriv om URL:en med nyckeln för inläggstypen som slug. Din permalänkstruktur "
"kommer att vara"

#: includes/admin/views/acf-post-type/advanced-settings.php:1024
#: includes/admin/views/acf-taxonomy/advanced-settings.php:910
msgid "Permalink Rewrite"
msgstr "Omskrivning av permalänk"

#: includes/admin/views/acf-post-type/advanced-settings.php:1010
msgid "Delete items by a user when that user is deleted."
msgstr "Ta bort objekt av en användare när den användaren tas bort."

#: includes/admin/views/acf-post-type/advanced-settings.php:1009
msgid "Delete With User"
msgstr "Ta bort med användare"

#: includes/admin/views/acf-post-type/advanced-settings.php:995
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr "Tillåt att inläggstypen exporteras från ”Verktyg” > ”Export”."

#: includes/admin/views/acf-post-type/advanced-settings.php:994
msgid "Can Export"
msgstr "Kan exportera"

#: includes/admin/views/acf-post-type/advanced-settings.php:963
msgid "Optionally provide a plural to be used in capabilities."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:962
msgid "Plural Capability Name"
msgstr "Namn i plural på behörighet"

#: includes/admin/views/acf-post-type/advanced-settings.php:944
msgid "Choose another post type to base the capabilities for this post type."
msgstr ""
"Välj en annan inläggstyp för att basera behörigheterna för denna inläggstyp."

#: includes/admin/views/acf-post-type/advanced-settings.php:943
msgid "Singular Capability Name"
msgstr "Namn i singular på behörighet"

#: includes/admin/views/acf-post-type/advanced-settings.php:929
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:928
msgid "Rename Capabilities"
msgstr "Byt namn på behörigheter"

#: includes/admin/views/acf-post-type/advanced-settings.php:913
msgid "Exclude From Search"
msgstr "Exkludera från sök"

#: includes/admin/views/acf-post-type/advanced-settings.php:900
#: includes/admin/views/acf-taxonomy/advanced-settings.php:854
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:899
#: includes/admin/views/acf-taxonomy/advanced-settings.php:853
msgid "Appearance Menus Support"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:881
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr "Visas som ett objekt i menyn ”Nytt” i adminfältet."

#: includes/admin/views/acf-post-type/advanced-settings.php:880
msgid "Show In Admin Bar"
msgstr "Visa i adminmeny"

#: includes/admin/views/acf-post-type/advanced-settings.php:849
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:848
msgid "Custom Meta Box Callback"
msgstr "Återanrop för anpassad metaruta"

#: includes/admin/views/acf-post-type/advanced-settings.php:822
#: includes/fields/class-acf-field-icon_picker.php:611
msgid "Menu Icon"
msgstr "Menyikon"

#: includes/admin/views/acf-post-type/advanced-settings.php:778
msgid "The position in the sidebar menu in the admin dashboard."
msgstr "Positionen i sidopanelsmenyn i adminpanelen."

#: includes/admin/views/acf-post-type/advanced-settings.php:777
msgid "Menu Position"
msgstr "Menyposition"

#: includes/admin/views/acf-post-type/advanced-settings.php:759
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:758
msgid "Admin Menu Parent"
msgstr "Överordnad adminmeny"

#: includes/admin/views/acf-post-type/advanced-settings.php:739
#: includes/admin/views/acf-taxonomy/advanced-settings.php:734
msgid "Admin editor navigation in the sidebar menu."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:738
#: includes/admin/views/acf-taxonomy/advanced-settings.php:733
msgid "Show In Admin Menu"
msgstr "Visa i adminmeny"

#: includes/admin/views/acf-post-type/advanced-settings.php:725
#: includes/admin/views/acf-taxonomy/advanced-settings.php:719
msgid "Items can be edited and managed in the admin dashboard."
msgstr "Objekt kan redigeras och hanteras i adminpanelen."

#: includes/admin/views/acf-post-type/advanced-settings.php:724
#: includes/admin/views/acf-taxonomy/advanced-settings.php:718
msgid "Show In UI"
msgstr "Visa i användargränssnitt"

#: includes/admin/views/acf-post-type/advanced-settings.php:694
msgid "A link to a post."
msgstr "En länk till ett inlägg."

#: includes/admin/views/acf-post-type/advanced-settings.php:693
msgid "Description for a navigation link block variation."
msgstr "Beskrivning för en variant av navigationslänksblock."

#: includes/admin/views/acf-post-type/advanced-settings.php:692
#: includes/admin/views/acf-taxonomy/advanced-settings.php:702
msgid "Item Link Description"
msgstr "Objekts länkbeskrivning"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:688
msgid "A link to a %s."
msgstr "En länk till en/ett %s."

#: includes/admin/views/acf-post-type/advanced-settings.php:673
msgid "Post Link"
msgstr "Inläggslänk"

#: includes/admin/views/acf-post-type/advanced-settings.php:672
msgid "Title for a navigation link block variation."
msgstr "Rubrik för en variant av navigationslänksblock."

#: includes/admin/views/acf-post-type/advanced-settings.php:671
#: includes/admin/views/acf-taxonomy/advanced-settings.php:681
msgid "Item Link"
msgstr "Objektlänk"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:668
#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid "%s Link"
msgstr "%s-länk"

#: includes/admin/views/acf-post-type/advanced-settings.php:653
msgid "Post updated."
msgstr "Inlägg uppdaterat."

#: includes/admin/views/acf-post-type/advanced-settings.php:652
msgid "In the editor notice after an item is updated."
msgstr "Avisering i redigeraren efter att ett objekt har uppdaterats."

#: includes/admin/views/acf-post-type/advanced-settings.php:651
msgid "Item Updated"
msgstr "Objekt uppdaterat"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:648
msgid "%s updated."
msgstr "%s har uppdaterats."

#: includes/admin/views/acf-post-type/advanced-settings.php:633
msgid "Post scheduled."
msgstr "Inlägget har schemalagts."

#: includes/admin/views/acf-post-type/advanced-settings.php:632
msgid "In the editor notice after scheduling an item."
msgstr "Avisering i redigeraren efter schemaläggning av ett objekt."

#: includes/admin/views/acf-post-type/advanced-settings.php:631
msgid "Item Scheduled"
msgstr "Objekt tidsinställt"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:628
msgid "%s scheduled."
msgstr "%s schemalagd."

#: includes/admin/views/acf-post-type/advanced-settings.php:613
msgid "Post reverted to draft."
msgstr "Inlägget har återställts till utkastet."

#: includes/admin/views/acf-post-type/advanced-settings.php:612
msgid "In the editor notice after reverting an item to draft."
msgstr ""
"Avisering i redigeraren efter att ha återställt ett objekt till utkast."

#: includes/admin/views/acf-post-type/advanced-settings.php:611
msgid "Item Reverted To Draft"
msgstr "Objekt återställt till utkast"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:608
msgid "%s reverted to draft."
msgstr "%s återställt till utkast."

#: includes/admin/views/acf-post-type/advanced-settings.php:593
msgid "Post published privately."
msgstr "Inlägg publicerat privat."

#: includes/admin/views/acf-post-type/advanced-settings.php:592
msgid "In the editor notice after publishing a private item."
msgstr "Avisering i redigeraren efter publicering av ett privat objekt."

#: includes/admin/views/acf-post-type/advanced-settings.php:591
msgid "Item Published Privately"
msgstr "Objekt publicerat privat"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:588
msgid "%s published privately."
msgstr "%s har publicerats privat."

#: includes/admin/views/acf-post-type/advanced-settings.php:573
msgid "Post published."
msgstr "Inlägg publicerat."

#: includes/admin/views/acf-post-type/advanced-settings.php:572
msgid "In the editor notice after publishing an item."
msgstr "Avisering i redigeraren efter publicering av ett objekt."

#: includes/admin/views/acf-post-type/advanced-settings.php:571
msgid "Item Published"
msgstr "Objekt publicerat"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:568
msgid "%s published."
msgstr "%s publicerad."

#: includes/admin/views/acf-post-type/advanced-settings.php:553
msgid "Posts list"
msgstr "Inläggslista"

#: includes/admin/views/acf-post-type/advanced-settings.php:552
msgid "Used by screen readers for the items list on the post type list screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:551
#: includes/admin/views/acf-taxonomy/advanced-settings.php:640
msgid "Items List"
msgstr "Objektlista"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:548
#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "%s list"
msgstr "%s-lista"

#: includes/admin/views/acf-post-type/advanced-settings.php:533
msgid "Posts list navigation"
msgstr "Navigation för inläggslista"

#: includes/admin/views/acf-post-type/advanced-settings.php:532
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:531
#: includes/admin/views/acf-taxonomy/advanced-settings.php:620
msgid "Items List Navigation"
msgstr "Navigation för objektlista"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:528
#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "%s list navigation"
msgstr "Navigation för %s-lista"

#: includes/admin/views/acf-post-type/advanced-settings.php:512
msgid "Filter posts by date"
msgstr "Filtrera inlägg efter datum"

#: includes/admin/views/acf-post-type/advanced-settings.php:511
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:510
msgid "Filter Items By Date"
msgstr "Filtrera objekt efter datum"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:506
msgid "Filter %s by date"
msgstr "Filtrera %s efter datum"

#: includes/admin/views/acf-post-type/advanced-settings.php:491
msgid "Filter posts list"
msgstr "Filtrera inläggslista"

#: includes/admin/views/acf-post-type/advanced-settings.php:490
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:489
msgid "Filter Items List"
msgstr "Filtrera lista med objekt"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:485
msgid "Filter %s list"
msgstr "Filtrera %s-listan"

#: includes/admin/views/acf-post-type/advanced-settings.php:469
msgid "In the media modal showing all media uploaded to this item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:468
msgid "Uploaded To This Item"
msgstr "Uppladdat till detta objekt"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:464
msgid "Uploaded to this %s"
msgstr "Uppladdad till denna %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:449
msgid "Insert into post"
msgstr "Infoga i inlägg"

#: includes/admin/views/acf-post-type/advanced-settings.php:448
msgid "As the button label when adding media to content."
msgstr "Som knappetikett när du lägger till media i innehållet."

#: includes/admin/views/acf-post-type/advanced-settings.php:447
msgid "Insert Into Media Button"
msgstr "Infoga ”Infoga media”-knapp"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:443
msgid "Insert into %s"
msgstr "Infoga i %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:428
msgid "Use as featured image"
msgstr "Använd som utvald bild"

#: includes/admin/views/acf-post-type/advanced-settings.php:427
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr ""
"Som knappetikett för att välja att använda en bild som den utvalda bilden."

#: includes/admin/views/acf-post-type/advanced-settings.php:426
msgid "Use Featured Image"
msgstr "Använd utvald bild"

#: includes/admin/views/acf-post-type/advanced-settings.php:413
msgid "Remove featured image"
msgstr "Ta bort utvald bild"

#: includes/admin/views/acf-post-type/advanced-settings.php:412
msgid "As the button label when removing the featured image."
msgstr "Som knappetiketten vid borttagning av den utvalda bilden."

#: includes/admin/views/acf-post-type/advanced-settings.php:411
msgid "Remove Featured Image"
msgstr "Ta bort utvald bild"

#: includes/admin/views/acf-post-type/advanced-settings.php:398
msgid "Set featured image"
msgstr "Ange utvald bild"

#: includes/admin/views/acf-post-type/advanced-settings.php:397
msgid "As the button label when setting the featured image."
msgstr "Som knappetiketten när den utvalda bilden anges."

#: includes/admin/views/acf-post-type/advanced-settings.php:396
msgid "Set Featured Image"
msgstr "Ange utvald bild"

#: includes/admin/views/acf-post-type/advanced-settings.php:383
msgid "Featured image"
msgstr "Utvald bild"

#: includes/admin/views/acf-post-type/advanced-settings.php:382
msgid "In the editor used for the title of the featured image meta box."
msgstr ""
"Används i redigeraren för rubriken på metarutan för den utvalda bilden."

#: includes/admin/views/acf-post-type/advanced-settings.php:381
msgid "Featured Image Meta Box"
msgstr "Metaruta för utvald bild"

#: includes/admin/views/acf-post-type/advanced-settings.php:368
msgid "Post Attributes"
msgstr "Inläggsattribut"

#: includes/admin/views/acf-post-type/advanced-settings.php:367
msgid "In the editor used for the title of the post attributes meta box."
msgstr "Används i redigeraren för rubriken på metarutan för inläggsattribut."

#: includes/admin/views/acf-post-type/advanced-settings.php:366
msgid "Attributes Meta Box"
msgstr "Metaruta för attribut"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:363
msgid "%s Attributes"
msgstr "Attribut för %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:348
msgid "Post Archives"
msgstr "Inläggsarkiv"

#: includes/admin/views/acf-post-type/advanced-settings.php:347
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:346
msgid "Archives Nav Menu"
msgstr "Navigationsmeny för arkiv"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:343
msgid "%s Archives"
msgstr "Arkiv för %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:328
msgid "No posts found in Trash"
msgstr "Inga inlägg hittades i papperskorgen"

#: includes/admin/views/acf-post-type/advanced-settings.php:327
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:326
msgid "No Items Found in Trash"
msgstr "Inga objekt hittades i papperskorgen"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:322
msgid "No %s found in Trash"
msgstr "Inga %s hittades i papperskorgen"

#: includes/admin/views/acf-post-type/advanced-settings.php:307
msgid "No posts found"
msgstr "Inga inlägg hittades"

#: includes/admin/views/acf-post-type/advanced-settings.php:306
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:305
msgid "No Items Found"
msgstr "Inga objekt hittades"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:301
#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "No %s found"
msgstr "Inga %s hittades"

#: includes/admin/views/acf-post-type/advanced-settings.php:286
msgid "Search Posts"
msgstr "Sök inlägg"

#: includes/admin/views/acf-post-type/advanced-settings.php:285
msgid "At the top of the items screen when searching for an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:284
#: includes/admin/views/acf-taxonomy/advanced-settings.php:345
msgid "Search Items"
msgstr "Sök objekt"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:281
#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Search %s"
msgstr "Sök efter %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:266
msgid "Parent Page:"
msgstr "Överordnad sida:"

#: includes/admin/views/acf-post-type/advanced-settings.php:265
msgid "For hierarchical types in the post type list screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:264
msgid "Parent Item Prefix"
msgstr "Prefix för överordnat objekt"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:261
#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Parent %s:"
msgstr "Överordnad %s:"

#: includes/admin/views/acf-post-type/advanced-settings.php:246
msgid "New Post"
msgstr "Nytt inlägg"

#: includes/admin/views/acf-post-type/advanced-settings.php:244
msgid "New Item"
msgstr "Nytt objekt"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:241
msgid "New %s"
msgstr "Ny %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:206
#: includes/admin/views/acf-post-type/advanced-settings.php:226
msgid "Add New Post"
msgstr "Lägg till nytt inlägg"

#: includes/admin/views/acf-post-type/advanced-settings.php:205
msgid "At the top of the editor screen when adding a new item."
msgstr "Överst i redigeringsvyn när du lägger till ett nytt objekt."

#: includes/admin/views/acf-post-type/advanced-settings.php:204
#: includes/admin/views/acf-taxonomy/advanced-settings.php:256
msgid "Add New Item"
msgstr "Lägg till nytt objekt"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:201
#: includes/admin/views/acf-post-type/advanced-settings.php:221
#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Add New %s"
msgstr "Lägg till ny/nytt %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:186
msgid "View Posts"
msgstr "Visa inlägg"

#: includes/admin/views/acf-post-type/advanced-settings.php:185
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:184
msgid "View Items"
msgstr "Visa objekt"

#: includes/admin/views/acf-post-type/advanced-settings.php:166
msgid "View Post"
msgstr "Visa inlägg"

#: includes/admin/views/acf-post-type/advanced-settings.php:165
msgid "In the admin bar to view item when editing it."
msgstr "I adminfältet för att visa objekt när du redigerar det."

#: includes/admin/views/acf-post-type/advanced-settings.php:164
#: includes/admin/views/acf-taxonomy/advanced-settings.php:216
msgid "View Item"
msgstr "Visa objekt"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:161
#: includes/admin/views/acf-post-type/advanced-settings.php:181
#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "View %s"
msgstr "Visa %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:146
msgid "Edit Post"
msgstr "Redigera inlägg"

#: includes/admin/views/acf-post-type/advanced-settings.php:145
msgid "At the top of the editor screen when editing an item."
msgstr "Överst i redigeringsvyn när du redigerar ett objekt."

#: includes/admin/views/acf-post-type/advanced-settings.php:144
#: includes/admin/views/acf-taxonomy/advanced-settings.php:196
msgid "Edit Item"
msgstr "Redigera objekt"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:141
#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "Edit %s"
msgstr "Redigera %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:126
msgid "All Posts"
msgstr "Alla inlägg"

#: includes/admin/views/acf-post-type/advanced-settings.php:125
#: includes/admin/views/acf-post-type/advanced-settings.php:225
#: includes/admin/views/acf-post-type/advanced-settings.php:245
msgid "In the post type submenu in the admin dashboard."
msgstr "I undermenyn för inläggstyp i adminpanelen."

#: includes/admin/views/acf-post-type/advanced-settings.php:124
#: includes/admin/views/acf-taxonomy/advanced-settings.php:176
msgid "All Items"
msgstr "Alla objekt"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "All %s"
msgstr "Alla %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:105
msgid "Admin menu name for the post type."
msgstr "Adminmenynamn för inläggstypen."

#: includes/admin/views/acf-post-type/advanced-settings.php:104
msgid "Menu Name"
msgstr "Menynamn"

#: includes/admin/views/acf-post-type/advanced-settings.php:90
#: includes/admin/views/acf-taxonomy/advanced-settings.php:142
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr "Återskapa alla etiketter med etiketterna för singular och plural"

#: includes/admin/views/acf-post-type/advanced-settings.php:88
#: includes/admin/views/acf-taxonomy/advanced-settings.php:140
msgid "Regenerate"
msgstr "Återskapa"

#: includes/admin/views/acf-post-type/advanced-settings.php:79
msgid "Active post types are enabled and registered with WordPress."
msgstr "Aktiva inläggstyper är aktiverade och registrerade med WordPress."

#: includes/admin/views/acf-post-type/advanced-settings.php:63
msgid "A descriptive summary of the post type."
msgstr "En beskrivande sammanfattning av inläggstypen."

#: includes/admin/views/acf-post-type/advanced-settings.php:48
msgid "Add Custom"
msgstr "Lägg till anpassad"

#: includes/admin/views/acf-post-type/advanced-settings.php:42
msgid "Enable various features in the content editor."
msgstr "Aktivera olika funktioner i innehållsredigeraren."

#: includes/admin/views/acf-post-type/advanced-settings.php:31
msgid "Post Formats"
msgstr "Inläggsformat"

#: includes/admin/views/acf-post-type/advanced-settings.php:25
msgid "Editor"
msgstr "Redigerare"

#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Trackbacks"
msgstr "Trackbacks"

#: includes/admin/views/acf-post-type/basic-settings.php:87
msgid "Select existing taxonomies to classify items of the post type."
msgstr ""
"Välj befintliga taxonomier för att klassificera objekt av inläggstypen."

#: includes/admin/views/acf-field-group/field.php:158
msgid "Browse Fields"
msgstr "Bläddra bland fält"

#: includes/admin/tools/class-acf-admin-tool-import.php:287
msgid "Nothing to import"
msgstr "Ingenting att importera"

#: includes/admin/tools/class-acf-admin-tool-import.php:282
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr ". Tillägget ”Custom Post Type UI” kan inaktiveras."

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:273
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] "Importerade %d objekt från ”Custom Post Type UI” –"
msgstr[1] "Importerade %d objekt från ”Custom Post Type UI” –"

#: includes/admin/tools/class-acf-admin-tool-import.php:257
msgid "Failed to import taxonomies."
msgstr "Misslyckades att importera taxonomier."

#: includes/admin/tools/class-acf-admin-tool-import.php:239
msgid "Failed to import post types."
msgstr "Misslyckades att importera inläggstyper."

#: includes/admin/tools/class-acf-admin-tool-import.php:228
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr "Inget från ”Custom Post Type UI” har valts för import."

#: includes/admin/tools/class-acf-admin-tool-import.php:204
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] "Importerade 1 objekt"
msgstr[1] "Importerade %s objekt"

#: includes/admin/tools/class-acf-admin-tool-import.php:119
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""
"Om en inläggstyp eller taxonomi importeras med samma nyckel som en som redan "
"finns skrivs inställningarna för den befintliga inläggstypen eller taxonomin "
"över med inställningarna för importen."

#: includes/admin/tools/class-acf-admin-tool-import.php:108
#: includes/admin/tools/class-acf-admin-tool-import.php:124
msgid "Import from Custom Post Type UI"
msgstr "Importera från Custom Post Type UI"

#: includes/admin/tools/class-acf-admin-tool-export.php:354
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's functions."
"php file or include it within an external file, then deactivate or delete "
"the items from the ACF admin."
msgstr ""
"Följande kod kan användas för att registrera en lokal version av de valda "
"objekten. Att lagra fältgrupper, inläggstyper eller taxonomier lokalt kan ge "
"många fördelar, till exempel snabbare inläsningstider, versionskontroll och "
"dynamiska fält/inställningar. Kopiera och klistra in följande kod i ditt "
"temas ”functions.php”-fil eller inkludera den i en extern fil, inaktivera "
"eller ta sen bort objekten från ACF-administrationen."

#: includes/admin/tools/class-acf-admin-tool-export.php:353
msgid "Export - Generate PHP"
msgstr "Exportera - Generera PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:330
msgid "Export"
msgstr "Exportera"

#: includes/admin/tools/class-acf-admin-tool-export.php:264
msgid "Select Taxonomies"
msgstr "Välj taxonomier"

#: includes/admin/tools/class-acf-admin-tool-export.php:239
msgid "Select Post Types"
msgstr "Välj inläggstyper"

#: includes/admin/tools/class-acf-admin-tool-export.php:155
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] "Exporterade 1 objekt."
msgstr[1] "Exporterade %s objekt."

#: includes/admin/post-types/admin-taxonomy.php:127
#: assets/build/js/acf-internal-post-type.js:182
#: assets/build/js/acf-internal-post-type.js:256
msgid "Category"
msgstr "Kategori"

#: includes/admin/post-types/admin-taxonomy.php:125
#: assets/build/js/acf-internal-post-type.js:179
#: assets/build/js/acf-internal-post-type.js:253
msgid "Tag"
msgstr "Etikett"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr "Taxonomin %s skapades"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr "Taxonomin %s uppdaterad"

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr "Taxonomiutkast uppdaterat."

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr "Taxonomi schemalagd till."

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr "Taxonomi inskickad."

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr "Taxonomi sparad."

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr "Taxonomi borttagen."

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr "Taxonomi uppdaterad."

#: includes/admin/post-types/admin-taxonomies.php:351
#: includes/admin/post-types/admin-taxonomy.php:153
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr ""
"Denna taxonomi kunde inte registreras eftersom dess nyckel används av en "
"annan taxonomi som registrerats av ett annat tillägg eller tema."

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:333
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] "Taxonomi synkroniserad."
msgstr[1] "%s taxonomier synkroniserade."

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:326
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] "Taxonomi duplicerad."
msgstr[1] "%s taxonomier duplicerade."

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:319
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] "Taxonomi inaktiverad."
msgstr[1] "%s taxonomier inaktiverade."

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:312
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] "Taxonomi aktiverad."
msgstr[1] "%s taxonomier aktiverade."

#: includes/admin/post-types/admin-taxonomies.php:113
msgid "Terms"
msgstr "Termer"

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:327
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] "Inläggstyp synkroniserad."
msgstr[1] "%s inläggstyper synkroniserade."

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:320
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] "Inläggstyp duplicerad."
msgstr[1] "%s inläggstyper duplicerade."

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:313
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] "Inläggstyp inaktiverad."
msgstr[1] "%s inläggstyper inaktiverade."

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:306
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] "Inläggstyp aktiverad."
msgstr[1] "%s inläggstyper aktiverade."

#: includes/admin/post-types/admin-post-types.php:87
#: includes/admin/post-types/admin-taxonomies.php:111
#: includes/admin/tools/class-acf-admin-tool-import.php:79
#: includes/admin/views/acf-taxonomy/basic-settings.php:82
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Types"
msgstr "Inläggstyper"

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:160
msgid "Advanced Settings"
msgstr "Avancerade inställningar"

#: includes/admin/post-types/admin-post-type.php:157
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Basic Settings"
msgstr "Grundläggande inställningar"

#: includes/admin/post-types/admin-post-type.php:151
#: includes/admin/post-types/admin-post-types.php:345
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr ""
"Denna inläggstyp kunde inte registreras eftersom dess nyckel används av en "
"annan inläggstyp som registrerats av ett annat tillägg eller tema."

#: includes/admin/post-types/admin-post-type.php:126
#: assets/build/js/acf-internal-post-type.js:176
#: assets/build/js/acf-internal-post-type.js:250
msgid "Pages"
msgstr "Sidor"

#: includes/admin/admin-internal-post-type.php:347
msgid "Link Existing Field Groups"
msgstr "Länka befintliga fältgrupper"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:80
msgid "%s post type created"
msgstr "Inläggstypen %s skapad"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr "Lägg till fält till %s"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr "Inläggstypen %s uppdaterad"

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr "Utkast för inläggstyp uppdaterat."

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr "Inläggstyp schemalagd till."

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr "Inläggstyp inskickad."

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr "Inläggstyp sparad."

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr "Inläggstyp uppdaterad."

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr "Inläggstyp borttagen."

#: includes/admin/post-types/admin-field-group.php:146
#: assets/build/js/acf-field-group.js:1159
#: assets/build/js/acf-field-group.js:1383
msgid "Type to search..."
msgstr "Skriv för att söka …"

#: includes/admin/post-types/admin-field-group.php:101
#: assets/build/js/acf-field-group.js:1186
#: assets/build/js/acf-field-group.js:2349
#: assets/build/js/acf-field-group.js:1429
#: assets/build/js/acf-field-group.js:2761
msgid "PRO Only"
msgstr "Endast PRO"

#: includes/admin/post-types/admin-field-group.php:93
#: assets/build/js/acf-internal-post-type.js:308
#: assets/build/js/acf-internal-post-type.js:417
msgid "Field groups linked successfully."
msgstr "Fältgrupper har länkats."

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:199
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""
"Importera inläggstyper och taxonomier som registrerats med ”Custom Post Type "
"UI” och hantera dem med ACF. <a href=\"%s\">Kom igång</a>."

#: includes/admin/admin.php:46 includes/admin/admin.php:352
#: includes/class-acf-site-health.php:250
msgid "ACF"
msgstr "ACF"

#: includes/admin/admin-internal-post-type.php:314
msgid "taxonomy"
msgstr "taxonomi"

#: includes/admin/admin-internal-post-type.php:314
msgid "post type"
msgstr "inläggstyp"

#: includes/admin/admin-internal-post-type.php:338
msgid "Done"
msgstr "Klar"

#: includes/admin/admin-internal-post-type.php:324
msgid "Field Group(s)"
msgstr "Fältgrupp/fältgrupper"

#: includes/admin/admin-internal-post-type.php:323
msgid "Select one or many field groups..."
msgstr "Markera en eller flera fältgrupper …"

#: includes/admin/admin-internal-post-type.php:322
msgid "Please select the field groups to link."
msgstr "Välj de fältgrupper som ska länkas."

#: includes/admin/admin-internal-post-type.php:280
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] "Fältgrupp har länkats."
msgstr[1] "Fältgrupper har länkats."

#: includes/admin/admin-internal-post-type-list.php:277
#: includes/admin/post-types/admin-post-types.php:346
#: includes/admin/post-types/admin-taxonomies.php:352
msgctxt "post status"
msgid "Registration Failed"
msgstr "Registrering misslyckades"

#: includes/admin/admin-internal-post-type-list.php:276
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr ""
"Detta objekt kunde inte registreras eftersom dess nyckel används av ett "
"annat objekt som registrerats av ett annat tillägg eller tema."

#: includes/acf-internal-post-type-functions.php:509
#: includes/acf-internal-post-type-functions.php:538
msgid "REST API"
msgstr "REST API"

#: includes/acf-internal-post-type-functions.php:508
#: includes/acf-internal-post-type-functions.php:537
#: includes/acf-internal-post-type-functions.php:564
msgid "Permissions"
msgstr "Behörigheter"

#: includes/acf-internal-post-type-functions.php:507
#: includes/acf-internal-post-type-functions.php:536
msgid "URLs"
msgstr "URL:er"

#: includes/acf-internal-post-type-functions.php:506
#: includes/acf-internal-post-type-functions.php:535
#: includes/acf-internal-post-type-functions.php:562
msgid "Visibility"
msgstr "Synlighet"

#: includes/acf-internal-post-type-functions.php:505
#: includes/acf-internal-post-type-functions.php:534
#: includes/acf-internal-post-type-functions.php:563
msgid "Labels"
msgstr "Etiketter"

#: includes/admin/post-types/admin-field-group.php:279
msgid "Field Settings Tabs"
msgstr "Fältinställningar för flikar"

#. Author URI of the plugin
#: acf.php
msgid ""
"https://wpengine.com/?utm_source=wordpress."
"org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""
"https://wpengine.com/?utm_source=wordpress."
"org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"

#: includes/api/api-template.php:1027
msgid "[ACF shortcode value disabled for preview]"
msgstr "[ACF-kortkod inaktiverad för förhandsvisning]"

#: includes/admin/admin-internal-post-type.php:290
#: includes/admin/post-types/admin-field-group.php:572
msgid "Close Modal"
msgstr "Stäng modal"

#: includes/admin/post-types/admin-field-group.php:92
#: assets/build/js/acf-field-group.js:1701
#: assets/build/js/acf-field-group.js:2032
msgid "Field moved to other group"
msgstr "Fält flyttat till annan grupp"

#: includes/admin/post-types/admin-field-group.php:91
#: assets/build/js/acf.js:1443 assets/build/js/acf.js:1521
msgid "Close modal"
msgstr "Stäng modal"

#: includes/fields/class-acf-field-tab.php:119
msgid "Start a new group of tabs at this tab."
msgstr "Starta en ny grupp av flikar på denna flik."

#: includes/fields/class-acf-field-tab.php:118
msgid "New Tab Group"
msgstr "Ny flikgrupp"

#: includes/fields/class-acf-field-select.php:423
#: includes/fields/class-acf-field-true_false.php:188
msgid "Use a stylized checkbox using select2"
msgstr "Använd en stiliserad kryssruta med hjälp av select2"

#: includes/fields/class-acf-field-radio.php:250
msgid "Save Other Choice"
msgstr "Spara annat val"

#: includes/fields/class-acf-field-radio.php:239
msgid "Allow Other Choice"
msgstr "Tillåt annat val"

#: includes/fields/class-acf-field-checkbox.php:420
msgid "Add Toggle All"
msgstr "Lägg till ”Slå på/av alla”"

#: includes/fields/class-acf-field-checkbox.php:379
msgid "Save Custom Values"
msgstr "Spara anpassade värden"

#: includes/fields/class-acf-field-checkbox.php:368
msgid "Allow Custom Values"
msgstr "Tillåt anpassade värden"

#: includes/fields/class-acf-field-checkbox.php:134
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""
"Anpassade värden för kryssrutor kan inte vara tomma. Avmarkera alla tomma "
"värden."

#: includes/admin/views/global/navigation.php:253
msgid "Updates"
msgstr "Uppdateringar"

#: includes/admin/views/global/navigation.php:177
#: includes/admin/views/global/navigation.php:181
msgid "Advanced Custom Fields logo"
msgstr "Logga för Advanced Custom Fields"

#: includes/admin/views/global/form-top.php:92
msgid "Save Changes"
msgstr "Spara ändringarna"

#: includes/admin/views/global/form-top.php:79
msgid "Field Group Title"
msgstr "Rubrik för fältgrupp"

#: includes/admin/views/acf-post-type/advanced-settings.php:709
#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr "Lägg till rubrik"

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:30
#: includes/admin/views/acf-post-type/list-empty.php:20
#: includes/admin/views/acf-taxonomy/list-empty.php:21
#: includes/admin/views/options-page-preview.php:13
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""
"Har du just börjat med ACF? Kolla gärna in vår <a href=\"%s\" "
"target=\"_blank\">välkomstguide</a>."

#: includes/admin/views/acf-field-group/list-empty.php:24
msgid "Add Field Group"
msgstr "Lägg till fältgrupp"

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:18
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""
"ACF samlar anpassade fält i <a href=\"%s\" target=\"_blank\">fältgrupper</a> "
"och kopplar sedan dessa fält till redigeringsvyer."

#: includes/admin/views/acf-field-group/list-empty.php:12
msgid "Add Your First Field Group"
msgstr "Lägg till din första fältgrupp"

#: includes/admin/admin-options-pages-preview.php:28
#: includes/admin/views/acf-field-group/pro-features.php:58
#: includes/admin/views/global/navigation.php:86
#: includes/admin/views/global/navigation.php:255
msgid "Options Pages"
msgstr "Alternativsidor"

#: includes/admin/views/acf-field-group/pro-features.php:54
msgid "ACF Blocks"
msgstr "ACF-block"

#: includes/admin/views/acf-field-group/pro-features.php:62
msgid "Gallery Field"
msgstr "Gallerifält"

#: includes/admin/views/acf-field-group/pro-features.php:42
msgid "Flexible Content Field"
msgstr "Flexibelt innehållsfält"

#: includes/admin/views/acf-field-group/pro-features.php:46
msgid "Repeater Field"
msgstr "Upprepningsfält"

#: includes/admin/views/global/navigation.php:215
msgid "Unlock Extra Features with ACF PRO"
msgstr "Lås upp extra funktioner med ACF PRO"

#: includes/admin/views/acf-field-group/options.php:267
msgid "Delete Field Group"
msgstr "Ta bort fältgrupp"

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:261
msgid "Created on %1$s at %2$s"
msgstr "Skapad den %1$s kl. %2$s"

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr "Gruppinställningar"

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr "Platsregler"

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:73
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""
"Välj från över 30 fälttyper. <a href=\"%s\" target=\"_blank\">Lär dig mer</"
"a>."

#: includes/admin/views/acf-field-group/fields.php:65
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""
"Kom igång med att skapa nya anpassade fält för dina inlägg, sidor, anpassade "
"inläggstyper och annat WordPress-innehåll."

#: includes/admin/views/acf-field-group/fields.php:64
msgid "Add Your First Field"
msgstr "Lägg till ditt första fält"

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:43
msgid "#"
msgstr "#"

#: includes/admin/views/acf-field-group/fields.php:33
#: includes/admin/views/acf-field-group/fields.php:67
#: includes/admin/views/acf-field-group/fields.php:101
#: includes/admin/views/global/form-top.php:88
msgid "Add Field"
msgstr "Lägg till fält"

#: includes/acf-field-group-functions.php:496 includes/fields.php:384
msgid "Presentation"
msgstr "Presentation"

#: includes/fields.php:383
msgid "Validation"
msgstr "Validering"

#: includes/acf-internal-post-type-functions.php:504
#: includes/acf-internal-post-type-functions.php:533 includes/fields.php:382
msgid "General"
msgstr "Allmänt"

#: includes/admin/tools/class-acf-admin-tool-import.php:67
msgid "Import JSON"
msgstr "Importera JSON"

#: includes/admin/tools/class-acf-admin-tool-export.php:338
msgid "Export As JSON"
msgstr "Exportera som JSON"

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:360
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] "Fältgrupp inaktiverad."
msgstr[1] "%s fältgrupper inaktiverade."

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:353
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] "Fältgrupp aktiverad."
msgstr[1] "%s fältgrupper aktiverade."

#: includes/admin/admin-internal-post-type-list.php:470
#: includes/admin/admin-internal-post-type-list.php:496
msgid "Deactivate"
msgstr "Inaktivera"

#: includes/admin/admin-internal-post-type-list.php:470
msgid "Deactivate this item"
msgstr "Inaktivera detta objekt"

#: includes/admin/admin-internal-post-type-list.php:466
#: includes/admin/admin-internal-post-type-list.php:495
msgid "Activate"
msgstr "Aktivera"

#: includes/admin/admin-internal-post-type-list.php:466
msgid "Activate this item"
msgstr "Aktivera detta objekt"

#: includes/admin/post-types/admin-field-group.php:88
#: assets/build/js/acf-field-group.js:2862
#: assets/build/js/acf-field-group.js:3375
msgid "Move field group to trash?"
msgstr "Flytta fältgrupp till papperskorg?"

#: acf.php:500 includes/admin/admin-internal-post-type-list.php:264
#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Inactive"
msgstr "Inaktiv"

#. Author of the plugin
#: acf.php
msgid "WP Engine"
msgstr "WP Engine"

#: acf.php:558
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"Advanced Custom Fields och Advanced Custom Fields PRO ska inte vara aktiva "
"samtidigt. Vi har inaktiverat Advanced Custom Fields PRO automatiskt."

#: acf.php:556
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""
"Advanced Custom Fields och Advanced Custom Fields PRO ska inte vara aktiva "
"samtidigt. Vi har inaktiverat Advanced Custom Fields automatiskt."

#. translators: %1 plugin name, %2 the URL to the documentation on this error
#: includes/acf-value-functions.php:376
msgid ""
"<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
"field values before ACF has been initialized. This is not supported and can "
"result in malformed or missing data. <a href=\"%2$s\" "
"target=\"_blank\">Learn how to fix this</a>."
msgstr ""
"<strong>%1$s</strong> – Vi har upptäckt ett eller flera anrop för att hämta "
"ACF-fältvärden innan ACF har initierats. Detta stöds inte och kan resultera "
"i felaktiga eller saknade data. <a href=\"%2$s\" target=\"_blank\">Lär dig "
"hur man åtgärdar detta</a>."

#: includes/fields/class-acf-field-user.php:578
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s måste ha en användare med rollen %2$s."
msgstr[1] "%1$s måste ha en användare med en av följande roller: %2$s"

#: includes/fields/class-acf-field-user.php:569
msgid "%1$s must have a valid user ID."
msgstr "%1$s måste ha ett giltigt användar-ID."

#: includes/fields/class-acf-field-user.php:408
msgid "Invalid request."
msgstr "Ogiltig begäran."

#: includes/fields/class-acf-field-select.php:637
msgid "%1$s is not one of %2$s"
msgstr "%1$s är inte en av %2$s"

#: includes/fields/class-acf-field-post_object.php:660
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s måste ha termen %2$s."
msgstr[1] "%1$s måste ha en av följande termer: %2$s"

#: includes/fields/class-acf-field-post_object.php:644
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s måste vara av inläggstypen %2$s."
msgstr[1] "%1$s måste vara en av följande inläggstyper: %2$s"

#: includes/fields/class-acf-field-post_object.php:635
msgid "%1$s must have a valid post ID."
msgstr "%1$s måste ha ett giltigt inläggs-ID."

#: includes/fields/class-acf-field-file.php:447
msgid "%s requires a valid attachment ID."
msgstr "%s kräver ett giltig bilage-ID."

#: includes/admin/views/acf-field-group/options.php:233
msgid "Show in REST API"
msgstr "Visa i REST API"

#: includes/fields/class-acf-field-color_picker.php:156
msgid "Enable Transparency"
msgstr "Aktivera genomskinlighet"

#: includes/fields/class-acf-field-color_picker.php:175
msgid "RGBA Array"
msgstr "RGBA-array"

#: includes/fields/class-acf-field-color_picker.php:92
msgid "RGBA String"
msgstr "RGBA-sträng"

#: includes/fields/class-acf-field-color_picker.php:91
#: includes/fields/class-acf-field-color_picker.php:174
msgid "Hex String"
msgstr "HEX-sträng"

#: includes/admin/views/browse-fields-modal.php:12
msgid "Upgrade to PRO"
msgstr "Uppgradera till PRO"

#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Active"
msgstr "Aktivt"

#: includes/fields/class-acf-field-email.php:166
msgid "'%s' is not a valid email address"
msgstr "”%s” är inte en giltig e-postadress"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Color value"
msgstr "Färgvärde"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Select default color"
msgstr "Välj standardfärg"

#: includes/fields/class-acf-field-color_picker.php:66
msgid "Clear color"
msgstr "Rensa färg"

#: includes/acf-wp-functions.php:90
msgid "Blocks"
msgstr "Block"

#: includes/acf-wp-functions.php:86
msgid "Options"
msgstr "Alternativ"

#: includes/acf-wp-functions.php:82
msgid "Users"
msgstr "Användare"

#: includes/acf-wp-functions.php:78
msgid "Menu items"
msgstr "Menyval"

#: includes/acf-wp-functions.php:70
msgid "Widgets"
msgstr "Widgetar"

#: includes/acf-wp-functions.php:62
msgid "Attachments"
msgstr "Bilagor"

#: includes/acf-wp-functions.php:57
#: includes/admin/post-types/admin-post-types.php:112
#: includes/admin/post-types/admin-taxonomies.php:86
#: includes/admin/tools/class-acf-admin-tool-import.php:90
#: includes/admin/views/acf-post-type/basic-settings.php:86
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr "Taxonomier"

#: includes/acf-wp-functions.php:44
#: includes/admin/post-types/admin-post-type.php:124
#: includes/admin/post-types/admin-post-types.php:114
#: includes/admin/views/acf-post-type/advanced-settings.php:106
#: assets/build/js/acf-internal-post-type.js:173
#: assets/build/js/acf-internal-post-type.js:247
msgid "Posts"
msgstr "Inlägg"

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Last updated: %s"
msgstr "Senast uppdaterad: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:75
msgid "Sorry, this post is unavailable for diff comparison."
msgstr "Detta inlägg är inte tillgängligt för diff-jämförelse."

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid field group parameter(s)."
msgstr "Ogiltiga parametrar för fältgrupp."

#: includes/admin/admin-internal-post-type-list.php:429
msgid "Awaiting save"
msgstr "Väntar på att sparas"

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Saved"
msgstr "Sparad"

#: includes/admin/admin-internal-post-type-list.php:422
#: includes/admin/tools/class-acf-admin-tool-import.php:46
msgid "Import"
msgstr "Importera"

#: includes/admin/admin-internal-post-type-list.php:418
msgid "Review changes"
msgstr "Granska ändringar"

#: includes/admin/admin-internal-post-type-list.php:394
msgid "Located in: %s"
msgstr "Finns i: %s"

#: includes/admin/admin-internal-post-type-list.php:391
msgid "Located in plugin: %s"
msgstr "Finns i tillägg: %s"

#: includes/admin/admin-internal-post-type-list.php:388
msgid "Located in theme: %s"
msgstr "Finns i tema: %s"

#: includes/admin/post-types/admin-field-groups.php:235
msgid "Various"
msgstr "Diverse"

#: includes/admin/admin-internal-post-type-list.php:230
#: includes/admin/admin-internal-post-type-list.php:503
msgid "Sync changes"
msgstr "Synkronisera ändringar"

#: includes/admin/admin-internal-post-type-list.php:229
msgid "Loading diff"
msgstr "Hämtar diff"

#: includes/admin/admin-internal-post-type-list.php:228
msgid "Review local JSON changes"
msgstr "Granska lokala JSON-ändringar"

#: includes/admin/admin.php:174
msgid "Visit website"
msgstr "Besök webbplats"

#: includes/admin/admin.php:173
msgid "View details"
msgstr "Visa detaljer"

#: includes/admin/admin.php:172
msgid "Version %s"
msgstr "Version %s"

#: includes/admin/admin.php:171
msgid "Information"
msgstr "Information"

#: includes/admin/admin.php:162
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Support</a>. Vår professionella "
"supportpersonal kan hjälpa dig med mer komplicerade och tekniska utmaningar."

#: includes/admin/admin.php:158
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Diskussioner</a>. Vi har en aktiv och "
"vänlig community på våra community-forum som kanske kan hjälpa dig att räkna "
"ut ”hur man gör” i ACF-världen."

#: includes/admin/admin.php:154
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Dokumentation</a>. Vår omfattande "
"dokumentation innehåller referenser och guider för de flesta situationer du "
"kan stöta på."

#: includes/admin/admin.php:151
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"Vi är fanatiska när det gäller support och vill att du ska få ut det bästa "
"av din webbplats med ACF. Om du stöter på några svårigheter finns det flera "
"ställen där du kan få hjälp:"

#: includes/admin/admin.php:148 includes/admin/admin.php:150
msgid "Help & Support"
msgstr "Hjälp och support"

#: includes/admin/admin.php:139
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"Använd fliken ”Hjälp och support” för att kontakta oss om du skulle behöva "
"hjälp."

#: includes/admin/admin.php:136
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"Innan du skapar din första fältgrupp rekommenderar vi att du först läser vår "
"<a href=\"%s\" target=\"_blank\">Komma igång</a>-guide för att bekanta dig "
"med tilläggets filosofi och bästa praxis."

#: includes/admin/admin.php:134
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"Tillägget ”Advanced Custom Fields” tillhandahåller en visuell "
"formulärbyggare för att anpassa WordPress redigeringsvyer med extra fält och "
"ett intuitivt API för att visa anpassade fältvärden i alla temamallsfiler."

#: includes/admin/admin.php:131 includes/admin/admin.php:133
msgid "Overview"
msgstr "Översikt"

#. translators: %s the name of the location type
#: includes/locations.php:38
msgid "Location type \"%s\" is already registered."
msgstr "Platstypen ”%s” är redan registrerad."

#. translators: %s class name for a location that could not be found
#: includes/locations.php:26
msgid "Class \"%s\" does not exist."
msgstr "Klassen ”%s” finns inte."

#: includes/ajax/class-acf-ajax-query-users.php:41
#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Ogiltig engångskod."

#: includes/fields/class-acf-field-user.php:400
msgid "Error loading field."
msgstr "Fel vid inläsning av fält."

#: assets/build/js/acf-input.js:3439 assets/build/js/acf-input.js:3508
#: assets/build/js/acf-input.js:3687 assets/build/js/acf-input.js:3761
msgid "Location not found: %s"
msgstr "Plats hittades inte: %s"

#: includes/forms/form-user.php:328
msgid "<strong>Error</strong>: %s"
msgstr "<strong>Fel</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Widget"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Användarroll"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Kommentera"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Inläggsformat"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Menyval"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Inläggsstatus"

#: includes/acf-wp-functions.php:74
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Menyer"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Menyplatser"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Meny"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Inläggstaxonomi"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Undersida (har överordnad)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Överordnad sida (har undersidor)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Toppnivåsida (ingen överordnad)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Inläggssida"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Startsida"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Sidtyp"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Visar back-end"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Visar front-end"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Inloggad"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Nuvarande användare"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Sidmall"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Registrera"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Lägg till/redigera"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Användarformulär"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Överordnad sida"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Superadmin"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Nuvarande användarroll"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Standardmall"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Inläggsmall"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Inläggskategori"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Alla %s-format"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Bilaga"

#: includes/validation.php:313
msgid "%s value is required"
msgstr "%s-värde är obligatoriskt"

#: includes/admin/views/acf-field-group/conditional-logic.php:64
msgid "Show this field if"
msgstr "Visa detta fält om"

#: includes/admin/views/acf-field-group/conditional-logic.php:25
#: includes/admin/views/acf-field-group/field.php:122 includes/fields.php:385
msgid "Conditional Logic"
msgstr "Villkorad logik"

#: includes/admin/views/acf-field-group/conditional-logic.php:169
#: includes/admin/views/acf-field-group/location-rule.php:84
msgid "and"
msgstr "och"

#: includes/admin/post-types/admin-field-groups.php:97
#: includes/admin/post-types/admin-post-types.php:118
#: includes/admin/post-types/admin-taxonomies.php:117
msgid "Local JSON"
msgstr "Lokal JSON"

#: includes/admin/views/acf-field-group/pro-features.php:50
msgid "Clone Field"
msgstr "Klona fält"

#. translators: %s a list of plugin
#: includes/admin/views/upgrade/notice.php:32
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Kontrollera också att alla premiumutökningar (%s) är uppdaterade till den "
"senaste versionen."

#: includes/admin/views/upgrade/notice.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Denna version innehåller förbättringar av din databas och kräver en "
"uppgradering."

#. translators: %1 plugin name, %2 version number
#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "Tack för att du uppdaterade till %1$s v%2$s!"

#: includes/admin/views/upgrade/notice.php:26
msgid "Database Upgrade Required"
msgstr "Databasuppgradering krävs"

#: includes/admin/post-types/admin-field-group.php:159
#: includes/admin/views/upgrade/notice.php:17
msgid "Options Page"
msgstr "Alternativsida"

#: includes/admin/views/upgrade/notice.php:14 includes/fields.php:436
msgid "Gallery"
msgstr "Galleri"

#: includes/admin/views/upgrade/notice.php:11 includes/fields.php:426
msgid "Flexible Content"
msgstr "Flexibelt innehåll"

#: includes/admin/views/upgrade/notice.php:8 includes/fields.php:446
msgid "Repeater"
msgstr "Repeterare"

#: includes/admin/views/tools/tools.php:16
msgid "Back to all tools"
msgstr "Tillbaka till alla verktyg"

#: includes/admin/views/acf-field-group/options.php:195
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Om flera fältgrupper visas på en redigeringssida, kommer den första "
"fältgruppens alternativ att användas (den med lägsta sorteringsnummer)"

#: includes/admin/views/acf-field-group/options.php:195
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>Markera</b> objekt för att <b>dölja</b> dem från redigeringsvyn."

#: includes/admin/views/acf-field-group/options.php:194
msgid "Hide on screen"
msgstr "Dölj på skärmen"

#: includes/admin/views/acf-field-group/options.php:186
msgid "Send Trackbacks"
msgstr "Skicka trackbacks"

#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/admin/views/acf-field-group/options.php:185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:159
#: assets/build/js/acf-internal-post-type.js:180
#: assets/build/js/acf-internal-post-type.js:254
msgid "Tags"
msgstr "Etiketter"

#: includes/admin/post-types/admin-taxonomy.php:128
#: includes/admin/views/acf-field-group/options.php:184
#: assets/build/js/acf-internal-post-type.js:183
#: assets/build/js/acf-internal-post-type.js:257
msgid "Categories"
msgstr "Kategorier"

#: includes/admin/views/acf-field-group/options.php:182
#: includes/admin/views/acf-post-type/advanced-settings.php:28
msgid "Page Attributes"
msgstr "Sidattribut"

#: includes/admin/views/acf-field-group/options.php:181
msgid "Format"
msgstr "Format"

#: includes/admin/views/acf-field-group/options.php:180
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Author"
msgstr "Författare"

#: includes/admin/views/acf-field-group/options.php:179
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/acf-field-group/options.php:178
#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Revisions"
msgstr "Versioner"

#: includes/acf-wp-functions.php:66
#: includes/admin/views/acf-field-group/options.php:177
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Comments"
msgstr "Kommentarer"

#: includes/admin/views/acf-field-group/options.php:176
msgid "Discussion"
msgstr "Diskussion"

#: includes/admin/views/acf-field-group/options.php:174
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Excerpt"
msgstr "Utdrag"

#: includes/admin/views/acf-field-group/options.php:173
msgid "Content Editor"
msgstr "Innehållsredigerare"

#: includes/admin/views/acf-field-group/options.php:172
msgid "Permalink"
msgstr "Permalänk"

#: includes/admin/views/acf-field-group/options.php:250
msgid "Shown in field group list"
msgstr "Visa i fältgrupplista"

#: includes/admin/views/acf-field-group/options.php:157
msgid "Field groups with a lower order will appear first"
msgstr "Fältgrupper med lägre ordningsnummer kommer synas först"

#: includes/admin/views/acf-field-group/options.php:156
msgid "Order No."
msgstr "Sorteringsnummer"

#: includes/admin/views/acf-field-group/options.php:147
msgid "Below fields"
msgstr "Under fält"

#: includes/admin/views/acf-field-group/options.php:146
msgid "Below labels"
msgstr "Under etiketter"

#: includes/admin/views/acf-field-group/options.php:139
msgid "Instruction Placement"
msgstr "Instruktionsplacering"

#: includes/admin/views/acf-field-group/options.php:122
msgid "Label Placement"
msgstr "Etikettplacering"

#: includes/admin/views/acf-field-group/options.php:110
msgid "Side"
msgstr "Vid sidan"

#: includes/admin/views/acf-field-group/options.php:109
msgid "Normal (after content)"
msgstr "Normal (efter innehåll)"

#: includes/admin/views/acf-field-group/options.php:108
msgid "High (after title)"
msgstr "Hög (efter rubrik)"

#: includes/admin/views/acf-field-group/options.php:101
msgid "Position"
msgstr "Position"

#: includes/admin/views/acf-field-group/options.php:92
msgid "Seamless (no metabox)"
msgstr "Sömnlöst (ingen metaruta)"

#: includes/admin/views/acf-field-group/options.php:91
msgid "Standard (WP metabox)"
msgstr "Standard (WP meta-ruta)"

#: includes/admin/views/acf-field-group/options.php:84
msgid "Style"
msgstr "Stil"

#: includes/admin/views/acf-field-group/fields.php:55
msgid "Type"
msgstr "Typ"

#: includes/admin/post-types/admin-field-groups.php:91
#: includes/admin/post-types/admin-post-types.php:111
#: includes/admin/post-types/admin-taxonomies.php:110
#: includes/admin/views/acf-field-group/fields.php:54
msgid "Key"
msgstr "Nyckel"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:48
msgid "Order"
msgstr "Sortering"

#: includes/admin/views/acf-field-group/field.php:321
msgid "Close Field"
msgstr "Stäng fält"

#: includes/admin/views/acf-field-group/field.php:252
msgid "id"
msgstr "id"

#: includes/admin/views/acf-field-group/field.php:236
msgid "class"
msgstr "klass"

#: includes/admin/views/acf-field-group/field.php:278
msgid "width"
msgstr "bredd"

#: includes/admin/views/acf-field-group/field.php:272
msgid "Wrapper Attributes"
msgstr "Omslagsattribut"

#: includes/fields/class-acf-field.php:311
msgid "Required"
msgstr "Obligatoriskt"

#: includes/admin/views/acf-field-group/field.php:219
msgid "Instructions"
msgstr "Instruktioner"

#: includes/admin/views/acf-field-group/field.php:142
msgid "Field Type"
msgstr "Fälttyp"

#: includes/admin/views/acf-field-group/field.php:183
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Enstaka ord, inga mellanslag. Understreck och bindestreck tillåtna"

#: includes/admin/views/acf-field-group/field.php:182
msgid "Field Name"
msgstr "Fältnamn"

#: includes/admin/views/acf-field-group/field.php:170
msgid "This is the name which will appear on the EDIT page"
msgstr "Detta är namnet som kommer att visas på REDIGERINGS-sidan"

#: includes/admin/views/acf-field-group/field.php:169
#: includes/admin/views/browse-fields-modal.php:69
msgid "Field Label"
msgstr "Fältetikett"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete"
msgstr "Ta bort"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete field"
msgstr "Ta bort fält"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move"
msgstr "Flytta"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move field to another group"
msgstr "Flytta fältet till en annan grupp"

#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate field"
msgstr "Duplicera fält"

#: includes/admin/views/acf-field-group/field.php:86
#: includes/admin/views/acf-field-group/field.php:89
msgid "Edit field"
msgstr "Redigera fält"

#: includes/admin/views/acf-field-group/field.php:82
msgid "Drag to reorder"
msgstr "Dra för att sortera om"

#: includes/admin/post-types/admin-field-group.php:99
#: includes/admin/views/acf-field-group/location-group.php:3
#: assets/build/js/acf-field-group.js:2387
#: assets/build/js/acf-field-group.js:2812
msgid "Show this field group if"
msgstr "Visa denna fältgrupp om"

#: includes/admin/views/upgrade/upgrade.php:93
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "Inga uppdateringar tillgängliga."

#. translators: %s the url to the field group page.
#: includes/admin/views/upgrade/upgrade.php:32
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr "Databasuppgradering slutförd. <a href=\"%s\">Se vad som är nytt</a>"

#: includes/admin/views/upgrade/upgrade.php:27
msgid "Reading upgrade tasks..."
msgstr "Läser in uppgraderingsuppgifter …"

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:64
msgid "Upgrade failed."
msgstr "Uppgradering misslyckades."

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr "Uppgradering slutförd."

#. translators: %s the version being upgraded to.
#. translators: %s the new ACF version
#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:29
msgid "Upgrading data to version %s"
msgstr "Uppgraderar data till version %s"

#: includes/admin/views/upgrade/network.php:120
#: includes/admin/views/upgrade/notice.php:46
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Det rekommenderas starkt att du säkerhetskopierar din databas innan du "
"fortsätter. Är du säker på att du vill köra uppdateraren nu?"

#: includes/admin/views/upgrade/network.php:116
msgid "Please select at least one site to upgrade."
msgstr "Välj minst en webbplats att uppgradera."

#. translators: %s admin dashboard url page
#: includes/admin/views/upgrade/network.php:96
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Uppgradering av databas slutförd. <a href=\"%s\">Tillbaka till nätverkets "
"adminpanel</a>"

#: includes/admin/views/upgrade/network.php:79
msgid "Site is up to date"
msgstr "Webbplatsen är uppdaterad"

#. translators: %1 current db version, %2 available db version
#: includes/admin/views/upgrade/network.php:77
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "Webbplatsen kräver databasuppgradering från %1$s till %2$s"

#: includes/admin/views/upgrade/network.php:34
#: includes/admin/views/upgrade/network.php:45
msgid "Site"
msgstr "Webbplats"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/network.php:25
#: includes/admin/views/upgrade/network.php:94
msgid "Upgrade Sites"
msgstr "Uppgradera webbplatser"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Följande webbplatser kräver en DB-uppgradering. Kontrollera de du vill "
"uppdatera och klicka sedan på %s."

#: includes/admin/views/acf-field-group/conditional-logic.php:184
#: includes/admin/views/acf-field-group/locations.php:37
msgid "Add rule group"
msgstr "Lägg till regelgrupp"

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Skapa en uppsättning regler för att avgöra vilka redigeringsvyer som ska "
"använda dessa avancerade anpassade fält"

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "Regler"

#: includes/admin/tools/class-acf-admin-tool-export.php:449
msgid "Copied"
msgstr "Kopierad"

#: includes/admin/tools/class-acf-admin-tool-export.php:425
msgid "Copy to clipboard"
msgstr "Kopiera till urklipp"

#: includes/admin/tools/class-acf-admin-tool-export.php:331
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""
"Välj vilka objekt du vill exportera och sedan exportmetod. ”Exportera som "
"JSON” för att exportera till en .json-fil som du sedan kan importera till "
"någon annan ACF-installation. ”Generera PHP” för att exportera PHP kod som "
"du kan lägga till i ditt tema."

#: includes/admin/tools/class-acf-admin-tool-export.php:215
msgid "Select Field Groups"
msgstr "Välj fältgrupper"

#: includes/admin/tools/class-acf-admin-tool-export.php:88
#: includes/admin/tools/class-acf-admin-tool-export.php:121
msgid "No field groups selected"
msgstr "Inga fältgrupper valda"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:339
#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid "Generate PHP"
msgstr "Generera PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:34
msgid "Export Field Groups"
msgstr "Exportera fältgrupper"

#: includes/admin/tools/class-acf-admin-tool-import.php:172
msgid "Import file empty"
msgstr "Importerad fil är tom"

#: includes/admin/tools/class-acf-admin-tool-import.php:163
msgid "Incorrect file type"
msgstr "Felaktig filtyp"

#: includes/admin/tools/class-acf-admin-tool-import.php:158
msgid "Error uploading file. Please try again"
msgstr "Fel vid uppladdning av fil. Försök igen"

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""
"Välj den JSON-fil för Advanced Custom Fields som du vill importera. När du "
"klickar på importknappen nedan kommer ACF att importera objekten i den filen."

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Importera fältgrupper"

#: includes/admin/admin-internal-post-type-list.php:417
msgid "Sync"
msgstr "Synkronisera"

#. translators: %s: field group title
#: includes/admin/admin-internal-post-type-list.php:960
msgid "Select %s"
msgstr "Välj %s"

#: includes/admin/admin-internal-post-type-list.php:460
#: includes/admin/admin-internal-post-type-list.php:492
#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate"
msgstr "Duplicera"

#: includes/admin/admin-internal-post-type-list.php:460
msgid "Duplicate this item"
msgstr "Duplicera detta objekt"

#: includes/admin/views/acf-post-type/advanced-settings.php:41
msgid "Supports"
msgstr "Stöder"

#: includes/admin/admin.php:346
#: includes/admin/views/browse-fields-modal.php:102
msgid "Documentation"
msgstr "Dokumentation"

#: includes/admin/post-types/admin-field-groups.php:90
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/post-types/admin-taxonomies.php:109
#: includes/admin/views/acf-field-group/options.php:249
#: includes/admin/views/acf-post-type/advanced-settings.php:62
#: includes/admin/views/acf-taxonomy/advanced-settings.php:114
#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Description"
msgstr "Beskrivning"

#: includes/admin/admin-internal-post-type-list.php:414
#: includes/admin/admin-internal-post-type-list.php:832
msgid "Sync available"
msgstr "Synkronisering tillgänglig"

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:374
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] "Fältgrupp synkroniserad."
msgstr[1] "%s fältgrupper synkroniserade."

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:367
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Fältgrupp duplicerad."
msgstr[1] "%s fältgrupper duplicerade."

#: includes/admin/admin-internal-post-type-list.php:155
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Aktiv <span class=\"count\">(%s)</span>"
msgstr[1] "Aktiva <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:251
msgid "Review sites & upgrade"
msgstr "Granska webbplatser och uppgradera"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:90
#: includes/admin/admin-upgrade.php:91 includes/admin/admin-upgrade.php:227
#: includes/admin/views/upgrade/network.php:21
#: includes/admin/views/upgrade/upgrade.php:23
msgid "Upgrade Database"
msgstr "Uppgradera databas"

#: includes/admin/views/acf-field-group/options.php:175
#: includes/admin/views/acf-post-type/advanced-settings.php:30
msgid "Custom Fields"
msgstr "Anpassade fält"

#: includes/admin/post-types/admin-field-group.php:609
msgid "Move Field"
msgstr "Flytta fält"

#: includes/admin/post-types/admin-field-group.php:602
#: includes/admin/post-types/admin-field-group.php:606
msgid "Please select the destination for this field"
msgstr "Välj destinationen för detta fält"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:568
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "Fältet %1$s kan nu hittas i fältgruppen %2$s"

#: includes/admin/post-types/admin-field-group.php:565
msgid "Move Complete."
msgstr "Flytt färdig."

#: includes/admin/views/acf-field-group/field.php:52
#: includes/admin/views/acf-field-group/options.php:217
#: includes/admin/views/acf-post-type/advanced-settings.php:78
#: includes/admin/views/acf-taxonomy/advanced-settings.php:130
msgid "Active"
msgstr "Aktiv"

#: includes/admin/post-types/admin-field-group.php:276
msgid "Field Keys"
msgstr "Fältnycklar"

#: includes/admin/post-types/admin-field-group.php:180
msgid "Settings"
msgstr "Inställningar"

#: includes/admin/post-types/admin-field-groups.php:92
msgid "Location"
msgstr "Plats"

#: includes/admin/post-types/admin-field-group.php:100
#: assets/build/js/acf-input.js:1689 assets/build/js/acf-input.js:1851
msgid "Null"
msgstr "Null"

#: includes/admin/post-types/admin-field-group.php:97
#: includes/class-acf-internal-post-type.php:728
#: includes/post-types/class-acf-field-group.php:345
#: assets/build/js/acf-field-group.js:1541
#: assets/build/js/acf-field-group.js:1860
msgid "copy"
msgstr "kopiera"

#: includes/admin/post-types/admin-field-group.php:96
#: assets/build/js/acf-field-group.js:627
#: assets/build/js/acf-field-group.js:782
msgid "(this field)"
msgstr "(detta fält)"

#: includes/admin/post-types/admin-field-group.php:94
#: assets/build/js/acf-input.js:1630 assets/build/js/acf-input.js:1652
#: assets/build/js/acf-input.js:1784 assets/build/js/acf-input.js:1809
msgid "Checked"
msgstr "Ikryssad"

#: includes/admin/post-types/admin-field-group.php:90
#: assets/build/js/acf-field-group.js:1646
#: assets/build/js/acf-field-group.js:1972
msgid "Move Custom Field"
msgstr "Flytta anpassat fält"

#: includes/admin/post-types/admin-field-group.php:89
#: assets/build/js/acf-field-group.js:653
#: assets/build/js/acf-field-group.js:808
msgid "No toggle fields available"
msgstr "Inga fält för att slå på/av är tillgängliga"

#: includes/admin/post-types/admin-field-group.php:87
msgid "Field group title is required"
msgstr "Rubrik för fältgrupp är obligatoriskt"

#: includes/admin/post-types/admin-field-group.php:86
#: assets/build/js/acf-field-group.js:1635
#: assets/build/js/acf-field-group.js:1958
msgid "This field cannot be moved until its changes have been saved"
msgstr "Detta fält kan inte flyttas innan dess ändringar har sparats"

#: includes/admin/post-types/admin-field-group.php:85
#: assets/build/js/acf-field-group.js:1445
#: assets/build/js/acf-field-group.js:1755
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Strängen ”field_” får inte användas i början av ett fältnamn"

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group draft updated."
msgstr "Fältgruppsutkast uppdaterat."

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group scheduled for."
msgstr "Fältgrupp schemalagd för."

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group submitted."
msgstr "Fältgrupp skickad."

#: includes/admin/post-types/admin-field-group.php:66
msgid "Field group saved."
msgstr "Fältgrupp sparad."

#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group published."
msgstr "Fältgrupp publicerad."

#: includes/admin/post-types/admin-field-group.php:62
msgid "Field group deleted."
msgstr "Fältgrupp borttagen."

#: includes/admin/post-types/admin-field-group.php:60
#: includes/admin/post-types/admin-field-group.php:61
#: includes/admin/post-types/admin-field-group.php:63
msgid "Field group updated."
msgstr "Fältgrupp uppdaterad."

#: includes/admin/admin-tools.php:107
#: includes/admin/views/global/navigation.php:251
#: includes/admin/views/tools/tools.php:13
msgid "Tools"
msgstr "Verktyg"

#: includes/locations/abstract-acf-location.php:105
msgid "is not equal to"
msgstr "är inte lika med"

#: includes/locations/abstract-acf-location.php:104
msgid "is equal to"
msgstr "är lika med"

#: includes/locations.php:104
msgid "Forms"
msgstr "Formulär"

#: includes/admin/post-types/admin-post-type.php:125 includes/locations.php:102
#: includes/locations/class-acf-location-page.php:22
#: assets/build/js/acf-internal-post-type.js:175
#: assets/build/js/acf-internal-post-type.js:249
msgid "Page"
msgstr "Sida"

#: includes/admin/post-types/admin-post-type.php:123 includes/locations.php:101
#: includes/locations/class-acf-location-post.php:22
#: assets/build/js/acf-internal-post-type.js:172
#: assets/build/js/acf-internal-post-type.js:246
msgid "Post"
msgstr "Inlägg"

#: includes/fields.php:328
msgid "Relational"
msgstr "Relationellt"

#: includes/fields.php:327
msgid "Choice"
msgstr "Val"

#: includes/fields.php:325
msgid "Basic"
msgstr "Grundläggande"

#: includes/fields.php:276
msgid "Unknown"
msgstr "Okänt"

#: includes/fields.php:276
msgid "Field type does not exist"
msgstr "Fälttyp finns inte"

#: includes/forms/form-front.php:217
msgid "Spam Detected"
msgstr "Skräppost upptäckt"

#: includes/forms/form-front.php:100
msgid "Post updated"
msgstr "Inlägg uppdaterat"

#: includes/forms/form-front.php:99
msgid "Update"
msgstr "Uppdatera"

#: includes/forms/form-front.php:54
msgid "Validate Email"
msgstr "Validera e-post"

#: includes/fields.php:326 includes/forms/form-front.php:46
msgid "Content"
msgstr "Innehåll"

#: includes/admin/views/acf-post-type/advanced-settings.php:21
#: includes/forms/form-front.php:37
msgid "Title"
msgstr "Rubrik"

#: includes/assets.php:376 includes/forms/form-comment.php:140
#: assets/build/js/acf-input.js:8414 assets/build/js/acf-input.js:9186
msgid "Edit field group"
msgstr "Redigera fältgrupp"

#: includes/admin/post-types/admin-field-group.php:113
#: assets/build/js/acf-input.js:1816 assets/build/js/acf-input.js:1991
msgid "Selection is less than"
msgstr "Valet är mindre än"

#: includes/admin/post-types/admin-field-group.php:112
#: assets/build/js/acf-input.js:1800 assets/build/js/acf-input.js:1966
msgid "Selection is greater than"
msgstr "Valet är större än"

#: includes/admin/post-types/admin-field-group.php:111
#: assets/build/js/acf-input.js:1772 assets/build/js/acf-input.js:1937
msgid "Value is less than"
msgstr "Värde är mindre än"

#: includes/admin/post-types/admin-field-group.php:110
#: assets/build/js/acf-input.js:1745 assets/build/js/acf-input.js:1909
msgid "Value is greater than"
msgstr "Värde är större än"

#: includes/admin/post-types/admin-field-group.php:109
#: assets/build/js/acf-input.js:1603 assets/build/js/acf-input.js:1745
msgid "Value contains"
msgstr "Värde innehåller"

#: includes/admin/post-types/admin-field-group.php:108
#: assets/build/js/acf-input.js:1580 assets/build/js/acf-input.js:1714
msgid "Value matches pattern"
msgstr "Värde matchar mönster"

#: includes/admin/post-types/admin-field-group.php:107
#: assets/build/js/acf-input.js:1561 assets/build/js/acf-input.js:1726
#: assets/build/js/acf-input.js:1694 assets/build/js/acf-input.js:1889
msgid "Value is not equal to"
msgstr "Värde är inte lika med"

#: includes/admin/post-types/admin-field-group.php:106
#: assets/build/js/acf-input.js:1534 assets/build/js/acf-input.js:1670
#: assets/build/js/acf-input.js:1658 assets/build/js/acf-input.js:1829
msgid "Value is equal to"
msgstr "Värde är lika med"

#: includes/admin/post-types/admin-field-group.php:105
#: assets/build/js/acf-input.js:1515 assets/build/js/acf-input.js:1638
msgid "Has no value"
msgstr "Har inget värde"

#: includes/admin/post-types/admin-field-group.php:104
#: assets/build/js/acf-input.js:1488 assets/build/js/acf-input.js:1587
msgid "Has any value"
msgstr "Har något värde"

#: includes/admin/admin-internal-post-type.php:337
#: includes/admin/views/browse-fields-modal.php:72 includes/assets.php:354
#: assets/build/js/acf.js:1570 assets/build/js/acf.js:1662
msgid "Cancel"
msgstr "Avbryt"

#: includes/assets.php:350 assets/build/js/acf.js:1744
#: assets/build/js/acf.js:1859
msgid "Are you sure?"
msgstr "Är du säker?"

#: includes/assets.php:370 assets/build/js/acf-input.js:10482
#: assets/build/js/acf-input.js:11532
msgid "%d fields require attention"
msgstr "%d fält kräver din uppmärksamhet"

#: includes/assets.php:369 assets/build/js/acf-input.js:10480
#: assets/build/js/acf-input.js:11530
msgid "1 field requires attention"
msgstr "1 fält kräver din uppmärksamhet"

#: includes/assets.php:368 includes/validation.php:247
#: includes/validation.php:255 assets/build/js/acf-input.js:10475
#: assets/build/js/acf-input.js:11525
msgid "Validation failed"
msgstr "Validering misslyckades"

#: includes/assets.php:367 assets/build/js/acf-input.js:10643
#: assets/build/js/acf-input.js:11703
msgid "Validation successful"
msgstr "Validering lyckades"

#: includes/media.php:54 assets/build/js/acf-input.js:8242
#: assets/build/js/acf-input.js:8990
msgid "Restricted"
msgstr "Begränsad"

#: includes/media.php:53 assets/build/js/acf-input.js:8057
#: assets/build/js/acf-input.js:8754
msgid "Collapse Details"
msgstr "Minimera detaljer"

#: includes/media.php:52 assets/build/js/acf-input.js:8057
#: assets/build/js/acf-input.js:8751
msgid "Expand Details"
msgstr "Expandera detaljer"

#: includes/admin/views/acf-post-type/advanced-settings.php:470
#: includes/media.php:51 assets/build/js/acf-input.js:7924
#: assets/build/js/acf-input.js:8599
msgid "Uploaded to this post"
msgstr "Uppladdat till detta inlägg"

#: includes/media.php:50 assets/build/js/acf-input.js:7963
#: assets/build/js/acf-input.js:8638
msgctxt "verb"
msgid "Update"
msgstr "Uppdatera"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Redigera"

#: includes/assets.php:364 assets/build/js/acf-input.js:10253
#: assets/build/js/acf-input.js:11297
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""
"De ändringar du gjort kommer att gå förlorade om du navigerar bort från "
"denna sida"

#: includes/api/api-helpers.php:2984
msgid "File type must be %s."
msgstr "Filtyp måste vara %s."

#: includes/admin/post-types/admin-field-group.php:98
#: includes/admin/views/acf-field-group/conditional-logic.php:64
#: includes/admin/views/acf-field-group/conditional-logic.php:182
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:35
#: includes/api/api-helpers.php:2981 assets/build/js/acf-field-group.js:781
#: assets/build/js/acf-field-group.js:2427
#: assets/build/js/acf-field-group.js:946
#: assets/build/js/acf-field-group.js:2859
msgid "or"
msgstr "eller"

#: includes/api/api-helpers.php:2957
msgid "File size must not exceed %s."
msgstr "Filstorleken får inte överskrida %s."

#: includes/api/api-helpers.php:2953
msgid "File size must be at least %s."
msgstr "Filstorlek måste vara lägst %s."

#: includes/api/api-helpers.php:2940
msgid "Image height must not exceed %dpx."
msgstr "Bildens höjd får inte överskrida %d px."

#: includes/api/api-helpers.php:2936
msgid "Image height must be at least %dpx."
msgstr "Bildens höjd måste vara åtminstone %d px."

#: includes/api/api-helpers.php:2924
msgid "Image width must not exceed %dpx."
msgstr "Bildens bredd får inte överskrida %d px."

#: includes/api/api-helpers.php:2920
msgid "Image width must be at least %dpx."
msgstr "Bildens bredd måste vara åtminstone %d px."

#: includes/api/api-helpers.php:1409 includes/api/api-term.php:140
msgid "(no title)"
msgstr "(ingen rubrik)"

#: includes/api/api-helpers.php:765
msgid "Full Size"
msgstr "Full storlek"

#: includes/api/api-helpers.php:730
msgid "Large"
msgstr "Stor"

#: includes/api/api-helpers.php:729
msgid "Medium"
msgstr "Medium"

#: includes/api/api-helpers.php:728
msgid "Thumbnail"
msgstr "Miniatyr"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:95
#: assets/build/js/acf-field-group.js:1090
#: assets/build/js/acf-field-group.js:1277
msgid "(no label)"
msgstr "(ingen etikett)"

#: includes/fields/class-acf-field-textarea.php:135
msgid "Sets the textarea height"
msgstr "Ställer in textområdets höjd"

#: includes/fields/class-acf-field-textarea.php:134
msgid "Rows"
msgstr "Rader"

#: includes/fields/class-acf-field-textarea.php:22
msgid "Text Area"
msgstr "Textområde"

#: includes/fields/class-acf-field-checkbox.php:421
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Förbered en extra kryssruta för att slå på/av alla val"

#: includes/fields/class-acf-field-checkbox.php:383
msgid "Save 'custom' values to the field's choices"
msgstr "Spara ”anpassade” värden till fältets val"

#: includes/fields/class-acf-field-checkbox.php:372
msgid "Allow 'custom' values to be added"
msgstr "Tillåt att ”anpassade” värden kan läggas till"

#: includes/fields/class-acf-field-checkbox.php:35
msgid "Add new choice"
msgstr "Lägg till nytt val"

#: includes/fields/class-acf-field-checkbox.php:157
msgid "Toggle All"
msgstr "Slå på/av alla"

#: includes/fields/class-acf-field-page_link.php:487
msgid "Allow Archives URLs"
msgstr "Tillåt arkiv-URL:er"

#: includes/fields/class-acf-field-page_link.php:196
msgid "Archives"
msgstr "Arkiv"

#: includes/fields/class-acf-field-page_link.php:22
msgid "Page Link"
msgstr "Sidlänk"

#: includes/fields/class-acf-field-taxonomy.php:881
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Lägg till"

#: includes/admin/views/acf-field-group/fields.php:53
#: includes/fields/class-acf-field-taxonomy.php:851
msgid "Name"
msgstr "Namn"

#: includes/fields/class-acf-field-taxonomy.php:836
msgid "%s added"
msgstr "%s har lagts till"

#: includes/fields/class-acf-field-taxonomy.php:800
msgid "%s already exists"
msgstr "%s finns redan"

#: includes/fields/class-acf-field-taxonomy.php:788
msgid "User unable to add new %s"
msgstr "Användare kan inte lägga till ny %s"

#: includes/fields/class-acf-field-taxonomy.php:675
msgid "Term ID"
msgstr "Term-ID"

#: includes/fields/class-acf-field-taxonomy.php:674
msgid "Term Object"
msgstr "Term-objekt"

#: includes/fields/class-acf-field-taxonomy.php:659
msgid "Load value from posts terms"
msgstr "Hämta värde från inläggets termer"

#: includes/fields/class-acf-field-taxonomy.php:658
msgid "Load Terms"
msgstr "Ladda termer"

#: includes/fields/class-acf-field-taxonomy.php:648
msgid "Connect selected terms to the post"
msgstr "Koppla valda termer till inlägget"

#: includes/fields/class-acf-field-taxonomy.php:647
msgid "Save Terms"
msgstr "Spara termer"

#: includes/fields/class-acf-field-taxonomy.php:637
msgid "Allow new terms to be created whilst editing"
msgstr "Tillåt att nya termer skapas vid redigering"

#: includes/fields/class-acf-field-taxonomy.php:636
msgid "Create Terms"
msgstr "Skapa termer"

#: includes/fields/class-acf-field-taxonomy.php:695
msgid "Radio Buttons"
msgstr "Radioknappar"

#: includes/fields/class-acf-field-taxonomy.php:694
msgid "Single Value"
msgstr "Enskild värde"

#: includes/fields/class-acf-field-taxonomy.php:692
msgid "Multi Select"
msgstr "Flerval"

#: includes/fields/class-acf-field-checkbox.php:22
#: includes/fields/class-acf-field-taxonomy.php:691
msgid "Checkbox"
msgstr "Kryssruta"

#: includes/fields/class-acf-field-taxonomy.php:690
msgid "Multiple Values"
msgstr "Flera värden"

#: includes/fields/class-acf-field-taxonomy.php:685
msgid "Select the appearance of this field"
msgstr "Välj utseendet på detta fält"

#: includes/fields/class-acf-field-taxonomy.php:684
msgid "Appearance"
msgstr "Utseende"

#: includes/fields/class-acf-field-taxonomy.php:626
msgid "Select the taxonomy to be displayed"
msgstr "Välj taxonomin som ska visas"

#: includes/fields/class-acf-field-taxonomy.php:590
msgctxt "No Terms"
msgid "No %s"
msgstr "Inga %s"

#: includes/fields/class-acf-field-number.php:240
msgid "Value must be equal to or lower than %d"
msgstr "Värdet måste vara lika med eller lägre än %d"

#: includes/fields/class-acf-field-number.php:235
msgid "Value must be equal to or higher than %d"
msgstr "Värdet måste vara lika med eller högre än %d"

#: includes/fields/class-acf-field-number.php:223
msgid "Value must be a number"
msgstr "Värdet måste vara ett nummer"

#: includes/fields/class-acf-field-number.php:22
msgid "Number"
msgstr "Nummer"

#: includes/fields/class-acf-field-radio.php:254
msgid "Save 'other' values to the field's choices"
msgstr "Spara ”andra” värden i fältets val"

#: includes/fields/class-acf-field-radio.php:243
msgid "Add 'other' choice to allow for custom values"
msgstr "Lägg till valet ”annat” för att tillåta anpassade värden"

#: includes/admin/views/global/navigation.php:199
msgid "Other"
msgstr "Annat"

#: includes/fields/class-acf-field-radio.php:22
msgid "Radio Button"
msgstr "Alternativknapp"

#: includes/fields/class-acf-field-accordion.php:103
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Definiera en ändpunkt för föregående dragspel att stoppa. Detta dragspel "
"kommer inte att vara synligt."

#: includes/fields/class-acf-field-accordion.php:92
msgid "Allow this accordion to open without closing others."
msgstr "Tillåt detta dragspel öppna utan att stänga andra."

#: includes/fields/class-acf-field-accordion.php:91
msgid "Multi-Expand"
msgstr "Multi-expandera"

#: includes/fields/class-acf-field-accordion.php:81
msgid "Display this accordion as open on page load."
msgstr "Visa detta dragspel som öppet på sidladdning."

#: includes/fields/class-acf-field-accordion.php:80
msgid "Open"
msgstr "Öppen"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Dragspel"

#: includes/fields/class-acf-field-file.php:253
#: includes/fields/class-acf-field-file.php:265
msgid "Restrict which files can be uploaded"
msgstr "Begränsa vilka filer som kan laddas upp"

#: includes/fields/class-acf-field-file.php:207
msgid "File ID"
msgstr "Fil-ID"

#: includes/fields/class-acf-field-file.php:206
msgid "File URL"
msgstr "Fil-URL"

#: includes/fields/class-acf-field-file.php:205
msgid "File Array"
msgstr "Fil-array"

#: includes/fields/class-acf-field-file.php:176
msgid "Add File"
msgstr "Lägg till fil"

#: includes/admin/tools/class-acf-admin-tool-import.php:151
#: includes/fields/class-acf-field-file.php:176
msgid "No file selected"
msgstr "Ingen fil vald"

#: includes/fields/class-acf-field-file.php:140
msgid "File name"
msgstr "Filnamn"

#: includes/fields/class-acf-field-file.php:57
#: assets/build/js/acf-input.js:3163 assets/build/js/acf-input.js:3386
msgid "Update File"
msgstr "Uppdatera fil"

#: includes/fields/class-acf-field-file.php:56
#: assets/build/js/acf-input.js:3162 assets/build/js/acf-input.js:3385
msgid "Edit File"
msgstr "Redigera fil"

#: includes/admin/tools/class-acf-admin-tool-import.php:55
#: includes/fields/class-acf-field-file.php:55
#: assets/build/js/acf-input.js:3136 assets/build/js/acf-input.js:3358
msgid "Select File"
msgstr "Välj fil"

#: includes/fields/class-acf-field-file.php:22
msgid "File"
msgstr "Fil"

#: includes/fields/class-acf-field-password.php:22
msgid "Password"
msgstr "Lösenord"

#: includes/fields/class-acf-field-select.php:365
msgid "Specify the value returned"
msgstr "Specificera värdet att returnera"

#: includes/fields/class-acf-field-select.php:433
msgid "Use AJAX to lazy load choices?"
msgstr "Använda AJAX för att ladda alternativ efter att sidan laddats?"

#: includes/fields/class-acf-field-checkbox.php:333
#: includes/fields/class-acf-field-select.php:354
msgid "Enter each default value on a new line"
msgstr "Ange varje standardvärde på en ny rad"

#: includes/fields/class-acf-field-select.php:229 includes/media.php:48
#: assets/build/js/acf-input.js:7822 assets/build/js/acf-input.js:8484
msgctxt "verb"
msgid "Select"
msgstr "Välj"

#: includes/fields/class-acf-field-select.php:109
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Laddning misslyckades"

#: includes/fields/class-acf-field-select.php:108
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Söker&hellip;"

#: includes/fields/class-acf-field-select.php:107
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Laddar in fler resultat …"

#: includes/fields/class-acf-field-select.php:106
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Du kan endast välja %d objekt"

#: includes/fields/class-acf-field-select.php:105
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Du kan endast välja 1 objekt"

#: includes/fields/class-acf-field-select.php:104
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Ta bort %d tecken"

#: includes/fields/class-acf-field-select.php:103
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Ta bort 1 tecken"

#: includes/fields/class-acf-field-select.php:102
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Ange %d eller fler tecken"

#: includes/fields/class-acf-field-select.php:101
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Ange 1 eller fler tecken"

#: includes/fields/class-acf-field-select.php:100
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Inga matchningar hittades"

#: includes/fields/class-acf-field-select.php:99
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""
"%d resultat är tillgängliga, använd tangenterna med uppåt- och nedåtpil för "
"att navigera."

#: includes/fields/class-acf-field-select.php:98
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr ""
"Ett resultat är tillgängligt, tryck på returtangenten för att välja det."

#: includes/fields/class-acf-field-select.php:22
#: includes/fields/class-acf-field-taxonomy.php:696
msgctxt "noun"
msgid "Select"
msgstr "Välj"

#: includes/fields/class-acf-field-user.php:102
msgid "User ID"
msgstr "Användar-ID"

#: includes/fields/class-acf-field-user.php:101
msgid "User Object"
msgstr "Användarobjekt"

#: includes/fields/class-acf-field-user.php:100
msgid "User Array"
msgstr "Användar-array"

#: includes/fields/class-acf-field-user.php:88
msgid "All user roles"
msgstr "Alla användarroller"

#: includes/fields/class-acf-field-user.php:80
msgid "Filter by Role"
msgstr "Filtrera efter roll"

#: includes/fields/class-acf-field-user.php:15 includes/locations.php:103
msgid "User"
msgstr "Användare"

#: includes/fields/class-acf-field-separator.php:22
msgid "Separator"
msgstr "Avgränsare"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Select Color"
msgstr "Välj färg"

#: includes/admin/post-types/admin-post-type.php:127
#: includes/admin/post-types/admin-taxonomy.php:129
#: includes/fields/class-acf-field-color_picker.php:67
#: assets/build/js/acf-internal-post-type.js:72
#: assets/build/js/acf-internal-post-type.js:86
msgid "Default"
msgstr "Standard"

#: includes/admin/views/acf-post-type/advanced-settings.php:89
#: includes/admin/views/acf-taxonomy/advanced-settings.php:141
#: includes/fields/class-acf-field-color_picker.php:65
msgid "Clear"
msgstr "Rensa"

#: includes/fields/class-acf-field-color_picker.php:22
msgid "Color Picker"
msgstr "Färgväljare"

#: includes/fields/class-acf-field-date_time_picker.php:82
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Välj"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Klart"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Nu"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Tidszon"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Mikrosekund"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Millisekund"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Sekund"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minut"

#: includes/fields/class-acf-field-date_time_picker.php:67
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Timme"

#: includes/fields/class-acf-field-date_time_picker.php:66
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Tid"

#: includes/fields/class-acf-field-date_time_picker.php:65
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Välj tid"

#: includes/fields/class-acf-field-date_time_picker.php:22
msgid "Date Time Picker"
msgstr "Datum/tidväljare"

#: includes/fields/class-acf-field-accordion.php:102
msgid "Endpoint"
msgstr "Ändpunkt"

#: includes/admin/views/acf-field-group/options.php:130
#: includes/fields/class-acf-field-tab.php:109
msgid "Left aligned"
msgstr "Vänsterjusterad"

#: includes/admin/views/acf-field-group/options.php:129
#: includes/fields/class-acf-field-tab.php:108
msgid "Top aligned"
msgstr "Toppjusterad"

#: includes/fields/class-acf-field-tab.php:104
msgid "Placement"
msgstr "Placering"

#: includes/fields/class-acf-field-tab.php:23
msgid "Tab"
msgstr "Flik"

#: includes/fields/class-acf-field-url.php:138
msgid "Value must be a valid URL"
msgstr "Värde måste vara en giltig URL"

#: includes/fields/class-acf-field-link.php:153
msgid "Link URL"
msgstr "Länk-URL"

#: includes/fields/class-acf-field-link.php:152
msgid "Link Array"
msgstr "Länk-array"

#: includes/fields/class-acf-field-link.php:124
msgid "Opens in a new window/tab"
msgstr "Öppnas i ett nytt fönster/flik"

#: includes/fields/class-acf-field-link.php:119
msgid "Select Link"
msgstr "Välj länk"

#: includes/fields/class-acf-field-link.php:22
msgid "Link"
msgstr "Länk"

#: includes/fields/class-acf-field-email.php:22
msgid "Email"
msgstr "E-post"

#: includes/fields/class-acf-field-number.php:173
#: includes/fields/class-acf-field-range.php:206
msgid "Step Size"
msgstr "Stegstorlek"

#: includes/fields/class-acf-field-number.php:143
#: includes/fields/class-acf-field-range.php:184
msgid "Maximum Value"
msgstr "Maximalt värde"

#: includes/fields/class-acf-field-number.php:133
#: includes/fields/class-acf-field-range.php:173
msgid "Minimum Value"
msgstr "Minsta värde"

#: includes/fields/class-acf-field-range.php:22
msgid "Range"
msgstr "Intervall"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:350
#: includes/fields/class-acf-field-radio.php:210
#: includes/fields/class-acf-field-select.php:372
msgid "Both (Array)"
msgstr "Båda (Array)"

#: includes/admin/views/acf-field-group/fields.php:52
#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:349
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-select.php:371
msgid "Label"
msgstr "Etikett"

#: includes/fields/class-acf-field-button-group.php:163
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:208
#: includes/fields/class-acf-field-select.php:370
msgid "Value"
msgstr "Värde"

#: includes/fields/class-acf-field-button-group.php:211
#: includes/fields/class-acf-field-checkbox.php:411
#: includes/fields/class-acf-field-radio.php:282
msgid "Vertical"
msgstr "Vertikal"

#: includes/fields/class-acf-field-button-group.php:210
#: includes/fields/class-acf-field-checkbox.php:412
#: includes/fields/class-acf-field-radio.php:283
msgid "Horizontal"
msgstr "Horisontell"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:343
msgid "red : Red"
msgstr "röd : Röd"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:343
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"För mer kontroll kan du specificera både ett värde och en etikett så här:"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:343
msgid "Enter each choice on a new line."
msgstr "Ange varje val på en ny rad."

#: includes/fields/class-acf-field-button-group.php:137
#: includes/fields/class-acf-field-checkbox.php:322
#: includes/fields/class-acf-field-radio.php:182
#: includes/fields/class-acf-field-select.php:342
msgid "Choices"
msgstr "Val"

#: includes/fields/class-acf-field-button-group.php:23
msgid "Button Group"
msgstr "Knappgrupp"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-page_link.php:519
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-radio.php:228
#: includes/fields/class-acf-field-select.php:401
#: includes/fields/class-acf-field-taxonomy.php:705
#: includes/fields/class-acf-field-user.php:132
msgid "Allow Null"
msgstr "Tillåt värdet ”null”"

#: includes/fields/class-acf-field-page_link.php:273
#: includes/fields/class-acf-field-post_object.php:254
#: includes/fields/class-acf-field-taxonomy.php:869
msgid "Parent"
msgstr "Överordnad"

#: includes/fields/class-acf-field-wysiwyg.php:367
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "TinyMCE kommer inte att initialiseras förrän fältet är klickat"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Delay Initialization"
msgstr "Fördröjd initialisering"

#: includes/fields/class-acf-field-wysiwyg.php:355
msgid "Show Media Upload Buttons"
msgstr "Visa knappar för mediauppladdning"

#: includes/fields/class-acf-field-wysiwyg.php:339
msgid "Toolbar"
msgstr "Verktygsfält"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgid "Text Only"
msgstr "Endast text"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual Only"
msgstr "Endast visuell"

#: includes/fields/class-acf-field-wysiwyg.php:329
msgid "Visual & Text"
msgstr "Visuell och text"

#: includes/fields/class-acf-field-icon_picker.php:237
#: includes/fields/class-acf-field-wysiwyg.php:324
msgid "Tabs"
msgstr "Flikar"

#: includes/fields/class-acf-field-wysiwyg.php:268
msgid "Click to initialize TinyMCE"
msgstr "Klicka för att initialisera TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:262
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Text"

#: includes/fields/class-acf-field-wysiwyg.php:261
msgid "Visual"
msgstr "Visuellt"

#: includes/fields/class-acf-field-text.php:181
#: includes/fields/class-acf-field-textarea.php:217
msgid "Value must not exceed %d characters"
msgstr "Värde får inte överstiga %d tecken"

#: includes/fields/class-acf-field-text.php:116
#: includes/fields/class-acf-field-textarea.php:114
msgid "Leave blank for no limit"
msgstr "Lämna fältet tomt för att inte sätta någon begränsning"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:113
msgid "Character Limit"
msgstr "Teckenbegränsning"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:194
#: includes/fields/class-acf-field-password.php:95
#: includes/fields/class-acf-field-range.php:228
#: includes/fields/class-acf-field-text.php:156
msgid "Appears after the input"
msgstr "Visas efter inmatningen"

#: includes/fields/class-acf-field-email.php:143
#: includes/fields/class-acf-field-number.php:193
#: includes/fields/class-acf-field-password.php:94
#: includes/fields/class-acf-field-range.php:227
#: includes/fields/class-acf-field-text.php:155
msgid "Append"
msgstr "Lägg till efter"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:184
#: includes/fields/class-acf-field-password.php:85
#: includes/fields/class-acf-field-range.php:218
#: includes/fields/class-acf-field-text.php:146
msgid "Appears before the input"
msgstr "Visas före inmatningen"

#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:183
#: includes/fields/class-acf-field-password.php:84
#: includes/fields/class-acf-field-range.php:217
#: includes/fields/class-acf-field-text.php:145
msgid "Prepend"
msgstr "Lägg till före"

#: includes/fields/class-acf-field-email.php:124
#: includes/fields/class-acf-field-number.php:164
#: includes/fields/class-acf-field-password.php:75
#: includes/fields/class-acf-field-text.php:136
#: includes/fields/class-acf-field-textarea.php:146
#: includes/fields/class-acf-field-url.php:105
msgid "Appears within the input"
msgstr "Visas inuti inmatningen"

#: includes/fields/class-acf-field-email.php:123
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-password.php:74
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:145
#: includes/fields/class-acf-field-url.php:104
msgid "Placeholder Text"
msgstr "Platshållartext"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-email.php:104
#: includes/fields/class-acf-field-number.php:114
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-range.php:154
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:94
#: includes/fields/class-acf-field-url.php:85
#: includes/fields/class-acf-field-wysiwyg.php:292
msgid "Appears when creating a new post"
msgstr "Visas när ett nytt inlägg skapas"

#: includes/fields/class-acf-field-text.php:22
msgid "Text"
msgstr "Text"

#: includes/fields/class-acf-field-relationship.php:753
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s kräver minst %2$s val"
msgstr[1] "%1$s kräver minst %2$s val"

#: includes/fields/class-acf-field-post_object.php:402
#: includes/fields/class-acf-field-relationship.php:616
msgid "Post ID"
msgstr "Inläggs-ID"

#: includes/fields/class-acf-field-post_object.php:15
#: includes/fields/class-acf-field-post_object.php:401
#: includes/fields/class-acf-field-relationship.php:615
msgid "Post Object"
msgstr "Inläggsobjekt"

#: includes/fields/class-acf-field-relationship.php:648
msgid "Maximum Posts"
msgstr "Maximalt antal inlägg"

#: includes/fields/class-acf-field-relationship.php:638
msgid "Minimum Posts"
msgstr "Minsta antal inlägg"

#: includes/admin/views/acf-field-group/options.php:183
#: includes/admin/views/acf-post-type/advanced-settings.php:29
#: includes/fields/class-acf-field-relationship.php:673
msgid "Featured Image"
msgstr "Utvald bild"

#: includes/fields/class-acf-field-relationship.php:669
msgid "Selected elements will be displayed in each result"
msgstr "Valda element kommer att visas i varje resultat"

#: includes/fields/class-acf-field-relationship.php:668
msgid "Elements"
msgstr "Element"

#: includes/fields/class-acf-field-relationship.php:602
#: includes/fields/class-acf-field-taxonomy.php:20
#: includes/fields/class-acf-field-taxonomy.php:625
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Taxonomi"

#: includes/fields/class-acf-field-relationship.php:601
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:92
msgid "Post Type"
msgstr "Inläggstyp"

#: includes/fields/class-acf-field-relationship.php:595
msgid "Filters"
msgstr "Filter"

#: includes/fields/class-acf-field-page_link.php:480
#: includes/fields/class-acf-field-post_object.php:389
#: includes/fields/class-acf-field-relationship.php:588
msgid "All taxonomies"
msgstr "Alla taxonomier"

#: includes/fields/class-acf-field-page_link.php:472
#: includes/fields/class-acf-field-post_object.php:381
#: includes/fields/class-acf-field-relationship.php:580
msgid "Filter by Taxonomy"
msgstr "Filtrera efter taxonomi"

#: includes/fields/class-acf-field-page_link.php:450
#: includes/fields/class-acf-field-post_object.php:359
#: includes/fields/class-acf-field-relationship.php:558
msgid "All post types"
msgstr "Alla inläggstyper"

#: includes/fields/class-acf-field-page_link.php:442
#: includes/fields/class-acf-field-post_object.php:351
#: includes/fields/class-acf-field-relationship.php:550
msgid "Filter by Post Type"
msgstr "Filtrera efter inläggstyp"

#: includes/fields/class-acf-field-relationship.php:450
msgid "Search..."
msgstr "Sök …"

#: includes/fields/class-acf-field-relationship.php:380
msgid "Select taxonomy"
msgstr "Välj taxonomi"

#: includes/fields/class-acf-field-relationship.php:372
msgid "Select post type"
msgstr "Välj inläggstyp"

#: includes/fields/class-acf-field-relationship.php:78
#: assets/build/js/acf-input.js:4938 assets/build/js/acf-input.js:5403
msgid "No matches found"
msgstr "Inga matchningar hittades"

#: includes/fields/class-acf-field-relationship.php:77
#: assets/build/js/acf-input.js:4921 assets/build/js/acf-input.js:5382
msgid "Loading"
msgstr "Laddar in"

#: includes/fields/class-acf-field-relationship.php:76
#: assets/build/js/acf-input.js:4825 assets/build/js/acf-input.js:5272
msgid "Maximum values reached ( {max} values )"
msgstr "Maximalt antal värden har nåtts ({max} värden)"

#: includes/fields/class-acf-field-relationship.php:17
msgid "Relationship"
msgstr "Relationer"

#: includes/fields/class-acf-field-file.php:277
#: includes/fields/class-acf-field-image.php:307
msgid "Comma separated list. Leave blank for all types"
msgstr "Kommaseparerad lista. Lämna tomt för alla typer"

#: includes/fields/class-acf-field-file.php:276
#: includes/fields/class-acf-field-image.php:306
msgid "Allowed File Types"
msgstr "Tillåtna filtyper"

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-image.php:270
msgid "Maximum"
msgstr "Maximalt"

#: includes/fields/class-acf-field-file.php:144
#: includes/fields/class-acf-field-file.php:256
#: includes/fields/class-acf-field-file.php:268
#: includes/fields/class-acf-field-image.php:261
#: includes/fields/class-acf-field-image.php:297
msgid "File size"
msgstr "Filstorlek"

#: includes/fields/class-acf-field-image.php:235
#: includes/fields/class-acf-field-image.php:271
msgid "Restrict which images can be uploaded"
msgstr "Begränsa vilka bilder som kan laddas upp"

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:234
msgid "Minimum"
msgstr "Minimum"

#: includes/fields/class-acf-field-file.php:222
#: includes/fields/class-acf-field-image.php:200
msgid "Uploaded to post"
msgstr "Uppladdat till inlägg"

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:199
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "Alla"

#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:194
msgid "Limit the media library choice"
msgstr "Begränsa mediabiblioteksvalet"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:193
msgid "Library"
msgstr "Bibliotek"

#: includes/fields/class-acf-field-image.php:326
msgid "Preview Size"
msgstr "Förhandsgranska storlek"

#: includes/fields/class-acf-field-image.php:185
msgid "Image ID"
msgstr "Bild-ID"

#: includes/fields/class-acf-field-image.php:184
msgid "Image URL"
msgstr "Bild-URL"

#: includes/fields/class-acf-field-image.php:183
msgid "Image Array"
msgstr "Bild-array"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-checkbox.php:343
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-link.php:147
#: includes/fields/class-acf-field-radio.php:203
msgid "Specify the returned value on front end"
msgstr "Specificera returvärdet på front-end"

#: includes/fields/class-acf-field-button-group.php:157
#: includes/fields/class-acf-field-checkbox.php:342
#: includes/fields/class-acf-field-file.php:199
#: includes/fields/class-acf-field-link.php:146
#: includes/fields/class-acf-field-radio.php:202
#: includes/fields/class-acf-field-taxonomy.php:669
msgid "Return Value"
msgstr "Returvärde"

#: includes/fields/class-acf-field-image.php:155
msgid "Add Image"
msgstr "Lägg till bild"

#: includes/fields/class-acf-field-image.php:155
msgid "No image selected"
msgstr "Ingen bild vald"

#: includes/assets.php:353 includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:135
#: includes/fields/class-acf-field-link.php:124 assets/build/js/acf.js:1569
#: assets/build/js/acf.js:1661
msgid "Remove"
msgstr "Ta bort"

#: includes/admin/views/acf-field-group/field.php:89
#: includes/fields/class-acf-field-file.php:150
#: includes/fields/class-acf-field-image.php:133
#: includes/fields/class-acf-field-link.php:124
msgid "Edit"
msgstr "Redigera"

#: includes/fields/class-acf-field-image.php:63 includes/media.php:55
#: assets/build/js/acf-input.js:7869 assets/build/js/acf-input.js:8538
msgid "All images"
msgstr "Alla bilder"

#: includes/fields/class-acf-field-image.php:62
#: assets/build/js/acf-input.js:4182 assets/build/js/acf-input.js:4580
msgid "Update Image"
msgstr "Uppdatera bild"

#: includes/fields/class-acf-field-image.php:61
#: assets/build/js/acf-input.js:4181 assets/build/js/acf-input.js:4579
msgid "Edit Image"
msgstr "Redigera bild"

#: includes/fields/class-acf-field-image.php:60
#: assets/build/js/acf-input.js:4017 assets/build/js/acf-input.js:4157
#: assets/build/js/acf-input.js:4405 assets/build/js/acf-input.js:4554
msgid "Select Image"
msgstr "Välj bild"

#: includes/fields/class-acf-field-image.php:22
msgid "Image"
msgstr "Bild"

#: includes/fields/class-acf-field-message.php:110
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Tillåt att HTML-märkkod visas som synlig text i stället för att renderas"

#: includes/fields/class-acf-field-message.php:109
msgid "Escape HTML"
msgstr "Inaktivera HTML-rendering"

#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-textarea.php:162
msgid "No Formatting"
msgstr "Ingen formatering"

#: includes/fields/class-acf-field-message.php:100
#: includes/fields/class-acf-field-textarea.php:161
msgid "Automatically add &lt;br&gt;"
msgstr "Lägg automatiskt till &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:99
#: includes/fields/class-acf-field-textarea.php:160
msgid "Automatically add paragraphs"
msgstr "Lägg automatiskt till stycken"

#: includes/fields/class-acf-field-message.php:95
#: includes/fields/class-acf-field-textarea.php:156
msgid "Controls how new lines are rendered"
msgstr "Styr hur nya rader visas"

#: includes/fields/class-acf-field-message.php:94
#: includes/fields/class-acf-field-textarea.php:155
msgid "New Lines"
msgstr "Nya rader"

#: includes/fields/class-acf-field-date_picker.php:221
#: includes/fields/class-acf-field-date_time_picker.php:208
msgid "Week Starts On"
msgstr "Veckan börjar på"

#: includes/fields/class-acf-field-date_picker.php:190
msgid "The format used when saving a value"
msgstr "Formatet som används när ett värde sparas"

#: includes/fields/class-acf-field-date_picker.php:189
msgid "Save Format"
msgstr "Spara format"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "V"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Föreg."

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Nästa"

#: includes/fields/class-acf-field-date_picker.php:58
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Idag"

#: includes/fields/class-acf-field-date_picker.php:57
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Klart"

#: includes/fields/class-acf-field-date_picker.php:22
msgid "Date Picker"
msgstr "Datumväljare"

#: includes/fields/class-acf-field-image.php:238
#: includes/fields/class-acf-field-image.php:274
#: includes/fields/class-acf-field-oembed.php:241
msgid "Width"
msgstr "Bredd"

#: includes/fields/class-acf-field-oembed.php:238
#: includes/fields/class-acf-field-oembed.php:250
msgid "Embed Size"
msgstr "Inbäddad storlek"

#: includes/fields/class-acf-field-oembed.php:198
msgid "Enter URL"
msgstr "Ange URL"

#: includes/fields/class-acf-field-oembed.php:22
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:172
msgid "Text shown when inactive"
msgstr "Text som visas när inaktivt"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Off Text"
msgstr "”Av”-text"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Text som visas när aktivt"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "”På”-text"

#: includes/fields/class-acf-field-select.php:422
#: includes/fields/class-acf-field-true_false.php:187
msgid "Stylized UI"
msgstr "Stiliserat användargränssnitt"

#: includes/fields/class-acf-field-button-group.php:147
#: includes/fields/class-acf-field-checkbox.php:332
#: includes/fields/class-acf-field-color_picker.php:144
#: includes/fields/class-acf-field-email.php:103
#: includes/fields/class-acf-field-number.php:113
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-range.php:153
#: includes/fields/class-acf-field-select.php:353
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:93
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:84
#: includes/fields/class-acf-field-wysiwyg.php:291
msgid "Default Value"
msgstr "Standardvärde"

#: includes/fields/class-acf-field-true_false.php:126
msgid "Displays text alongside the checkbox"
msgstr "Visar text bredvid kryssrutan"

#: includes/fields/class-acf-field-message.php:23
#: includes/fields/class-acf-field-message.php:84
#: includes/fields/class-acf-field-true_false.php:125
msgid "Message"
msgstr "Meddelande"

#: includes/assets.php:352 includes/class-acf-site-health.php:277
#: includes/class-acf-site-health.php:334
#: includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:175
#: assets/build/js/acf.js:1746 assets/build/js/acf.js:1861
msgid "No"
msgstr "Nej"

#: includes/assets.php:351 includes/class-acf-site-health.php:276
#: includes/class-acf-site-health.php:334
#: includes/fields/class-acf-field-true_false.php:76
#: includes/fields/class-acf-field-true_false.php:159
#: assets/build/js/acf.js:1745 assets/build/js/acf.js:1860
msgid "Yes"
msgstr "Ja"

#: includes/fields/class-acf-field-true_false.php:22
msgid "True / False"
msgstr "Sant/falskt"

#: includes/fields/class-acf-field-group.php:412
msgid "Row"
msgstr "Rad"

#: includes/fields/class-acf-field-group.php:411
msgid "Table"
msgstr "Tabell"

#: includes/admin/post-types/admin-field-group.php:158
#: includes/fields/class-acf-field-group.php:410
msgid "Block"
msgstr "Block"

#: includes/fields/class-acf-field-group.php:405
msgid "Specify the style used to render the selected fields"
msgstr "Specificera stilen för att rendera valda fält"

#: includes/fields.php:330 includes/fields/class-acf-field-button-group.php:204
#: includes/fields/class-acf-field-checkbox.php:405
#: includes/fields/class-acf-field-group.php:404
#: includes/fields/class-acf-field-radio.php:276
msgid "Layout"
msgstr "Layout"

#: includes/fields/class-acf-field-group.php:388
msgid "Sub Fields"
msgstr "Underfält"

#: includes/fields/class-acf-field-group.php:22
msgid "Group"
msgstr "Grupp"

#: includes/fields/class-acf-field-google-map.php:222
msgid "Customize the map height"
msgstr "Anpassa karthöjden"

#: includes/fields/class-acf-field-google-map.php:221
#: includes/fields/class-acf-field-image.php:249
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:253
msgid "Height"
msgstr "Höjd"

#: includes/fields/class-acf-field-google-map.php:210
msgid "Set the initial zoom level"
msgstr "Ställ in den initiala zoomnivån"

#: includes/fields/class-acf-field-google-map.php:209
msgid "Zoom"
msgstr "Zooma"

#: includes/fields/class-acf-field-google-map.php:183
#: includes/fields/class-acf-field-google-map.php:196
msgid "Center the initial map"
msgstr "Centrera den inledande kartan"

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:195
msgid "Center"
msgstr "Centrerat"

#: includes/fields/class-acf-field-google-map.php:154
msgid "Search for address..."
msgstr "Sök efter adress …"

#: includes/fields/class-acf-field-google-map.php:151
msgid "Find current location"
msgstr "Hitta nuvarande plats"

#: includes/fields/class-acf-field-google-map.php:150
msgid "Clear location"
msgstr "Rensa plats"

#: includes/fields/class-acf-field-google-map.php:149
#: includes/fields/class-acf-field-relationship.php:600
msgid "Search"
msgstr "Sök"

#: includes/fields/class-acf-field-google-map.php:57
#: assets/build/js/acf-input.js:3529 assets/build/js/acf-input.js:3787
msgid "Sorry, this browser does not support geolocation"
msgstr "Denna webbläsare saknar stöd för platsinformation"

#: includes/fields/class-acf-field-google-map.php:22
msgid "Google Map"
msgstr "Google Map"

#: includes/fields/class-acf-field-date_picker.php:201
#: includes/fields/class-acf-field-date_time_picker.php:189
#: includes/fields/class-acf-field-time_picker.php:122
msgid "The format returned via template functions"
msgstr "Formatet returnerad via mallfunktioner"

#: includes/fields/class-acf-field-color_picker.php:168
#: includes/fields/class-acf-field-date_picker.php:200
#: includes/fields/class-acf-field-date_time_picker.php:188
#: includes/fields/class-acf-field-icon_picker.php:260
#: includes/fields/class-acf-field-image.php:177
#: includes/fields/class-acf-field-post_object.php:396
#: includes/fields/class-acf-field-relationship.php:610
#: includes/fields/class-acf-field-select.php:364
#: includes/fields/class-acf-field-time_picker.php:121
#: includes/fields/class-acf-field-user.php:95
msgid "Return Format"
msgstr "Returformat"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:210
#: includes/fields/class-acf-field-date_time_picker.php:180
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:113
#: includes/fields/class-acf-field-time_picker.php:129
msgid "Custom:"
msgstr "Anpassad:"

#: includes/fields/class-acf-field-date_picker.php:171
#: includes/fields/class-acf-field-date_time_picker.php:171
#: includes/fields/class-acf-field-time_picker.php:106
msgid "The format displayed when editing a post"
msgstr "Formatet visas när ett inlägg redigeras"

#: includes/fields/class-acf-field-date_picker.php:170
#: includes/fields/class-acf-field-date_time_picker.php:170
#: includes/fields/class-acf-field-time_picker.php:105
msgid "Display Format"
msgstr "Visningsformat"

#: includes/fields/class-acf-field-time_picker.php:22
msgid "Time Picker"
msgstr "Tidsväljare"

#. translators: counts for inactive field groups
#: acf.php:506
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Inaktiv <span class=\"count\">(%s)</span>"
msgstr[1] "Inaktiva <span class=\"count\">(%s)</span>"

#: acf.php:467
msgid "No Fields found in Trash"
msgstr "Inga fält hittades i papperskorgen"

#: acf.php:466
msgid "No Fields found"
msgstr "Inga fält hittades"

#: acf.php:465
msgid "Search Fields"
msgstr "Sök fält"

#: acf.php:464
msgid "View Field"
msgstr "Visa fält"

#: acf.php:463 includes/admin/views/acf-field-group/fields.php:113
msgid "New Field"
msgstr "Nytt fält"

#: acf.php:462
msgid "Edit Field"
msgstr "Redigera fält"

#: acf.php:461
msgid "Add New Field"
msgstr "Lägg till nytt fält"

#: acf.php:459
msgid "Field"
msgstr "Fält"

#: acf.php:458 includes/admin/post-types/admin-field-group.php:179
#: includes/admin/post-types/admin-field-groups.php:93
#: includes/admin/views/acf-field-group/fields.php:32
msgid "Fields"
msgstr "Fält"

#: acf.php:433
msgid "No Field Groups found in Trash"
msgstr "Inga fältgrupper hittades i papperskorgen"

#: acf.php:432
msgid "No Field Groups found"
msgstr "Inga fältgrupper hittades"

#: acf.php:431
msgid "Search Field Groups"
msgstr "Sök fältgrupper"

#: acf.php:430
msgid "View Field Group"
msgstr "Visa fältgrupp"

#: acf.php:429
msgid "New Field Group"
msgstr "Ny fältgrupp"

#: acf.php:428
msgid "Edit Field Group"
msgstr "Redigera fältgrupp"

#: acf.php:427
msgid "Add New Field Group"
msgstr "Lägg till ny fältgrupp"

#: acf.php:426 acf.php:460
#: includes/admin/views/acf-post-type/advanced-settings.php:224
#: includes/post-types/class-acf-post-type.php:93
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "Lägg till nytt"

#: acf.php:425
msgid "Field Group"
msgstr "Fältgrupp"

#: acf.php:424 includes/admin/post-types/admin-field-groups.php:55
#: includes/admin/post-types/admin-post-types.php:113
#: includes/admin/post-types/admin-taxonomies.php:112
msgid "Field Groups"
msgstr "Fältgrupper"

#. Description of the plugin
#: acf.php
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr "Anpassa WordPress med kraftfulla, professionella och intuitiva fält."

#. Plugin URI of the plugin
#: acf.php
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php acf.php:93
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/blocks.php:170
msgid "Block type name is required."
msgstr "Blocktypsnamn är obligatoriskt."

#. translators: The name of the block type
#: pro/blocks.php:178
msgid "Block type \"%s\" is already registered."
msgstr "Blocktypen \"%s\" är redan registrerad."

#: pro/blocks.php:726
msgid "Switch to Edit"
msgstr "Växla till Redigera"

#: pro/blocks.php:727
msgid "Switch to Preview"
msgstr "Växla till förhandsgranskning"

#: pro/blocks.php:728
msgid "Change content alignment"
msgstr "Ändra innehållsjustering"

#. translators: %s: Block type title
#: pro/blocks.php:731
msgid "%s settings"
msgstr "%s-inställningar"

#: pro/blocks.php:936
msgid "This block contains no editable fields."
msgstr "Det här blocket innehåller inga redigerbara fält."

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:942
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""
"Tilldela en <a href=\"%s\" target=\"_blank\">fältgrupp</a> för att lägga "
"till fält i detta block."

#: pro/options-page.php:78
msgid "Options Updated"
msgstr "Alternativ uppdaterade"

#: pro/updates.php:99
msgid ""
"To enable updates, please enter your license key on the <a "
"href=\"%1$s\">Updates</a> page. If you don't have a licence key, please see "
"<a href=\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""
"Om du vill aktivera uppdateringar anger du din licensnyckel på sidan <a "
"href=\"%1$s\">Uppdateringar</a>. Om du inte har en licensnyckel, se <a "
"href=\"%2$s\" target=\"_blank\">uppgifter och priser</a>."

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""
"<b>ACF-aktiveringsfel</b>. Din definierade licensnyckel har ändrats, men ett "
"fel uppstod vid inaktivering av din gamla licens"

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""
"<b>ACF-aktiveringsfel</b>. Din definierade licensnyckel har ändrats, men ett "
"fel uppstod vid anslutning till aktiveringsservern"

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr "<b>ACF-aktiveringsfel</b>"

#: pro/updates.php:187
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr ""
"<b>ACF-aktiveringsfel</b>. Ett fel uppstod vid anslutning till "
"aktiveringsservern"

#: pro/updates.php:279
msgid "Check Again"
msgstr "Kontrollera igen"

#: pro/updates.php:593
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr "<b>ACF-aktiveringsfel</b>. Kunde inte ansluta till aktiveringsservern"

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr "Publicera"

#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Inga fältgrupper hittades för denna inställningssida. <a href=\"%s\">Skapa "
"en fältgrupp</a>"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Fel</b>. Kunde inte ansluta till uppdateringsservern"

#: pro/admin/admin-updates.php:212
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""
"<b>Fel</b>. Det gick inte att autentisera uppdateringspaketet. Kontrollera "
"igen eller inaktivera och återaktivera din ACF PRO-licens."

#: pro/admin/admin-updates.php:199
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""
"<b>Fel</b>. Din licens för denna webbplats har gått ut eller inaktiverats. "
"Återaktivera din ACF PRO-licens."

#: pro/fields/class-acf-field-clone.php:27,
#: pro/fields/class-acf-field-repeater.php:31
msgid ""
"Allows you to select and display existing fields. It does not duplicate any "
"fields in the database, but loads and displays the selected fields at run-"
"time. The Clone field can either replace itself with the selected fields or "
"display the selected fields as a group of subfields."
msgstr ""

#: pro/fields/class-acf-field-clone.php:819
msgid "Select one or more fields you wish to clone"
msgstr "Välj ett eller flera fält som du vill klona"

#: pro/fields/class-acf-field-clone.php:838
msgid "Display"
msgstr "Visning"

#: pro/fields/class-acf-field-clone.php:839
msgid "Specify the style used to render the clone field"
msgstr "Specificera stilen som ska användas för att rendera det klonade fältet"

#: pro/fields/class-acf-field-clone.php:844
msgid "Group (displays selected fields in a group within this field)"
msgstr "Grupp (visar valda fält i en grupp i detta fält)"

#: pro/fields/class-acf-field-clone.php:845
msgid "Seamless (replaces this field with selected fields)"
msgstr "Sömlös (ersätter detta fält med valda fält)"

#: pro/fields/class-acf-field-clone.php:868
msgid "Labels will be displayed as %s"
msgstr "Etiketter kommer att visas som %s"

#: pro/fields/class-acf-field-clone.php:873
msgid "Prefix Field Labels"
msgstr "Prefix för fältetiketter"

#: pro/fields/class-acf-field-clone.php:883
msgid "Values will be saved as %s"
msgstr "Värden sparas som %s"

#: pro/fields/class-acf-field-clone.php:888
msgid "Prefix Field Names"
msgstr "Prefix för fältnamn"

#: pro/fields/class-acf-field-clone.php:1005
msgid "Unknown field"
msgstr "Okänt fält"

#: pro/fields/class-acf-field-clone.php:1042
msgid "Unknown field group"
msgstr "Okänd fältgrupp"

#: pro/fields/class-acf-field-clone.php:1046
msgid "All fields from %s field group"
msgstr "Alla fält från %s fältgrupp"

#: pro/fields/class-acf-field-flexible-content.php:27
msgid ""
"Allows you to define, create and manage content with total control by "
"creating layouts that contain subfields that content editors can choose from."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:36,
#: pro/fields/class-acf-field-repeater.php:103,
#: pro/fields/class-acf-field-repeater.php:297
msgid "Add Row"
msgstr "Lägg till rad"

#: pro/fields/class-acf-field-flexible-content.php:76,
#: pro/fields/class-acf-field-flexible-content.php:943,
#: pro/fields/class-acf-field-flexible-content.php:1022
msgid "layout"
msgid_plural "layouts"
msgstr[0] "layout"
msgstr[1] "layouter"

#: pro/fields/class-acf-field-flexible-content.php:77
msgid "layouts"
msgstr "layouter"

#: pro/fields/class-acf-field-flexible-content.php:81,
#: pro/fields/class-acf-field-flexible-content.php:942,
#: pro/fields/class-acf-field-flexible-content.php:1021
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Detta fält kräver minst {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Detta fält har en gräns på {max} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} tillgänglig (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:86
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} krävs (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:89
msgid "Flexible Content requires at least 1 layout"
msgstr "Flexibelt innehåll kräver minst 1 layout"

#: pro/fields/class-acf-field-flexible-content.php:282
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Klicka på knappen ”%s” nedan för att börja skapa din layout"

#: pro/fields/class-acf-field-flexible-content.php:423
msgid "Add layout"
msgstr "Lägg till layout"

#: pro/fields/class-acf-field-flexible-content.php:424
msgid "Duplicate layout"
msgstr "Duplicera layout"

#: pro/fields/class-acf-field-flexible-content.php:425
msgid "Remove layout"
msgstr "Ta bort layout"

#: pro/fields/class-acf-field-flexible-content.php:426,
#: pro/fields/class-acf-repeater-table.php:382
msgid "Click to toggle"
msgstr "Klicka för att växla"

#: pro/fields/class-acf-field-flexible-content.php:562
msgid "Delete Layout"
msgstr "Ta bort layout"

#: pro/fields/class-acf-field-flexible-content.php:563
msgid "Duplicate Layout"
msgstr "Duplicera layout"

#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Add New Layout"
msgstr "Lägg till ny layout"

#: pro/fields/class-acf-field-flexible-content.php:564
#, fuzzy
#| msgid "Add layout"
msgid "Add Layout"
msgstr "Lägg till layout"

#: pro/fields/class-acf-field-flexible-content.php:647
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:662
msgid "Max"
msgstr "Max"

#: pro/fields/class-acf-field-flexible-content.php:705
msgid "Minimum Layouts"
msgstr "Lägsta tillåtna antal layouter"

#: pro/fields/class-acf-field-flexible-content.php:716
msgid "Maximum Layouts"
msgstr "Högsta tillåtna antal layouter"

#: pro/fields/class-acf-field-flexible-content.php:727,
#: pro/fields/class-acf-field-repeater.php:293
msgid "Button Label"
msgstr "Knappetikett"

#: pro/fields/class-acf-field-flexible-content.php:1710,
#: pro/fields/class-acf-field-repeater.php:918
msgid "%s must be of type array or null."
msgstr "%s måste vara av typen array eller null."

#: pro/fields/class-acf-field-flexible-content.php:1721
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] "%1$s måste innehålla minst %2$s %3$s layout."
msgstr[1] "%1$s måste innehålla minst %2$s %3$s layouter."

#: pro/fields/class-acf-field-flexible-content.php:1737
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] "%1$s får innehålla högst %2$s %3$s layout."
msgstr[1] "%1$s får innehålla högst %2$s %3$s layouter."

#: pro/fields/class-acf-field-gallery.php:27
msgid ""
"An interactive interface for managing a collection of attachments, such as "
"images."
msgstr ""

#: pro/fields/class-acf-field-gallery.php:77
msgid "Add Image to Gallery"
msgstr "Lägg till bild i galleriet"

#: pro/fields/class-acf-field-gallery.php:78
msgid "Maximum selection reached"
msgstr "Högsta tillåtna antal val uppnått"

#: pro/fields/class-acf-field-gallery.php:324
msgid "Length"
msgstr "Längd"

#: pro/fields/class-acf-field-gallery.php:368
msgid "Caption"
msgstr "Bildtext"

#: pro/fields/class-acf-field-gallery.php:380
msgid "Alt Text"
msgstr "Alternativ text"

#: pro/fields/class-acf-field-gallery.php:504
msgid "Add to gallery"
msgstr "Lägg till i galleri"

#: pro/fields/class-acf-field-gallery.php:508
msgid "Bulk actions"
msgstr "Massåtgärder"

#: pro/fields/class-acf-field-gallery.php:509
msgid "Sort by date uploaded"
msgstr "Sortera efter uppladdningsdatum"

#: pro/fields/class-acf-field-gallery.php:510
msgid "Sort by date modified"
msgstr "Sortera efter redigeringsdatum"

#: pro/fields/class-acf-field-gallery.php:511
msgid "Sort by title"
msgstr "Sortera efter rubrik"

#: pro/fields/class-acf-field-gallery.php:512
msgid "Reverse current order"
msgstr "Omvänd nuvarande ordning"

#: pro/fields/class-acf-field-gallery.php:524
msgid "Close"
msgstr "Stäng"

#: pro/fields/class-acf-field-gallery.php:615
msgid "Minimum Selection"
msgstr "Minsta tillåtna antal val"

#: pro/fields/class-acf-field-gallery.php:625
msgid "Maximum Selection"
msgstr "Högsta tillåtna antal val"

#: pro/fields/class-acf-field-gallery.php:707
msgid "Allowed file types"
msgstr "Tillåtna filtyper"

#: pro/fields/class-acf-field-gallery.php:727
msgid "Insert"
msgstr "Infoga"

#: pro/fields/class-acf-field-gallery.php:728
msgid "Specify where new attachments are added"
msgstr "Specifiera var nya bilagor läggs till"

#: pro/fields/class-acf-field-gallery.php:732
msgid "Append to the end"
msgstr "Lägg till i slutet"

#: pro/fields/class-acf-field-gallery.php:733
msgid "Prepend to the beginning"
msgstr "Lägg till början"

#: pro/fields/class-acf-field-repeater.php:66,
#: pro/fields/class-acf-field-repeater.php:463
#, fuzzy
#| msgid "Minimum rows reached ({min} rows)"
msgid "Minimum rows not reached ({min} rows)"
msgstr "Minsta tillåtna antal rader uppnått ({min} rader)"

#: pro/fields/class-acf-field-repeater.php:67
msgid "Maximum rows reached ({max} rows)"
msgstr "Högsta tillåtna antal rader uppnått ({max} rader)"

#: pro/fields/class-acf-field-repeater.php:68
msgid "Error loading page"
msgstr "Kunde inte ladda in sida"

#: pro/fields/class-acf-field-repeater.php:69
msgid "Order will be assigned upon save"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:196
msgid "Useful for fields with a large number of rows."
msgstr "Användbart för fält med ett stort antal rader."

#: pro/fields/class-acf-field-repeater.php:207
msgid "Rows Per Page"
msgstr "Rader per sida"

#: pro/fields/class-acf-field-repeater.php:208
msgid "Set the number of rows to be displayed on a page."
msgstr "Ange antalet rader som ska visas på en sida."

#: pro/fields/class-acf-field-repeater.php:240
msgid "Minimum Rows"
msgstr "Minsta tillåtna antal rader"

#: pro/fields/class-acf-field-repeater.php:251
msgid "Maximum Rows"
msgstr "Högsta tillåtna antal rader"

#: pro/fields/class-acf-field-repeater.php:281
msgid "Collapsed"
msgstr "Ihopfälld"

#: pro/fields/class-acf-field-repeater.php:282
msgid "Select a sub field to show when row is collapsed"
msgstr "Välj ett underfält att visa när raden är ihopfälld"

#: pro/fields/class-acf-field-repeater.php:1060
#, fuzzy
#| msgid "Invalid field key."
msgid "Invalid field key or name."
msgstr "Ogiltig fältnyckel."

#: pro/fields/class-acf-field-repeater.php:1069
msgid "There was an error retrieving the field."
msgstr "Ett fel uppstod vid hämtning av fältet."

#: pro/fields/class-acf-repeater-table.php:369
#, fuzzy
#| msgid "Drag to reorder"
msgid "Click to reorder"
msgstr "Dra och släpp för att ändra ordning"

#: pro/fields/class-acf-repeater-table.php:402
msgid "Add row"
msgstr "Lägg till rad"

#: pro/fields/class-acf-repeater-table.php:403
msgid "Duplicate row"
msgstr "Duplicera rad"

#: pro/fields/class-acf-repeater-table.php:404
msgid "Remove row"
msgstr "Ta bort rad"

#: pro/fields/class-acf-repeater-table.php:448,
#: pro/fields/class-acf-repeater-table.php:465,
#: pro/fields/class-acf-repeater-table.php:466
msgid "Current Page"
msgstr "Nuvarande sida"

#: pro/fields/class-acf-repeater-table.php:456,
#: pro/fields/class-acf-repeater-table.php:457
#, fuzzy
#| msgid "First page"
msgid "First Page"
msgstr "Första sidan"

#: pro/fields/class-acf-repeater-table.php:460,
#: pro/fields/class-acf-repeater-table.php:461
#, fuzzy
#| msgid "Previous page"
msgid "Previous Page"
msgstr "Föregående sida"

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:470
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr "%1$s av %2$s"

#: pro/fields/class-acf-repeater-table.php:477,
#: pro/fields/class-acf-repeater-table.php:478
#, fuzzy
#| msgid "Next page"
msgid "Next Page"
msgstr "Nästa sida"

#: pro/fields/class-acf-repeater-table.php:481,
#: pro/fields/class-acf-repeater-table.php:482
#, fuzzy
#| msgid "Last page"
msgid "Last Page"
msgstr "Sista sidan"

#: pro/locations/class-acf-location-block.php:71
msgid "No block types exist"
msgstr "Det finns inga blocktyper"

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "Det finns inga alternativsidor"

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "Inaktivera licens"

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "Aktivera licens"

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr "Licensinformation"

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"För att låsa upp uppdateringar, fyll i din licensnyckel här nedan. Om du "
"inte har en licensnyckel, gå till sidan <a href=\"%s\">detaljer och priser</"
"a>."

#: pro/admin/views/html-settings-updates.php:37
msgid "License Key"
msgstr "Licensnyckel"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr "Din licensnyckel är angiven i wp-config.php."

#: pro/admin/views/html-settings-updates.php:29
msgid "Retry Activation"
msgstr "Försök aktivera igen"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Uppdateringsinformation"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Nuvarande version"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Senaste version"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Uppdatering tillgänglig"

#: pro/admin/views/html-settings-updates.php:98
msgid "Upgrade Notice"
msgstr "Uppgraderingsnotering"

#: pro/admin/views/html-settings-updates.php:126
msgid "Check For Updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:121
#, fuzzy
#| msgid "Please enter your license key above to unlock updates"
msgid "Enter your license key to unlock updates"
msgstr "Fyll i din licensnyckel här ovan för att låsa upp uppdateringar"

#: pro/admin/views/html-settings-updates.php:119
msgid "Update Plugin"
msgstr "Uppdatera tillägg"

#: pro/admin/views/html-settings-updates.php:117
msgid "Please reactivate your license to unlock updates"
msgstr "Återaktivera din licens för att låsa upp uppdateringar"
