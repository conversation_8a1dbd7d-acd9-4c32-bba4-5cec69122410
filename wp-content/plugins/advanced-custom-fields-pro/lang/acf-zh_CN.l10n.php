<?php
return ['domain'=>NULL,'plural-forms'=>NULL,'language'=>'zh_CN','project-id-version'=>'Advanced Custom Fields','pot-creation-date'=>'2024-08-01T09:58:52+00:00','po-revision-date'=>'2024-08-01T09:50:26+00:00','x-generator'=>'gettext','messages'=>['%s requires a valid attachment ID when type is set to media_library.'=>'当类型设置为 media_library 时，%s 需要有效的附件 ID。','%s is a required property of acf.'=>'%s 是 acf 的必需属性。','The value of icon to save.'=>'要保存的图标的值。','The type of icon to save.'=>'要保存的图标的类型。','Yes Icon'=>'是的图标','WordPress Icon'=>'Wordpress 图标','Warning Icon'=>'警告图标','Visibility Icon'=>'可见性图标','Vault Icon'=>'Vault 图标','Upload Icon'=>'上传图标','Update Icon'=>'更新图标','Unlock Icon'=>'解锁图标','Universal Access Icon'=>'通用访问图标','Undo Icon'=>'撤消图标','Twitter Icon'=>'Twitter 图标','Trash Icon'=>'回收站图标','Translation Icon'=>'翻译图标','Tickets Icon'=>'Tickets 图标','Thumbs Up Icon'=>'点赞图标','Thumbs Down Icon'=>'点踩图标','Text Icon'=>'文本图标','Testimonial Icon'=>'感言图标','Tagcloud Icon'=>'标签云图标','Tag Icon'=>'标签图标','Tablet Icon'=>'平板电脑图标','Store Icon'=>'商店图标','Sticky Icon'=>'置顶图标','Star Half Icon'=>'半星图标','The core ACF block binding source name for fields on the current pageACF Fields'=>'ACF 字段','ACF PRO Feature'=>'ACF PRO 功能','Renew PRO to Unlock'=>'续订 PRO 即可解锁','Renew PRO License'=>'更新 PRO 许可证','PRO fields cannot be edited without an active license.'=>'如果没有有效许可证，则无法编辑 PRO 字段。','Please activate your ACF PRO license to edit field groups assigned to an ACF Block.'=>'请激活您的 ACF PRO 许可证以编辑分配给 ACF 块的字段组。','Please activate your ACF PRO license to edit this options page.'=>'请激活您的 ACF PRO 许可证才能编辑此选项页面。','Returning escaped HTML values is only possible when format_value is also true. The field values have not been returned for security.'=>'仅当 format_value 也为 true 时，才可以返回转义的 HTML 值。为了安全起见，字段值尚未返回。','Returning an escaped HTML value is only possible when format_value is also true. The field value has not been returned for security.'=>'仅当 format_value 也为 true 时，才可以返回转义的 HTML 值。为了安全起见，该字段值尚未返回。','%1$s ACF now automatically escapes unsafe HTML when rendered by <code>the_field</code> or the ACF shortcode. We\'ve detected the output of some of your fields has been modified by this change, but this may not be a breaking change. %2$s.'=>'%1$s ACF 现在在由 <code>the_field</code> 或 ACF 短代码呈现时自动转义不安全的 HTML。我们检测到您的某些字段的输出已被此更改修改，但这可能不是重大更改。 %2$s。 %3$s。','Please contact your site administrator or developer for more details.'=>'请联系您的网站管理员或开发人员了解更多详细信息。','Learn&nbsp;more'=>'了解更多','Hide&nbsp;details'=>'隐藏详情','Show&nbsp;details'=>'显示详情','%1$s (%2$s) - rendered via %3$s'=>'%1$s (%2$s) - 通过 %3$s 呈现','Renew ACF PRO License'=>'续订 ACF PRO 许可证','Renew License'=>'更新许可证','Manage License'=>'管理许可证','\'High\' position not supported in the Block Editor'=>'块编辑器不支持“高”位置','Upgrade to ACF PRO'=>'升级到 ACF PRO','ACF <a href="%s" target="_blank">options pages</a> are custom admin pages for managing global settings via fields. You can create multiple pages and sub-pages.'=>'ACF<a href="%s" target="_blank">选项页</a>是通过字段管理全局设置的自定义管理页。您可以创建多个页面和子页面。','Add Options Page'=>'添加选项页面','In the editor used as the placeholder of the title.'=>'在编辑器中用作标题的占位符。','Title Placeholder'=>'标题占位符','4 Months Free'=>'4个月免费','(Duplicated from %s)'=>' （复制自 %s）','Select Options Pages'=>'选择选项页面','Duplicate taxonomy'=>'克隆分类法','Create taxonomy'=>'创建分类法','Duplicate post type'=>'克隆文章类型','Create post type'=>'创建文章类型','Link field groups'=>'链接字段组','Add fields'=>'添加字段','This Field'=>'这个字段','ACF PRO'=>'ACF PRO','Feedback'=>'反馈','Support'=>'支持','is developed and maintained by'=>'开发和维护者','Add this %s to the location rules of the selected field groups.'=>'将此 %s 添加到所选字段组的位置规则中。','Enabling the bidirectional setting allows you to update a value in the target fields for each value selected for this field, adding or removing the Post ID, Taxonomy ID or User ID of the item being updated. For more information, please read the <a href="%s" target="_blank">documentation</a>.'=>'启用双向设置允许您更新为此字段选择的每个值的目标字段中的值，添加或删除正在更新的项目的文章 ID、分类法 ID 或用户 ID。有关更多信息，请阅读<a href="%s" target="_blank">文档</a>。','Select field(s) to store the reference back to the item being updated. You may select this field. Target fields must be compatible with where this field is being displayed. For example, if this field is displayed on a Taxonomy, your target field should be of type Taxonomy'=>'选择字段以将引用存储回正在更新的项目。您可以选择该字段。目标字段必须与该字段的显示位置兼容。例如，如果此字段显示在分类法上，则您的目标字段应为分类法类型','Target Field'=>'目标字段','Update a field on the selected values, referencing back to this ID'=>'更新所选值的字段，引用回此 ID','Bidirectional'=>'双向','%s Field'=>'%s 字段','Select Multiple'=>'选择多个','WP Engine logo'=>'WP Engine logo','Lower case letters, underscores and dashes only, Max 32 characters.'=>'仅小写字母、下划线和破折号，最多 32 个字符。','The capability name for assigning terms of this taxonomy.'=>'用于分配此分类的术语的功能名称。','Assign Terms Capability'=>'分配分类项能力','The capability name for deleting terms of this taxonomy.'=>'用于删除该分类法的分类项的功能名称。','Delete Terms Capability'=>'删除分类项功能','The capability name for editing terms of this taxonomy.'=>'用于编辑该分类法的分类项的功能名称。','Edit Terms Capability'=>'编辑分类项能力','The capability name for managing terms of this taxonomy.'=>'用于管理该分类法的分类项的功能名称。','Manage Terms Capability'=>'管理分类项能力','Sets whether posts should be excluded from search results and taxonomy archive pages.'=>'设置帖子是否应从搜索结果和分类存档页面中排除。','More Tools from WP Engine'=>'WP Engine 的更多工具','Built for those that build with WordPress, by the team at %s'=>'由 %s 团队专为使用 WordPress 构建的用户而构建','View Pricing & Upgrade'=>'查看定价和升级','Learn More'=>'了解更多','Speed up your workflow and develop better websites with features like ACF Blocks and Options Pages, and sophisticated field types like Repeater, Flexible Content, Clone, and Gallery.'=>'利用 ACF 块和选项页面等功能以及循环、弹性内容、克隆和图库等复杂的字段类型，加快您的工作流程并开发更好的网站。','Unlock Advanced Features and Build Even More with ACF PRO'=>'使用 ACF PRO 解锁高级功能并构建更多功能','%s fields'=>'%s 字段','No terms'=>'无分类项','No post types'=>'无文章类型','No posts'=>'无文章','No taxonomies'=>'无分类','No field groups'=>'无字段分组','No fields'=>'无字段','No description'=>'无描述','Any post status'=>'任何文章状态','This taxonomy key is already in use by another taxonomy registered outside of ACF and cannot be used.'=>'这个分类标准的关键字已经被ACF以外注册的另一个分类标准所使用，不能使用。','This taxonomy key is already in use by another taxonomy in ACF and cannot be used.'=>'此分类法已被 ACF 中的另一个分类法使用，因此无法使用。','The taxonomy key must only contain lower case alphanumeric characters, underscores or dashes.'=>'分类法只能包含小写字母数字字符、下划线或破折号。','The taxonomy key must be under 32 characters.'=>'分类法必须少于 32 个字符。','No Taxonomies found in Trash'=>'回收站中未找到分类法','No Taxonomies found'=>'未找到分类法','Search Taxonomies'=>'搜索分类法','View Taxonomy'=>'查看分类法','New Taxonomy'=>'新分类法','Edit Taxonomy'=>'编辑分类法','Add New Taxonomy'=>'新增分类法','No Post Types found in Trash'=>'在回收站中找不到文章类型','No Post Types found'=>'找不到文章类型','Search Post Types'=>'搜索文章类型','View Post Type'=>'查看文章类型','New Post Type'=>'新文章类型','Edit Post Type'=>'编辑文章类型','Add New Post Type'=>'新增文章类型','This post type key is already in use by another post type registered outside of ACF and cannot be used.'=>'此帖子类型密钥已被 ACF 外部注册的另一个帖子类型使用，因此无法使用。','This post type key is already in use by another post type in ACF and cannot be used.'=>'此帖子类型密钥已被 ACF 中的另一个帖子类型使用，无法使用。','This field must not be a WordPress <a href="%s" target="_blank">reserved term</a>.'=>'此字段不得是 WordPress <a href="%s" target="_blank">保留词</a>。','The post type key must only contain lower case alphanumeric characters, underscores or dashes.'=>'帖子类型键只能包含小写字母数字字符、下划线或破折号。','The post type key must be under 20 characters.'=>'帖子类型键必须少于 20 个字符。','We do not recommend using this field in ACF Blocks.'=>'我们不建议在 ACF 块中使用此字段。','Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing for a rich text-editing experience that also allows for multimedia content.'=>'显示 WordPress 可视化编辑器，如文章和页面中所示，可提供丰富的文本编辑体验，还支持多媒体内容。','WYSIWYG Editor'=>'可视化编辑器','Allows the selection of one or more users which can be used to create relationships between data objects.'=>'允许选择一个或多个可用于在数据对象之间创建关系的用户。','A text input specifically designed for storing web addresses.'=>'专门为存储网址而设计的文本输入。','URL'=>'URL','A toggle that allows you to pick a value of 1 or 0 (on or off, true or false, etc). Can be presented as a stylized switch or checkbox.'=>'允许您选择值 1 或 0（打开或关闭、true 或 false 等）的切换开关。可以呈现为程式化的开关或复选框。','An interactive UI for picking a time. The time format can be customized using the field settings.'=>'用于选择时间的交互式 UI。可以使用字段设置自定义时间格式。','A basic textarea input for storing paragraphs of text.'=>'用于存储文本段落的基本文本区域输入。','A basic text input, useful for storing single string values.'=>'基本文本输入，可用于存储单个字符串值。','Allows the selection of one or more taxonomy terms based on the criteria and options specified in the fields settings.'=>'允许根据字段设置中指定的条件和选项选择一个或多个分类术语。','Allows you to group fields into tabbed sections in the edit screen. Useful for keeping fields organized and structured.'=>'允许您将字段分组到编辑屏幕中的选项卡部分。对于保持字段的组织性和结构化很有用。','A dropdown list with a selection of choices that you specify.'=>'包含您指定的选项的下拉列表。','A dual-column interface to select one or more posts, pages, or custom post type items to create a relationship with the item that you\'re currently editing. Includes options to search and filter.'=>'双栏界面，用于选择一个或多个帖子、页面或自定义帖子类型项目，以创建与您当前正在编辑的项目的关系。包括搜索和过滤选项。','An input for selecting a numerical value within a specified range using a range slider element.'=>'用于使用范围滑块元素选择指定范围内的数值的输入。','A group of radio button inputs that allows the user to make a single selection from values that you specify.'=>'一组单选按钮输入，允许用户从您指定的值中进行单一选择。','An interactive and customizable UI for picking one or many posts, pages or post type items with the option to search. '=>'一种交互式且可定制的用户界面，用于选择一个或多个帖子、页面或帖子类型项目，并提供搜索选项。 ','An input for providing a password using a masked field.'=>'使用屏蔽字段提供密码的输入。','Filter by Post Status'=>'按文章状态过滤','An interactive dropdown to select one or more posts, pages, custom post type items or archive URLs, with the option to search.'=>'交互式下拉菜单，用于选择一个或多个帖子、页面、自定义帖子类型项目或存档 URL，并提供搜索选项。','An interactive component for embedding videos, images, tweets, audio and other content by making use of the native WordPress oEmbed functionality.'=>'一个交互式组件，用于通过使用本机 WordPress oEmbed 功能来嵌入视频、图像、推文、音频和其他内容。','An input limited to numerical values.'=>'输入仅限于数值。','Used to display a message to editors alongside other fields. Useful for providing additional context or instructions around your fields.'=>'用于与其他字段一起向编辑者显示消息。对于提供有关您的字段的附加上下文或说明很有用。','Allows you to specify a link and its properties such as title and target using the WordPress native link picker.'=>'允许您使用 WordPress 本机链接选择器指定链接及其属性，例如标题和目标。','Uses the native WordPress media picker to upload, or choose images.'=>'使用本机 WordPress 媒体选择器上传或选择图像。','Provides a way to structure fields into groups to better organize the data and the edit screen.'=>'提供一种将字段组织为组的方法，以更好地组织数据和编辑屏幕。','An interactive UI for selecting a location using Google Maps. Requires a Google Maps API key and additional configuration to display correctly.'=>'用于使用 Google 地图选择位置的交互式 UI。需要 Google 地图 API 密钥和其他配置才能正确显示。','Uses the native WordPress media picker to upload, or choose files.'=>'使用本机 WordPress 媒体选择器上传或选择文件。','A text input specifically designed for storing email addresses.'=>'专门用于存储电子邮件地址的文本输入。','An interactive UI for picking a date and time. The date return format can be customized using the field settings.'=>'用于选择日期和时间的交互式 UI。可以使用字段设置自定义日期返回格式。','An interactive UI for picking a date. The date return format can be customized using the field settings.'=>'用于选择日期的交互式用户界面。可以使用字段设置自定义日期返回格式。','An interactive UI for selecting a color, or specifying a Hex value.'=>'用于选择颜色或指定十六进制值的交互式 UI。','A group of checkbox inputs that allow the user to select one, or multiple values that you specify.'=>'一组复选框输入，允许用户选择您指定的一个或多个值。','A group of buttons with values that you specify, users can choose one option from the values provided.'=>'一组具有您指定的值的按钮，用户可以从提供的值中选择一个选项。','Allows you to group and organize custom fields into collapsable panels that are shown while editing content. Useful for keeping large datasets tidy.'=>'允许您将自定义字段分组并组织到可折叠面板中，这些面板在编辑内容时显示。对于保持大型数据集整洁很有用。','This provides a solution for repeating content such as slides, team members, and call-to-action tiles, by acting as a parent to a set of subfields which can be repeated again and again.'=>'这提供了一种通过充当一组可以一次又一次重复的子字段的父字段来重复幻灯片、团队成员和号召性用语图块等内容的解决方案。','This provides an interactive interface for managing a collection of attachments. Most settings are similar to the Image field type. Additional settings allow you to specify where new attachments are added in the gallery and the minimum/maximum number of attachments allowed.'=>'这提供了用于管理附件集合的交互式界面。大多数设置与图像字段类型类似。其他设置允许您指定在库中添加新附件的位置以及允许的最小/最大附件数量。','This provides a simple, structured, layout-based editor. The Flexible Content field allows you to define, create and manage content with total control by using layouts and subfields to design the available blocks.'=>'这提供了一个简单、结构化、基于布局的编辑器。弹性内容字段允许您通过使用布局和子字段来设计可用块来定义、创建和管理具有完全控制的内容。','This allows you to select and display existing fields. It does not duplicate any fields in the database, but loads and displays the selected fields at run-time. The Clone field can either replace itself with the selected fields or display the selected fields as a group of subfields.'=>'这允许您选择并显示现有字段。它不会复制数据库中的任何字段，而是在运行时加载并显示所选字段。克隆字段可以用所选字段替换自身，也可以将所选字段显示为一组子字段。','nounClone'=>'克隆','PRO'=>'PRO','Advanced'=>'高级','JSON (newer)'=>'JSON (新)','Original'=>'原始','Invalid post ID.'=>'文章 ID 无效。','Invalid post type selected for review.'=>'选择进行审核的帖子类型无效。','More'=>'更多','Tutorial'=>'教程','Select Field'=>'选择字段','Try a different search term or browse %s'=>'尝试不同的搜索词或浏览 %s','Popular fields'=>'热门字段','No search results for \'%s\''=>'没有搜索到“%s”结果','Search fields...'=>'搜索字段...','Select Field Type'=>'选择字段类型','Popular'=>'热门','Add Taxonomy'=>'添加分类法','Create custom taxonomies to classify post type content'=>'创建自定义分类法对文章类型内容进行分类','Add Your First Taxonomy'=>'添加您的第一个分类法','Hierarchical taxonomies can have descendants (like categories).'=>'分层分类法可以有后代（如类别）。','Makes a taxonomy visible on the frontend and in the admin dashboard.'=>'使分类在前端和管理仪表板中可见。','One or many post types that can be classified with this taxonomy.'=>'可以使用此分类法对一种或多种文章类型进行分类。','genre'=>'类型','Genre'=>'类型','Genres'=>'类型','Optional custom controller to use instead of `WP_REST_Terms_Controller `.'=>'使用可选的自定义控制器来代替“WP_REST_Terms_Controller”。','Expose this post type in the REST API.'=>'在 REST API 中公开此文章类型。','Customize the query variable name'=>'自定义查询变量名','Terms can be accessed using the non-pretty permalink, e.g., {query_var}={term_slug}.'=>'可以使用非漂亮的永久链接访问术语，例如 {query_var}={term_slug}。','Parent-child terms in URLs for hierarchical taxonomies.'=>'分层分类法 URL 中的父子术语。','Customize the slug used in the URL'=>'自定义 URL 中使用的 slug','Permalinks for this taxonomy are disabled.'=>'此分类的永久链接已禁用。','Rewrite the URL using the taxonomy key as the slug. Your permalink structure will be'=>'使用分类键作为 slug 重写 URL。您的永久链接结构将是','Taxonomy Key'=>'分类法键','Select the type of permalink to use for this taxonomy.'=>'选择用于此分类的永久链接类型。','Display a column for the taxonomy on post type listing screens.'=>'在帖子类型列表屏幕上显示分类法列。','Show Admin Column'=>'显示管理栏','Show the taxonomy in the quick/bulk edit panel.'=>'在快速/批量编辑面板中显示分类。','Quick Edit'=>'快速编辑','List the taxonomy in the Tag Cloud Widget controls.'=>'列出标签云小部件控件中的分类法。','Tag Cloud'=>'标签云','A PHP function name to be called for sanitizing taxonomy data saved from a meta box.'=>'要调用的 PHP 函数名称，用于清理从元框中保存的分类数据。','Meta Box Sanitization Callback'=>'Meta Box 清理回调','A PHP function name to be called to handle the content of a meta box on your taxonomy.'=>'要调用的 PHP 函数名称来处理分类法上元框的内容。','Register Meta Box Callback'=>'注册元框回调','No Meta Box'=>'无  Meta Box','Custom Meta Box'=>'自定义  Meta Box','Controls the meta box on the content editor screen. By default, the Categories meta box is shown for hierarchical taxonomies, and the Tags meta box is shown for non-hierarchical taxonomies.'=>'控制内容编辑器屏幕上的元框。默认情况下，对于分层分类法显示“类别”元框，对于非分层分类法显示“标签”元框。','Meta Box'=>'Meta Box','Categories Meta Box'=>'分类 Meta Box','Tags Meta Box'=>'标签  Meta Box','A link to a tag'=>'指向标签的链接','Describes a navigation link block variation used in the block editor.'=>'描述块编辑器中使用的导航链接块变体。','A link to a %s'=>'%s 的链接','Tag Link'=>'标签链接','Assigns a title for navigation link block variation used in the block editor.'=>'为块编辑器中使用的导航链接块变体分配标题。','← Go to tags'=>'← 转到标签','Assigns the text used to link back to the main index after updating a term.'=>'在更新分类项后，指定用于链接回主索引的文本。','Back To Items'=>'返回项目','← Go to %s'=>'← 前往 %s','Tags list'=>'标签列表','Assigns text to the table hidden heading.'=>'将文本分配给表格隐藏标题。','Tags list navigation'=>'标签列表导航','Assigns text to the table pagination hidden heading.'=>'将文本分配给表格分页隐藏标题。','Filter by category'=>'按类别过滤','Assigns text to the filter button in the posts lists table.'=>'将文本分配给帖子列表中的过滤器按钮。','Filter By Item'=>'按项目过滤','Filter by %s'=>'按 %s 过滤','The description is not prominent by default; however, some themes may show it.'=>'默认情况下描述不突出；但是，某些主题可能会显示它。','Describes the Description field on the Edit Tags screen.'=>'描述编辑标签屏幕上的描述字段。','Description Field Description'=>'描述 字段 描述','Assign a parent term to create a hierarchy. The term Jazz, for example, would be the parent of Bebop and Big Band'=>'指定父分类项以创建层次结构。例如，“爵士乐”一词就是“Bebop”和“Big Band”的父词','Describes the Parent field on the Edit Tags screen.'=>'描述编辑标签屏幕上的父字段。','Parent Field Description'=>'父字段描述','The "slug" is the URL-friendly version of the name. It is usually all lower case and contains only letters, numbers, and hyphens.'=>'“slug”是该名称的 URL 友好版本。它通常全部小写，仅包含字母、数字和连字符。','Describes the Slug field on the Edit Tags screen.'=>'描述编辑标签屏幕上的 Slug 字段。','Slug Field Description'=>'Slug 字段描述','The name is how it appears on your site'=>'该名称是它在您网站上的显示方式','Describes the Name field on the Edit Tags screen.'=>'描述编辑标签屏幕上的名称字段。','Name Field Description'=>'名称 字段 描述','No tags'=>'无标签','Assigns the text displayed in the posts and media list tables when no tags or categories are available.'=>'当没有可用标签或类别时，分配帖子和媒体列表中显示的文本。','No Terms'=>'无分类项','No %s'=>'无 %s','No tags found'=>'未找到标签','Assigns the text displayed when clicking the \'choose from most used\' text in the taxonomy meta box when no tags are available, and assigns the text used in the terms list table when there are no items for a taxonomy.'=>'当没有可用标签时，分配在分类元框中单击“从最常用的选择”文本时显示的文本，并在没有分类项时分配术语列表中使用的文本。','Not Found'=>'未找到','Assigns text to the Title field of the Most Used tab.'=>'将文本分配给最常用选项卡的标题字段。','Most Used'=>'最常被使用','Choose from the most used tags'=>'从最常用的标签中选择','Assigns the \'choose from most used\' text used in the meta box when JavaScript is disabled. Only used on non-hierarchical taxonomies.'=>'当禁用 JavaScript 时，指定元框中使用的“从最常用的选项中选择”文本。仅用于非层次分类法。','Choose From Most Used'=>'从最常用的中选择','Choose from the most used %s'=>'从最常用的 %s 中选择','Add or remove tags'=>'添加或删除标签','Assigns the add or remove items text used in the meta box when JavaScript is disabled. Only used on non-hierarchical taxonomies'=>'禁用 JavaScript 时分配在元框中使用的添加或删除项目文本。仅用于非层次分类法','Add Or Remove Items'=>'添加或删除项目','Add or remove %s'=>'添加或删除%s','Separate tags with commas'=>'用逗号分隔标签','Assigns the separate item with commas text used in the taxonomy meta box. Only used on non-hierarchical taxonomies.'=>'使用分类元框中使用的逗号文本分配单独的项目。仅用于非层次分类法。','Separate Items With Commas'=>'用逗号分隔项目','Separate %s with commas'=>'用逗号分隔 %s','Popular Tags'=>'热门标签','Assigns popular items text. Only used for non-hierarchical taxonomies.'=>'分配热门项目文本。仅用于非层次分类法。','Popular Items'=>'热门项目','Popular %s'=>'热门 %s','Search Tags'=>'搜索标签','Assigns search items text.'=>'分配搜索项文本。','Parent Category:'=>'父级分类：','Assigns parent item text, but with a colon (:) added to the end.'=>'分配父项文本，但在末尾添加冒号 (:)。','Parent Item With Colon'=>'带冒号的父项','Parent Category'=>'父类别','Assigns parent item text. Only used on hierarchical taxonomies.'=>'分配父项文本。仅用于分层分类法。','Parent Item'=>'父级项目','Parent %s'=>'上级 %s','New Tag Name'=>'新标签名称','Assigns the new item name text.'=>'分配新的项目名称文本。','New Item Name'=>'新项目名称','New %s Name'=>'新 %s 名称','Add New Tag'=>'新增标签','Assigns the add new item text.'=>'分配新的项目名称文本。','Update Tag'=>'更新标签','Assigns the update item text.'=>'分配更新项目文本。','Update Item'=>'更新项目','Update %s'=>'更新 %s','View Tag'=>'查看标签','In the admin bar to view term during editing.'=>'在管理栏中可以在编辑期间查看分类项。','Edit Tag'=>'编辑标签','At the top of the editor screen when editing a term.'=>'编辑分类项时位于编辑器屏幕的顶部。','All Tags'=>'所有标签','Assigns the all items text.'=>'分配所有项目文本。','Assigns the menu name text.'=>'分配菜单名称文本。','Menu Label'=>'菜单标签','Active taxonomies are enabled and registered with WordPress.'=>'活动分类法已启用并在 WordPress 中注册。','A descriptive summary of the taxonomy.'=>'分类法的描述性摘要。','A descriptive summary of the term.'=>'该分类项的描述性摘要。','Term Description'=>'分类项说明','Single word, no spaces. Underscores and dashes allowed.'=>'单个字符串，不能有空格，允许下划线和破折号。','Term Slug'=>'分类项 Slug','The name of the default term.'=>'默认分类项的名称。','Term Name'=>'分类项名称','Create a term for the taxonomy that cannot be deleted. It will not be selected for posts by default.'=>'为无法删除的分类法创建分类项。默认情况下不会选择它来发布帖子。','Default Term'=>'默认分类项','Whether terms in this taxonomy should be sorted in the order they are provided to `wp_set_object_terms()`.'=>'此分类法中的分类项是否应按照提供给“wp_set_object_terms()”的顺序进行排序。','Sort Terms'=>'排序分类项','Add Post Type'=>'添加文章类型','Expand the functionality of WordPress beyond standard posts and pages with custom post types.'=>'使用自定义文章类型将 WordPress 的功能扩展到标准文章和页面之外。','Add Your First Post Type'=>'添加您的第一个文章类型','I know what I\'m doing, show me all the options.'=>'我知道我在做什么，告诉我所有的选择。','Advanced Configuration'=>'高级配置','Hierarchical post types can have descendants (like pages).'=>'分层文章类型可以有后代（如页面）。','Hierarchical'=>'分层','Visible on the frontend and in the admin dashboard.'=>'在前端和管理仪表板中可见。','Public'=>'公开','movie'=>'电影','Lower case letters, underscores and dashes only, Max 20 characters.'=>'仅限小写字母、下划线和破折号，最多 20 个字符。','Movie'=>'电影','Singular Label'=>'单一标签','Movies'=>'电影','Plural Label'=>'复数标签','Optional custom controller to use instead of `WP_REST_Posts_Controller`.'=>'使用可选的自定义控制器来代替“WP_REST_Posts_Controller”。','Controller Class'=>'控制器类','The namespace part of the REST API URL.'=>'REST API URL 的命名空间部分。','Namespace Route'=>'命名空间路由','The base URL for the post type REST API URLs.'=>'文章类型 REST API URL 的基本 URL。','Base URL'=>'基本网址','Exposes this post type in the REST API. Required to use the block editor.'=>'在 REST API 中公开此帖子类型。需要使用块编辑器。','Show In REST API'=>'在 REST API 中显示','Customize the query variable name.'=>'自定义查询变量名称。','Query Variable'=>'查询变量','No Query Variable Support'=>'不支持查询变量','Custom Query Variable'=>'自定义查询变量','Items can be accessed using the non-pretty permalink, eg. {post_type}={post_slug}.'=>'可以使用非漂亮的永久链接访问项目，例如。 {post_type}={post_slug}。','Query Variable Support'=>'查询变量支持','URLs for an item and items can be accessed with a query string.'=>'可以使用查询字符串访问一个或多个项目的 URL。','Publicly Queryable'=>'可公开查询','Custom slug for the Archive URL.'=>'存档 URL 的自定义 slug。','Archive Slug'=>'归档 Slug','Has an item archive that can be customized with an archive template file in your theme.'=>'拥有一个项目存档，可以使用主题中的存档模板文件进行自定义。','Archive'=>'归档','Pagination support for the items URLs such as the archives.'=>'对项目 URL（例如归档）的分页支持。','Pagination'=>'分页','RSS feed URL for the post type items.'=>'文章类型项目的 RSS 源 URL。','Feed URL'=>'Feed 网址','Alters the permalink structure to add the `WP_Rewrite::$front` prefix to URLs.'=>'更改永久链接结构以将 `WP_Rewrite::$front` 前缀添加到 URL。','Front URL Prefix'=>'前面的 URL 前缀','Customize the slug used in the URL.'=>'自定义 URL 中使用的 slug。','URL Slug'=>'URL Slug','Permalinks for this post type are disabled.'=>'此文章类型的永久链接已禁用。','Rewrite the URL using a custom slug defined in the input below. Your permalink structure will be'=>'使用下面输入中定义的自定义 slug 重写 URL。您的永久链接结构将是','No Permalink (prevent URL rewriting)'=>'无固定链接（防止 URL 重写）','Custom Permalink'=>'自定义固定链接','Post Type Key'=>'文章类型键','Rewrite the URL using the post type key as the slug. Your permalink structure will be'=>'使用文章类型键作为 slug 重写 URL。您的永久链接结构将是','Permalink Rewrite'=>'永久链接重写','Delete items by a user when that user is deleted.'=>'删除用户后，删除该用户的项目。','Delete With User'=>'与用户一起删除','Allow the post type to be exported from \'Tools\' > \'Export\'.'=>'允许从“工具”>“导出”导出文章类型。','Can Export'=>'可以导出','Optionally provide a plural to be used in capabilities.'=>'可以选择提供要在功能中使用的复数。','Plural Capability Name'=>'复数能力名称','Choose another post type to base the capabilities for this post type.'=>'选择另一个文章类型以基于此文章类型的功能。','Singular Capability Name'=>'单一能力名称','By default the capabilities of the post type will inherit the \'Post\' capability names, eg. edit_post, delete_posts. Enable to use post type specific capabilities, eg. edit_{singular}, delete_{plural}.'=>'默认情况下，文章类型的功能将继承“文章”功能名称，例如。编辑帖子、删除帖子。允许使用文章类型特定的功能，例如。编辑_{单数}，删除_{复数}。','Rename Capabilities'=>'重命名功能','Exclude From Search'=>'从搜索中排除','Allow items to be added to menus in the \'Appearance\' > \'Menus\' screen. Must be turned on in \'Screen options\'.'=>'允许将项目添加到“外观”>“菜单”屏幕中的菜单。必须在“屏幕选项”中打开。','Appearance Menus Support'=>'外观菜单支持','Appears as an item in the \'New\' menu in the admin bar.'=>'显示为管理栏中“新建”菜单中的一个项目。','Show In Admin Bar'=>'在管理栏中显示','A PHP function name to be called when setting up the meta boxes for the edit screen.'=>'为编辑屏幕设置元框时要调用的 PHP 函数名称。','Custom Meta Box Callback'=>'自定义 Meta Box 回调','Menu Icon'=>'菜单图标','The position in the sidebar menu in the admin dashboard.'=>'管理仪表板侧边栏菜单中的位置。','Menu Position'=>'菜单位置','By default the post type will get a new top level item in the admin menu. If an existing top level item is supplied here, the post type will be added as a submenu item under it.'=>'默认情况下，文章类型将在管理菜单中获得一个新的顶级项目。如果此处提供了现有的顶级项目，则文章类型将作为其下的子菜单项添加。','Admin Menu Parent'=>'管理菜单父级','Admin editor navigation in the sidebar menu.'=>'侧边栏菜单中的管理编辑器导航。','Show In Admin Menu'=>'在管理菜单中显示','Items can be edited and managed in the admin dashboard.'=>'可以在管理仪表板中编辑和管理项目。','Show In UI'=>'在用户界面中显示','A link to a post.'=>'文章的链接。','Description for a navigation link block variation.'=>'导航链接块变体的描述。','Item Link Description'=>'项目链接说明','A link to a %s.'=>'%s 的链接','Post Link'=>'文章链接','Title for a navigation link block variation.'=>'导航链接块变体的标题。','Item Link'=>'项目链接','%s Link'=>'%s 链接','Post updated.'=>'文章已更新。','In the editor notice after an item is updated.'=>'项目更新后在编辑器通知中。','Item Updated'=>'项目已更新。','%s updated.'=>'%s 已更新。','Post scheduled.'=>'文章已计划。','In the editor notice after scheduling an item.'=>'在安排项目后的编辑器通知中。','Item Scheduled'=>'项目已计划','%s scheduled.'=>'%s 已计划。','Post reverted to draft.'=>'文章已恢复为草稿。','In the editor notice after reverting an item to draft.'=>'将项目恢复为草稿后在编辑器通知中。','Item Reverted To Draft'=>'项目恢复为草稿','%s reverted to draft.'=>'%s 已恢复为草稿。','Post published privately.'=>'文章已私密发布。','In the editor notice after publishing a private item.'=>'在编辑发布私密项目后的通知中。','Item Published Privately'=>'私密项目已发布。','%s published privately.'=>'%s 已私密发布','Post published.'=>'文章已发布。','In the editor notice after publishing an item.'=>'在编辑发布项目后的通知中。','Item Published'=>'项目已发布','%s published.'=>'%s 已发布。','Posts list'=>'文章列表','Used by screen readers for the items list on the post type list screen.'=>'由屏幕阅读器用于文章类型列表屏幕上的项目列表。','Items List'=>'项目列表','%s list'=>'%s 列表','Posts list navigation'=>'文章列表导航','Used by screen readers for the filter list pagination on the post type list screen.'=>'由屏幕阅读器用于文章类型列表屏幕上的过滤器列表分页。','Items List Navigation'=>'项目列表导航','%s list navigation'=>'%s 列表导航','Filter posts by date'=>'按日期过滤文章','Used by screen readers for the filter by date heading on the post type list screen.'=>'屏幕阅读器用于按文章类型列表屏幕上的日期标题进行过滤。','Filter Items By Date'=>'按日期过滤项目','Filter %s by date'=>'按日期过滤 %s','Filter posts list'=>'筛选文章列表','Used by screen readers for the filter links heading on the post type list screen.'=>'由屏幕阅读器用于文章类型列表屏幕上的过滤器链接标题。','Filter Items List'=>'筛选项目列表','Filter %s list'=>'筛选 %s 列表','In the media modal showing all media uploaded to this item.'=>'在媒体模式中显示上传到此项目的所有媒体。','Uploaded To This Item'=>'已上传至此项目','Uploaded to this %s'=>'已上传至此 %s','Insert into post'=>'插入至文章','As the button label when adding media to content.'=>'作为向内容添加媒体时的按钮标签。','Insert Into Media Button'=>'插入到媒体按钮','Insert into %s'=>'插入至 %s','Use as featured image'=>'用作特色图像','As the button label for selecting to use an image as the featured image.'=>'作为选择使用图像作为特色图像的按钮标签。','Use Featured Image'=>'使用特色图像','Remove featured image'=>'移除特色图片','As the button label when removing the featured image.'=>'作为删除特色图像时的按钮标签。','Remove Featured Image'=>'移除特色图片','Set featured image'=>'设置特色图像','As the button label when setting the featured image.'=>'作为设置特色图片时的按钮标签。','Set Featured Image'=>'设置特色图像','Featured image'=>'特色图像','In the editor used for the title of the featured image meta box.'=>'在编辑器中用于特色图像 meta box 的标题。','Featured Image Meta Box'=>'特色图像 Meta Box','Post Attributes'=>'文章属性','In the editor used for the title of the post attributes meta box.'=>'在编辑器中用于文章属性 Meta Box 的标题。','Attributes Meta Box'=>'属性 Meta Box','%s Attributes'=>'%s 属性','Post Archives'=>'文章归档','Adds \'Post Type Archive\' items with this label to the list of posts shown when adding items to an existing menu in a CPT with archives enabled. Only appears when editing menus in \'Live Preview\' mode and a custom archive slug has been provided.'=>'将带有此标签的“文章类型存档”项目添加到在启用存档的情况下将项目添加到 CPT 中的现有菜单时显示的文章列表。仅当在“实时预览”模式下编辑菜单并且提供了自定义存档段时才会出现。','Archives Nav Menu'=>'归档导航菜单','%s Archives'=>'%s 归档','No posts found in Trash'=>'在回收站中未找到文章','At the top of the post type list screen when there are no posts in the trash.'=>'当回收站中没有文章时，位于文章类型列表屏幕的顶部。','No Items Found in Trash'=>'在回收站中未找到项目','No %s found in Trash'=>'在回收站中未找到 %s','No posts found'=>'未找到文章','At the top of the post type list screen when there are no posts to display.'=>'当没有可显示的帖子时，位于文章类型列表屏幕的顶部。','No Items Found'=>'未找到项目。','No %s found'=>'未找到 %s','Search Posts'=>'搜索文章','At the top of the items screen when searching for an item.'=>'搜索项目时位于项目屏幕的顶部。','Search Items'=>'搜索项目','Search %s'=>'搜索 %s','Parent Page:'=>'父页：','For hierarchical types in the post type list screen.'=>'对于文章类型列表屏幕中的分层类型。','Parent Item Prefix'=>'父项前缀','Parent %s:'=>'父级 %s：','New Post'=>'新文章','New Item'=>'新项目','New %s'=>'新 %s','Add New Post'=>'新增文章','At the top of the editor screen when adding a new item.'=>'添加新项目时位于编辑器屏幕的顶部。','Add New Item'=>'新增项目','Add New %s'=>'新增 %s','View Posts'=>'查看文章','Appears in the admin bar in the \'All Posts\' view, provided the post type supports archives and the home page is not an archive of that post type.'=>'显示在管理栏中的“所有文章”视图中，前提是文章类型支持存档并且主页不是该文章类型的存档。','View Items'=>'查看项目','View Post'=>'查看文章','In the admin bar to view item when editing it.'=>'在管理栏中编辑项目时可以查看项目。','View Item'=>'查看项目','View %s'=>'查看 %s','Edit Post'=>'编辑文章','At the top of the editor screen when editing an item.'=>'编辑项目时位于编辑器屏幕的顶部。','Edit Item'=>'编辑项目','Edit %s'=>'编辑 %s','All Posts'=>'所有文章','In the post type submenu in the admin dashboard.'=>'在管理仪表板的文章类型子菜单中。','All Items'=>'所有项目','All %s'=>'所有 %s','Admin menu name for the post type.'=>'文章类型的管理菜单名称。','Menu Name'=>'菜单名称','Regenerate all labels using the Singular and Plural labels'=>'使用单数和复数标签重新生成所有标签','Regenerate'=>'重新生成','Active post types are enabled and registered with WordPress.'=>'活动文章类型已在 WordPress 中启用并注册。','A descriptive summary of the post type.'=>'文章类型的描述性摘要。','Add Custom'=>'添加自定义','Enable various features in the content editor.'=>'启用内容编辑器中的各种功能。','Post Formats'=>'文章格式','Editor'=>'编辑','Trackbacks'=>'引用通告','Select existing taxonomies to classify items of the post type.'=>'选择现有分类法对文章类型的项目进行分类。','Browse Fields'=>'浏览字段','Nothing to import'=>'没有什么可导入的','. The Custom Post Type UI plugin can be deactivated.'=>'可以停用自定义文章类型 UI 插件。','Imported %d item from Custom Post Type UI -'=>'已从自定义文章类型 UI 导入 %d 项 -','Failed to import taxonomies.'=>'导入分类法失败。','Failed to import post types.'=>'导入文章类型失败。','Nothing from Custom Post Type UI plugin selected for import.'=>'自定义文章类型 UI 插件中没有选择导入。','Imported 1 item'=>'已导入 %s 个项目','Importing a Post Type or Taxonomy with the same key as one that already exists will overwrite the settings for the existing Post Type or Taxonomy with those of the import.'=>'导入与现有文章类型或分类法具有相同键的文章类型或分类法将会用导入的设置覆盖现有文章类型或分类法的设置。','Import from Custom Post Type UI'=>'从自定义文章类型 UI 导入','The following code can be used to register a local version of the selected items. Storing field groups, post types, or taxonomies locally can provide many benefits such as faster load times, version control & dynamic fields/settings. Simply copy and paste the following code to your theme\'s functions.php file or include it within an external file, then deactivate or delete the items from the ACF admin.'=>'以下代码可用于注册所选项目的本地版本。在本地存储字段组、帖子类型或分类法可以提供许多好处，例如更快的加载时间、版本控制和动态字段/设置。只需将以下代码复制并粘贴到主题的functions.php 文件中或将其包含在外部文件中，然后从ACF 管理中停用或删除这些项目。','Export - Generate PHP'=>'导出 - 生成 PHP','Export'=>'导出','Select Taxonomies'=>'选择分类法','Select Post Types'=>'选择文章类型','Exported 1 item.'=>'已导出 %s 个项目。','Category'=>'分类','Tag'=>'标签','%s taxonomy created'=>'%s 分类法已创建','%s taxonomy updated'=>'%s 分类法已更新','Taxonomy draft updated.'=>'分类法草稿已更新。','Taxonomy scheduled for.'=>'分类法已计划。','Taxonomy submitted.'=>'已提交分类法。','Taxonomy saved.'=>'分类法已保存。','Taxonomy deleted.'=>'分类法已删除。','Taxonomy updated.'=>'分类法已更新。','This taxonomy could not be registered because its key is in use by another taxonomy registered by another plugin or theme.'=>'无法注册此分类法，因为其密钥已被另一个插件或主题注册的另一个分类法使用。','Taxonomy synchronized.'=>'%s 分类法已同步。','Taxonomy duplicated.'=>'%s 分类法已复制。','Taxonomy deactivated.'=>'%s 分类法已停用。','Taxonomy activated.'=>'%s 分类法已激活。','Terms'=>'分类项','Post type synchronized.'=>'%s 个文章类型已同步。','Post type duplicated.'=>'%s 个文章类型已复制。','Post type deactivated.'=>'%s 个文章类型已停用。','Post type activated.'=>'%s 个文章类型已激活。','Post Types'=>'文章类型','Advanced Settings'=>'高级设置','Basic Settings'=>'基本设置','This post type could not be registered because its key is in use by another post type registered by another plugin or theme.'=>'无法注册此帖子类型，因为其密钥已被另一个插件或主题注册的另一个帖子类型使用。','Pages'=>'页面','Link Existing Field Groups'=>'链接现有字段组','%s post type created'=>'%s 文章类型已创建','Add fields to %s'=>'将字段添加到 %s','%s post type updated'=>'%s 文章类型已更新','Post type draft updated.'=>'文章类型草稿已更新。','Post type scheduled for.'=>'已计划文章类型。','Post type submitted.'=>'已提交文章类型。','Post type saved.'=>'文章类型已保存。','Post type updated.'=>'文章类型已更新。','Post type deleted.'=>'文章类型已删除。','Type to search...'=>'输入以搜索……','PRO Only'=>'仅限专业版','Field groups linked successfully.'=>'字段组链接成功。','Import Post Types and Taxonomies registered with Custom Post Type UI and manage them with ACF. <a href="%s">Get Started</a>.'=>'导入使用自定义帖子类型 UI 注册的帖子类型和分类法并使用 ACF 进行管理。 <a href="%s">开始使用</a>。','ACF'=>'ACF','taxonomy'=>'分类法','post type'=>'文章类型','Done'=>'完成','Field Group(s)'=>'字段组','Select one or many field groups...'=>'选择一个或多个字段组...','Please select the field groups to link.'=>'请选择要链接的字段组。','Field group linked successfully.'=>'字段组链接成功。','post statusRegistration Failed'=>'注册失败','This item could not be registered because its key is in use by another item registered by another plugin or theme.'=>'无法注册此项目，因为其密钥已被另一个插件或主题注册的另一个项目使用。','REST API'=>'REST API','Permissions'=>'权限','URLs'=>'网址','Visibility'=>'可见性','Labels'=>'标签','Field Settings Tabs'=>'字段设置选项卡','https://wpengine.com/?utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields'=>'https://wpengine.com/?utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields','[ACF shortcode value disabled for preview]'=>'[预览时禁用 ACF 短代码值]','Close Modal'=>'关闭模态框','Field moved to other group'=>'字段移至其他组','Close modal'=>'关闭模态框','Start a new group of tabs at this tab.'=>'在此选项卡上启动一组新选项卡。','New Tab Group'=>'新标签组','Use a stylized checkbox using select2'=>'使用 select2 使用程式化复选框','Save Other Choice'=>'保存其他选择','Allow Other Choice'=>'允许其他选择','Add Toggle All'=>'添加 全部切换','Save Custom Values'=>'保存自定义值','Allow Custom Values'=>'允许自定义值','Checkbox custom values cannot be empty. Uncheck any empty values.'=>'复选框自定义值不能为空。取消选中任何空值。','Updates'=>'更新','Advanced Custom Fields logo'=>'高级自定义字段 LOGO','Save Changes'=>'保存更改','Field Group Title'=>'字段组标题','Add title'=>'添加标题','New to ACF? Take a look at our <a href="%s" target="_blank">getting started guide</a>.'=>'ACF 新手？请查看我们的<a href="%s" target="_blank">入门指南</a>。','Add Field Group'=>'添加字段组','ACF uses <a href="%s" target="_blank">field groups</a> to group custom fields together, and then attach those fields to edit screens.'=>'ACF 使用<a href="%s" target="_blank">字段组</a>将自定义字段分组在一起，然后将这些字段附加到编辑屏幕。','Add Your First Field Group'=>'添加您的第一个字段组','Options Pages'=>'选项页','ACF Blocks'=>'ACF 块','Gallery Field'=>'画廊字段','Flexible Content Field'=>'弹性内容字段','Repeater Field'=>'循环字段','Unlock Extra Features with ACF PRO'=>'使用 ACF PRO 解锁额外功能','Delete Field Group'=>'删除字段组','Created on %1$s at %2$s'=>'于 %1$s 在 %2$s 创建','Group Settings'=>'群组设置','Location Rules'=>'定位规则','Choose from over 30 field types. <a href="%s" target="_blank">Learn more</a>.'=>'从30多种字段类型中进行选择<a href="%s"target="_blank">了解更多信息</a>。','Get started creating new custom fields for your posts, pages, custom post types and other WordPress content.'=>'开始为您的文章、页面、自定义帖子类型和其他WordPress内容创建新的自定义字段。','Add Your First Field'=>'添加您的第一个字段','#'=>'#','Add Field'=>'添加字段','Presentation'=>'展示','Validation'=>'验证','General'=>'常规','Import JSON'=>'导入 JSON','Export As JSON'=>'导出为 JSON','Field group deactivated.'=>'%s 字段组已停用。','Field group activated.'=>'%s 个字段组已激活。','Deactivate'=>'停用','Deactivate this item'=>'停用此项目','Activate'=>'激活','Activate this item'=>'激活此项目','Move field group to trash?'=>'将字段组移至回收站？','post statusInactive'=>'停用','WP Engine'=>'WP Engine','Advanced Custom Fields and Advanced Custom Fields PRO should not be active at the same time. We\'ve automatically deactivated Advanced Custom Fields PRO.'=>'高级自定义字段和高级自定义字段 PRO 不应同时处于活动状态。我们已自动停用高级自定义字段 PRO。','Advanced Custom Fields and Advanced Custom Fields PRO should not be active at the same time. We\'ve automatically deactivated Advanced Custom Fields.'=>'高级自定义字段和高级自定义字段 PRO 不应同时处于活动状态。我们已自动停用高级自定义字段。','<strong>%1$s</strong> - We\'ve detected one or more calls to retrieve ACF field values before ACF has been initialized. This is not supported and can result in malformed or missing data. <a href="%2$s" target="_blank">Learn how to fix this</a>.'=>'<strong>%1$s</strong> - 我们检测到在 ACF 初始化之前检索 ACF 字段值的一次或多次调用。不支持此操作，并且可能会导致数据格式错误或丢失。 <a href="%2$s" target="_blank">了解如何解决此问题</a>。','%1$s must have a user with the %2$s role.'=>'%1$s 必须拥有具有以下角色之一的用户：%2$s','%1$s must have a valid user ID.'=>'%1$s 必须具有有效的用户 ID。','Invalid request.'=>'无效的请求。','%1$s is not one of %2$s'=>'%1$s 不是 %2$s 之一。','%1$s must have term %2$s.'=>'%1$s 必须具有以下分类项之一：%2$s','%1$s must be of post type %2$s.'=>'%1$s 必须属于以下文章类型之一：%2$s','%1$s must have a valid post ID.'=>'%1$s 必须具有有效的文章 ID。','%s requires a valid attachment ID.'=>'%s 需要有效的附件 ID。','Show in REST API'=>'在 REST API 中显示','Enable Transparency'=>'启用透明度','RGBA Array'=>'RGBA 数组','RGBA String'=>'RGBA 字符串','Hex String'=>'十六进制字符串','Upgrade to PRO'=>'升级到专业版','post statusActive'=>'启用','\'%s\' is not a valid email address'=>'“%s”不是有效的电子邮件地址','Color value'=>'颜色值','Select default color'=>'选择默认颜色','Clear color'=>'清除颜色','Blocks'=>'区块','Options'=>'选项','Users'=>'用户','Menu items'=>'菜单项','Widgets'=>'小工具','Attachments'=>'附件','Taxonomies'=>'分类法','Posts'=>'文章','Last updated: %s'=>'最后更新：%s','Sorry, this post is unavailable for diff comparison.'=>'抱歉，这篇文章无法进行差异比较。','Invalid field group parameter(s).'=>'无效的字段组参数。','Awaiting save'=>'等待保存','Saved'=>'已保存','Import'=>'导入','Review changes'=>'查看变更','Located in: %s'=>'位于：%s','Located in plugin: %s'=>'位于插件中：%s','Located in theme: %s'=>'位于主题中：%s','Various'=>'各种各样的','Sync changes'=>'同步更改','Loading diff'=>'加载差异','Review local JSON changes'=>'查看本地JSON更改','Visit website'=>'访问网站','View details'=>'查看详情','Version %s'=>'版本 %s','Information'=>'信息','<a href="%s" target="_blank">Help Desk</a>. The support professionals on our Help Desk will assist with your more in depth, technical challenges.'=>'<a href="%s" target="_blank">服务台</a>。我们服务台上的支持专业人员将协助您解决更深入的技术难题。','<a href="%s" target="_blank">Discussions</a>. We have an active and friendly community on our Community Forums who may be able to help you figure out the \'how-tos\' of the ACF world.'=>'<a href="%s" target="_blank">讨论</a>。我们的社区论坛上有一个活跃且友好的社区，他们也许能够帮助您了解 ACF 世界的“操作方法”。','<a href="%s" target="_blank">Documentation</a>. Our extensive documentation contains references and guides for most situations you may encounter.'=>'<a href="%s" target="_blank">文档</a>。我们详尽的文档包含您可能遇到的大多数情况的参考和指南。','We are fanatical about support, and want you to get the best out of your website with ACF. If you run into any difficulties, there are several places you can find help:'=>'我们热衷于支持，并希望您通过ACF充分利用自己的网站。如果遇到任何困难，可以在几个地方找到帮助：','Help & Support'=>'帮助和支持','Please use the Help & Support tab to get in touch should you find yourself requiring assistance.'=>'如果您需要帮助，请使用“帮助和支持”选项卡进行联系。','Before creating your first Field Group, we recommend first reading our <a href="%s" target="_blank">Getting started</a> guide to familiarize yourself with the plugin\'s philosophy and best practises.'=>'在创建您的第一个字段组之前，我们建议您先阅读<a href="%s" target="_blank">入门</a>指南，以熟悉插件的原理和最佳实践。','The Advanced Custom Fields plugin provides a visual form builder to customize WordPress edit screens with extra fields, and an intuitive API to display custom field values in any theme template file.'=>'Advanced Custom Fields 插件提供了一个可视化的表单生成器，用于自定义带有额外字段的WordPress编辑屏幕，以及一个直观的API，用于在任何主题模板文件中显示自定义字段的值。','Overview'=>'概述','Location type "%s" is already registered.'=>'位置类型“%s”已被注册。','Class "%s" does not exist.'=>'Class“%s”不存在。','Invalid nonce.'=>'无效的随机数。','Error loading field.'=>'加载字段时出错。','Location not found: %s'=>'找不到位置：%s','<strong>Error</strong>: %s'=>'<strong>错误</strong>：%s','Widget'=>'小工具','User Role'=>'用户角色','Comment'=>'评论','Post Format'=>'文章格式','Menu Item'=>'菜单项','Post Status'=>'文章状态','Menus'=>'菜单','Menu Locations'=>'菜单位置','Menu'=>'菜单','Post Taxonomy'=>'文章分类法','Child Page (has parent)'=>'子页面（有父页面）','Parent Page (has children)'=>'父页面（有子页）','Top Level Page (no parent)'=>'顶级页面 (无父页面)','Posts Page'=>'文章页','Front Page'=>'首页','Page Type'=>'页面类型','Viewing back end'=>'查看后端','Viewing front end'=>'查看前端','Logged in'=>'登录','Current User'=>'当前用户','Page Template'=>'页面模板','Register'=>'注册','Add / Edit'=>'添加 / 编辑','User Form'=>'用户表单','Page Parent'=>'父级页面','Super Admin'=>'超级管理员','Current User Role'=>'当前用户角色','Default Template'=>'默认模板','Post Template'=>'文章模板','Post Category'=>'文章类别','All %s formats'=>'所有 %s 格式','Attachment'=>'附件','%s value is required'=>'%s 的值是必填项','Show this field if'=>'显示此字段的条件','Conditional Logic'=>'条件逻辑','and'=>'与','Local JSON'=>'本地 JSON','Clone Field'=>'克隆字段','Please also check all premium add-ons (%s) are updated to the latest version.'=>'还请检查所有高级扩展（%s）是否已更新到最新版本。','This version contains improvements to your database and requires an upgrade.'=>'此版本包含对数据库的改进，需要升级。','Thank you for updating to %1$s v%2$s!'=>'感谢您更新到 %1$s v%2$s!','Database Upgrade Required'=>'需要升级数据库','Options Page'=>'选项页面','Gallery'=>'画廊','Flexible Content'=>'弹性内容','Repeater'=>'循环','Back to all tools'=>'返回所有工具','If multiple field groups appear on an edit screen, the first field group\'s options will be used (the one with the lowest order number)'=>'如果多个字段组同时出现在编辑界面，会使用第一个字段组里的选项（就是序号最小的那个字段组）','<b>Select</b> items to <b>hide</b> them from the edit screen.'=>'<b>选择</b>需要在编辑界面<b>隐藏</b>的条目。','Hide on screen'=>'隐藏元素','Send Trackbacks'=>'发送 Trackbacks','Tags'=>'标签','Categories'=>'类别','Page Attributes'=>'页面属性','Format'=>'格式','Author'=>'作者','Slug'=>'别名','Revisions'=>'修订','Comments'=>'评论','Discussion'=>'讨论','Excerpt'=>'摘要','Content Editor'=>'内容编辑器','Permalink'=>'固定链接','Shown in field group list'=>'在字段组列表中显示','Field groups with a lower order will appear first'=>'序号小的字段组会排在最前面','Order No.'=>'序号','Below fields'=>'字段之下','Below labels'=>'标签之下','Instruction Placement'=>'说明位置','Label Placement'=>'标签位置','Side'=>'边栏','Normal (after content)'=>'正常（内容之后）','High (after title)'=>'高（标题之后）','Position'=>'位置','Seamless (no metabox)'=>'无缝（无 metabox）','Standard (WP metabox)'=>'标准（WP Metabox）','Style'=>'样式','Type'=>'类型','Key'=>'密钥','Order'=>'序号','Close Field'=>'关闭字段','id'=>'id','class'=>'class','width'=>'宽度','Wrapper Attributes'=>'包装属性','Required'=>'必填','Instructions'=>'说明','Field Type'=>'字段类型','Single word, no spaces. Underscores and dashes allowed'=>'单个字符串，不能有空格，允许下划线和破折号。','Field Name'=>'字段名称','This is the name which will appear on the EDIT page'=>'在编辑界面显示的名字','Field Label'=>'字段标签','Delete'=>'删除','Delete field'=>'删除字段','Move'=>'移动','Move field to another group'=>'把字段移动到其它群组','Duplicate field'=>'复制字段','Edit field'=>'编辑字段','Drag to reorder'=>'拖拽排序','Show this field group if'=>'显示此字段组的条件','No updates available.'=>'没有可用更新。','Database upgrade complete. <a href="%s">See what\'s new</a>'=>'数据库升级完成。<a href="%s">查看新的部分 </a>。','Reading upgrade tasks...'=>'阅读更新任务...','Upgrade failed.'=>'升级失败。','Upgrade complete.'=>'升级完成。','Upgrading data to version %s'=>'升级数据到 %s 版本','It is strongly recommended that you backup your database before proceeding. Are you sure you wish to run the updater now?'=>'升级前最好先备份一下。确定现在升级吗？','Please select at least one site to upgrade.'=>'请选择至少一个要升级的站点。','Database Upgrade complete. <a href="%s">Return to network dashboard</a>'=>'数据库升级完成，<a href="%s">返回网络面板</a>','Site is up to date'=>'网站已是最新版','Site requires database upgrade from %1$s to %2$s'=>'站点需要将数据库从 %1$s 升级到 %2$s','Site'=>'网站','Upgrade Sites'=>'升级站点','The following sites require a DB upgrade. Check the ones you want to update and then click %s.'=>'下面的网站需要升级数据库，点击 %s 。','Add rule group'=>'添加规则组','Create a set of rules to determine which edit screens will use these advanced custom fields'=>'创建一组规则以确定自定义字段在哪个编辑界面上显示','Rules'=>'规则','Copied'=>'复制','Copy to clipboard'=>'复制到剪贴板','Select the items you would like to export and then select your export method. Export As JSON to export to a .json file which you can then import to another ACF installation. Generate PHP to export to PHP code which you can place in your theme.'=>'选择您要导出的项目，然后选择导出方法。导出为 JSON 以导出到 .json 文件，然后可以将其导入到另一个 ACF 安装中。生成 PHP 以导出到 PHP 代码，您可以将其放置在主题中。','Select Field Groups'=>'选择字段组','No field groups selected'=>'没选择字段组','Generate PHP'=>'生成 PHP','Export Field Groups'=>'导出字段组','Import file empty'=>'导入的文件是空白的','Incorrect file type'=>'文本类型不对','Error uploading file. Please try again'=>'文件上传失败，请重试','Select the Advanced Custom Fields JSON file you would like to import. When you click the import button below, ACF will import the items in that file.'=>'选择您要导入的高级自定义字段 JSON 文件。当您单击下面的导入按钮时，ACF 将导入该文件中的项目。','Import Field Groups'=>'导入字段组','Sync'=>'同步','Select %s'=>'选择 %s','Duplicate'=>'复制','Duplicate this item'=>'复制此项','Supports'=>'支持','Documentation'=>'文档','Description'=>'描述','Sync available'=>'有可用同步','Field group synchronized.'=>'%s 个字段组已同步。','Field group duplicated.'=>'%s已复制字段组。','Active <span class="count">(%s)</span>'=>'启用 <span class="count">(%s)</span>','Review sites & upgrade'=>'检查网站并升级','Upgrade Database'=>'升级数据库','Custom Fields'=>'字段','Move Field'=>'移动字段','Please select the destination for this field'=>'请选择这个字段的位置','The %1$s field can now be found in the %2$s field group'=>'现在可以在 %2$s 字段组中找到 %1$s 字段','Move Complete.'=>'移动完成。','Active'=>'激活','Field Keys'=>'字段 Keys','Settings'=>'设置','Location'=>'位置','Null'=>'Null','copy'=>'复制','(this field)'=>'(这个字段)','Checked'=>'已选中','Move Custom Field'=>'移动自定义字段','No toggle fields available'=>'没有可用的切换字段','Field group title is required'=>'字段组的标题是必填项','This field cannot be moved until its changes have been saved'=>'保存这个字段的修改以后才能移动这个字段','The string "field_" may not be used at the start of a field name'=>'"field_" 这个字符串不能作为字段名字的开始部分','Field group draft updated.'=>'字段组草稿已更新。','Field group scheduled for.'=>'字段组已定时。','Field group submitted.'=>'字段组已提交。','Field group saved.'=>'字段组已保存。','Field group published.'=>'字段组已发布。','Field group deleted.'=>'字段组已删除。','Field group updated.'=>'字段组已更新。','Tools'=>'工具','is not equal to'=>'不等于','is equal to'=>'等于','Forms'=>'表单','Page'=>'页面','Post'=>'文章','Relational'=>'关系','Choice'=>'选项','Basic'=>'基本','Unknown'=>'未知','Field type does not exist'=>'字段类型不存在','Spam Detected'=>'检测到垃圾邮件','Post updated'=>'文章已更新','Update'=>'更新','Validate Email'=>'验证邮箱','Content'=>'内容','Title'=>'标题','Edit field group'=>'编辑字段组','Selection is less than'=>'选择小于','Selection is greater than'=>'选择大于','Value is less than'=>'值小于','Value is greater than'=>'值大于','Value contains'=>'值包含','Value matches pattern'=>'值匹配模式','Value is not equal to'=>'值不等于','Value is equal to'=>'值等于','Has no value'=>'没有价值','Has any value'=>'有任何价值','Cancel'=>'退出','Are you sure?'=>'确定吗?','%d fields require attention'=>'%d 个字段需要注意','1 field requires attention'=>'1 个字段需要注意','Validation failed'=>'验证失败','Validation successful'=>'验证成功','Restricted'=>'限制','Collapse Details'=>'折叠','Expand Details'=>'展开','Uploaded to this post'=>'上传到这个文章','verbUpdate'=>'更新','verbEdit'=>'编辑','The changes you made will be lost if you navigate away from this page'=>'如果浏览其它页面，会丢失当前所做的修改','File type must be %s.'=>'字段类型必须是 %s。','or'=>'或','File size must not exceed %s.'=>'文件尺寸最大不能超过 %s。','File size must be at least %s.'=>'文件尺寸至少得是 %s。','Image height must not exceed %dpx.'=>'图像高度最大不能超过 %dpx。','Image height must be at least %dpx.'=>'图像高度至少得是 %dpx。','Image width must not exceed %dpx.'=>'图像宽度最大不能超过 %dpx。','Image width must be at least %dpx.'=>'图像宽度至少得是 %dpx。','(no title)'=>'(无标题)','Full Size'=>'原图','Large'=>'大','Medium'=>'中','Thumbnail'=>'缩略图','(no label)'=>'(无标签)','Sets the textarea height'=>'设置文本区域的高度','Rows'=>'行','Text Area'=>'文本区域','Prepend an extra checkbox to toggle all choices'=>'添加一个可以全选的复选框','Save \'custom\' values to the field\'s choices'=>'将 "自定义" 值保存到字段的选择中','Allow \'custom\' values to be added'=>'允许添加 "自定义" 值','Add new choice'=>'添加新选项','Toggle All'=>'全选','Allow Archives URLs'=>'允许存档 url','Archives'=>'存档','Page Link'=>'页面链接','Add'=>'添加','Name'=>'名称','%s added'=>'%s 已添加','%s already exists'=>'%s 已存在','User unable to add new %s'=>'用户无法添加新的 %s','Term ID'=>'内容ID','Term Object'=>'对象缓存','Load value from posts terms'=>'从文章项目中加载值','Load Terms'=>'加载项目','Connect selected terms to the post'=>'连接所选项目到文章','Save Terms'=>'保存项目','Allow new terms to be created whilst editing'=>'在编辑时允许可以创建新的项目','Create Terms'=>'创建项目','Radio Buttons'=>'单选框','Single Value'=>'单个值','Multi Select'=>'多选','Checkbox'=>'复选框','Multiple Values'=>'多选','Select the appearance of this field'=>'为这个字段选择外观','Appearance'=>'外观','Select the taxonomy to be displayed'=>'选择要显示的分类法','No TermsNo %s'=>'无 %s','Value must be equal to or lower than %d'=>'值要小于等于 %d','Value must be equal to or higher than %d'=>'值要大于等于 %d','Value must be a number'=>'值必须是数字','Number'=>'数字','Save \'other\' values to the field\'s choices'=>'存档为字段的选择的 \'other\' 的值','Add \'other\' choice to allow for custom values'=>'为自定义值添加 \'other\' 选择','Other'=>'其他','Radio Button'=>'单选按钮','Define an endpoint for the previous accordion to stop. This accordion will not be visible.'=>'定义上一个手风琴停止的端点。此手风琴将不可见。','Allow this accordion to open without closing others.'=>'允许此手风琴打开而不关闭其他。','Multi-Expand'=>'多扩展','Display this accordion as open on page load.'=>'将此手风琴显示为在页面加载时打开。','Open'=>'打开','Accordion'=>'手风琴','Restrict which files can be uploaded'=>'限制什么类型的文件可以上传','File ID'=>'文件ID','File URL'=>'文件URL','File Array'=>'文件数组','Add File'=>'添加文件','No file selected'=>'没选择文件','File name'=>'文件名','Update File'=>'更新文件','Edit File'=>'编辑文件','Select File'=>'选择文件','File'=>'文件','Password'=>'密码','Specify the value returned'=>'指定返回的值','Use AJAX to lazy load choices?'=>'使用 AJAX 惰性选择？','Enter each default value on a new line'=>'每行输入一个默认值','verbSelect'=>'选择','Select2 JS load_failLoading failed'=>'加载失败','Select2 JS searchingSearching&hellip;'=>'搜索中&hellip;','Select2 JS load_moreLoading more results&hellip;'=>'载入更多结果&hellip;','Select2 JS selection_too_long_nYou can only select %d items'=>'只能选择 %d 项','Select2 JS selection_too_long_1You can only select 1 item'=>'您只能选择1项','Select2 JS input_too_long_nPlease delete %d characters'=>'请删除 %d 个字符','Select2 JS input_too_long_1Please delete 1 character'=>'请删除1个字符','Select2 JS input_too_short_nPlease enter %d or more characters'=>'请输入 %d 或者更多字符','Select2 JS input_too_short_1Please enter 1 or more characters'=>'请输入至少一个字符','Select2 JS matches_0No matches found'=>'找不到匹配项','Select2 JS matches_n%d results are available, use up and down arrow keys to navigate.'=>'%d 结果可用, 请使用向上和向下箭头键进行导航。','Select2 JS matches_1One result is available, press enter to select it.'=>'一个结果是可用的，按回车选择它。','nounSelect'=>'下拉选择','User ID'=>'用户 ID','User Object'=>'用户对象','User Array'=>'數組','All user roles'=>'所有用户角色','Filter by Role'=>'按角色过滤','User'=>'用户','Separator'=>'分隔线','Select Color'=>'选择颜色','Default'=>'默认','Clear'=>'清除','Color Picker'=>'颜色选择','Date Time Picker JS pmTextShortP'=>'P','Date Time Picker JS pmTextPM'=>'下午','Date Time Picker JS amTextShortA'=>'A','Date Time Picker JS amTextAM'=>'上午','Date Time Picker JS selectTextSelect'=>'选择','Date Time Picker JS closeTextDone'=>'已完成','Date Time Picker JS currentTextNow'=>'现在','Date Time Picker JS timezoneTextTime Zone'=>'时区','Date Time Picker JS microsecTextMicrosecond'=>'微秒','Date Time Picker JS millisecTextMillisecond'=>'毫秒','Date Time Picker JS secondTextSecond'=>'秒','Date Time Picker JS minuteTextMinute'=>'分钟','Date Time Picker JS hourTextHour'=>'小时','Date Time Picker JS timeTextTime'=>'时间','Date Time Picker JS timeOnlyTitleChoose Time'=>'选择时间','Date Time Picker'=>'日期时间选择器','Endpoint'=>'端点','Left aligned'=>'左对齐','Top aligned'=>'顶部对齐','Placement'=>'位置','Tab'=>'选项卡','Value must be a valid URL'=>'值必须是有效的地址','Link URL'=>'链接 URL','Link Array'=>'链接数组','Opens in a new window/tab'=>'在新窗口/选项卡中打开','Select Link'=>'选择链接','Link'=>'链接','Email'=>'电子邮件','Step Size'=>'步长','Maximum Value'=>'最大值','Minimum Value'=>'最小值','Range'=>'范围(滑块)','Both (Array)'=>'两个 (阵列)','Label'=>'标签','Value'=>'值','Vertical'=>'垂直','Horizontal'=>'水平','red : Red'=>'red : Red','For more control, you may specify both a value and label like this:'=>'如果需要更多控制，您按照一下格式，定义一个值和标签对：','Enter each choice on a new line.'=>'输入选项，每行一个。','Choices'=>'选项','Button Group'=>'按钮组','Allow Null'=>'允许空值','Parent'=>'父级','TinyMCE will not be initialized until field is clicked'=>'TinyMCE 在栏位没有点击之前不会初始化','Delay Initialization'=>'延迟初始化','Show Media Upload Buttons'=>'显示媒体上传按钮','Toolbar'=>'工具条','Text Only'=>'纯文本','Visual Only'=>'只有显示','Visual & Text'=>'显示与文本','Tabs'=>'标签','Click to initialize TinyMCE'=>'点击初始化 TinyMCE 编辑器','Name for the Text editor tab (formerly HTML)Text'=>'文本','Visual'=>'显示','Value must not exceed %d characters'=>'值不得超过%d个字符','Leave blank for no limit'=>'留空则不限制','Character Limit'=>'字符限制','Appears after the input'=>'在 input 后面显示','Append'=>'追加','Appears before the input'=>'在 input 前面显示','Prepend'=>'前置','Appears within the input'=>'在 input 内部显示','Placeholder Text'=>'占位符文本','Appears when creating a new post'=>'创建新文章的时候显示','Text'=>'文本','%1$s requires at least %2$s selection'=>'%1$s 至少需要 %2$s 个选择','Post ID'=>'文章 ID','Post Object'=>'文章对象','Maximum Posts'=>'最大文章数','Minimum Posts'=>'最小文章数','Featured Image'=>'特色图像','Selected elements will be displayed in each result'=>'选择的元素将在每个结果中显示','Elements'=>'元素','Taxonomy'=>'分类法','Post Type'=>'文章类型','Filters'=>'过滤器','All taxonomies'=>'所有分类法','Filter by Taxonomy'=>'按分类筛选','All post types'=>'所有文章类型','Filter by Post Type'=>'按文章类型筛选','Search...'=>'搜索...','Select taxonomy'=>'选择分类','Select post type'=>'选择文章类型','No matches found'=>'找不到匹配项','Loading'=>'加载','Maximum values reached ( {max} values )'=>'达到了最大值 ( {max} 值 )','Relationship'=>'关系','Comma separated list. Leave blank for all types'=>'用英文逗号分隔开，留空则为全部类型','Allowed File Types'=>'允许的文件类型','Maximum'=>'最大','File size'=>'文件尺寸','Restrict which images can be uploaded'=>'限制可以上传的图像','Minimum'=>'最小','Uploaded to post'=>'上传到文章','All'=>'所有','Limit the media library choice'=>'限制媒体库的选择','Library'=>'库','Preview Size'=>'预览图大小','Image ID'=>'图像ID','Image URL'=>'图像 URL','Image Array'=>'图像数组','Specify the returned value on front end'=>'指定前端返回的值','Return Value'=>'返回值','Add Image'=>'添加图片','No image selected'=>'没有选择图片','Remove'=>'删除','Edit'=>'编辑','All images'=>'所有图片','Update Image'=>'更新图像','Edit Image'=>'编辑图片','Select Image'=>'选择图像','Image'=>'图像','Allow HTML markup to display as visible text instead of rendering'=>'显示 HTML 文本，而不是渲染 HTML','Escape HTML'=>'转义 HTML','No Formatting'=>'无格式','Automatically add &lt;br&gt;'=>'自动添加 &lt;br&gt;','Automatically add paragraphs'=>'自动添加段落','Controls how new lines are rendered'=>'控制怎么显示新行','New Lines'=>'新行','Week Starts On'=>'每周开始于','The format used when saving a value'=>'保存值时使用的格式','Save Format'=>'保存格式','Date Picker JS weekHeaderWk'=>'周','Date Picker JS prevTextPrev'=>'上一页','Date Picker JS nextTextNext'=>'下一个','Date Picker JS currentTextToday'=>'今日','Date Picker JS closeTextDone'=>'完成','Date Picker'=>'日期选择','Width'=>'宽度','Embed Size'=>'嵌入尺寸','Enter URL'=>'输入 URL','oEmbed'=>'oEmbed(嵌入)','Text shown when inactive'=>'非激活时显示的文字','Off Text'=>'关闭文本','Text shown when active'=>'激活时显示的文本','On Text'=>'打开文本','Stylized UI'=>'风格化的用户界面','Default Value'=>'默认值','Displays text alongside the checkbox'=>'在复选框旁边显示文本','Message'=>'消息','No'=>'否','Yes'=>'是','True / False'=>'真 / 假 (开关)','Row'=>'行','Table'=>'表','Block'=>'区块','Specify the style used to render the selected fields'=>'指定用于呈现所选字段的样式','Layout'=>'样式','Sub Fields'=>'子字段','Group'=>'分组','Customize the map height'=>'自定义地图高度','Height'=>'高度','Set the initial zoom level'=>'设置初始缩放级别','Zoom'=>'缩放','Center the initial map'=>'居中显示初始地图','Center'=>'居中','Search for address...'=>'搜索地址...','Find current location'=>'搜索当前位置','Clear location'=>'清除位置','Search'=>'搜索','Sorry, this browser does not support geolocation'=>'抱歉，浏览器不支持定位','Google Map'=>'谷歌地图','The format returned via template functions'=>'通过模板函数返回的格式','Return Format'=>'返回格式','Custom:'=>'自定义：','The format displayed when editing a post'=>'编辑文章的时候显示的格式','Display Format'=>'显示格式','Time Picker'=>'时间选择','Inactive <span class="count">(%s)</span>'=>'已停用 <span class="count">(%s)</span>','No Fields found in Trash'=>'回收站里没有字段','No Fields found'=>'没找到字段','Search Fields'=>'搜索字段','View Field'=>'视图字段','New Field'=>'新字段','Edit Field'=>'编辑字段','Add New Field'=>'添加新字段','Field'=>'字段','Fields'=>'字段','No Field Groups found in Trash'=>'回收站中没有找到字段组','No Field Groups found'=>'没有找到字段组','Search Field Groups'=>'搜索字段组','View Field Group'=>'查看字段组','New Field Group'=>'新建字段组','Edit Field Group'=>'编辑字段组','Add New Field Group'=>'添加字段组','Add New'=>'新建','Field Group'=>'字段组','Field Groups'=>'字段组','Customize WordPress with powerful, professional and intuitive fields.'=>'【高级自定义字段 ACF】使用强大、专业和直观的字段自定义WordPress。','https://www.advancedcustomfields.com'=>'https://www.advancedcustomfields.com','Advanced Custom Fields'=>'Advanced Custom Fields','Advanced Custom Fields PRO'=>'Advanced Custom Fields 专业版','Options Updated'=>'选项已更新','Check Again'=>'重新检查','Publish'=>'发布','No Custom Field Groups found for this options page. <a href="%s">Create a Custom Field Group</a>'=>'这个选项页上还没有自定义字段群组。<a href="%s">创建自定义字段群组</a>','<b>Error</b>. Could not connect to update server'=>'<b>错误</b>，不能连接到更新服务器','Display'=>'显示','Add Row'=>'添加行','layout'=>'布局','layouts'=>'布局','This field requires at least {min} {label} {identifier}'=>'这个字段需要至少 {min} {label} {identifier}','{available} {label} {identifier} available (max {max})'=>'{available} {label} {identifier} 可用 (max {max})','{required} {label} {identifier} required (min {min})'=>'{required} {label} {identifier} 需要 (min {min})','Flexible Content requires at least 1 layout'=>'灵活内容字段需要至少一个布局','Click the "%s" button below to start creating your layout'=>'点击下面的 "%s" 按钮创建布局','Add layout'=>'添加布局','Remove layout'=>'删除布局','Delete Layout'=>'删除布局','Duplicate Layout'=>'复制布局','Add New Layout'=>'添加新布局','Add Layout'=>'添加布局','Min'=>'最小','Max'=>'最大','Minimum Layouts'=>'最小布局','Maximum Layouts'=>'最大布局','Button Label'=>'按钮标签','Add Image to Gallery'=>'添加图片到相册','Maximum selection reached'=>'已到最大选择','Length'=>'长度','Caption'=>'标题','Add to gallery'=>'添加到相册','Bulk actions'=>'批量动作','Sort by date uploaded'=>'按上传日期排序','Sort by date modified'=>'按修改日期排序','Sort by title'=>'按标题排序','Reverse current order'=>'颠倒当前排序','Close'=>'关闭','Minimum Selection'=>'最小选择','Maximum Selection'=>'最大选择','Allowed file types'=>'允许的文字类型','Minimum rows not reached ({min} rows)'=>'已到最小行数 ({min} 行)','Maximum rows reached ({max} rows)'=>'已到最大行数 ({max} 行)','Minimum Rows'=>'最小行数','Maximum Rows'=>'最大行数','Click to reorder'=>'拖拽排序','Add row'=>'添加行','Remove row'=>'删除行','First Page'=>'首页','Previous Page'=>'文章页','Next Page'=>'首页','Last Page'=>'文章页','No options pages exist'=>'还没有选项页面','Deactivate License'=>'关闭许可证','Activate License'=>'激活许可证','License Key'=>'许可证号','Update Information'=>'更新信息','Current Version'=>'当前版本','Latest Version'=>'最新版本','Update Available'=>'可用更新','Upgrade Notice'=>'更新通知','Enter your license key to unlock updates'=>'在上面输入许可证号解锁更新','Update Plugin'=>'更新插件']];