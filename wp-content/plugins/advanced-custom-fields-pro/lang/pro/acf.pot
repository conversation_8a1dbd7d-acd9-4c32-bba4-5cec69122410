# Copyright (C) 2024 Advanced Custom Fields PRO
# This file is distributed under the same license as the Advanced Custom Fields PRO package.
msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields PRO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language-Team: WP Engine <<EMAIL>>\n"
"POT-Creation-Date: 2024-08-01 09:50+0000\n"
"Report-Msgid-Bugs-To: https://support.advancedcustomfields.com\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: __;_e;_ex:1,2c;_n:1,2;_n_noop:1,2;_nx:1,2,4c;_nx_noop:1,2,3c;_x:1,2c;esc_attr__;esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"
"X-Poedit-SourceCharset: UTF-8\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: pro/acf-pro.php:21
msgid "Advanced Custom Fields PRO"
msgstr ""

#: pro/acf-pro.php:175
msgid "Your ACF PRO license is no longer active. Please renew to continue to have access to updates, support, & PRO features."
msgstr ""

#: pro/acf-pro.php:172
msgid "Your license has expired. Please renew to continue to have access to updates, support &amp; PRO features."
msgstr ""

#: pro/acf-pro.php:169
msgid "Activate your license to enable access to updates, support &amp; PRO features."
msgstr ""

#: pro/acf-pro.php:190, pro/admin/views/html-settings-updates.php:114
msgid "Manage License"
msgstr ""

#: pro/acf-pro.php:258
msgid "A valid license is required to edit options pages."
msgstr ""

#: pro/acf-pro.php:256
msgid "A valid license is required to edit field groups assigned to a block."
msgstr ""

#: pro/blocks.php:186
msgid "Block type name is required."
msgstr ""

#. translators: The name of the block type
#: pro/blocks.php:194
msgid "Block type \"%s\" is already registered."
msgstr ""

#: pro/blocks.php:788
msgid "Switch to Edit"
msgstr ""

#: pro/blocks.php:789
msgid "Switch to Preview"
msgstr ""

#: pro/blocks.php:790
msgid "Change content alignment"
msgstr ""

#: pro/blocks.php:791
msgid "An error occurred when loading the preview for this block."
msgstr ""

#: pro/blocks.php:792
msgid "An error occurred when loading the block in edit mode."
msgstr ""

#. translators: %s: Block type title
#: pro/blocks.php:795
msgid "%s settings"
msgstr ""

#: pro/blocks.php:1037
msgid "This block contains no editable fields."
msgstr ""

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:1043
msgid "Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to this block."
msgstr ""

#: pro/options-page.php:43, pro/admin/views/acf-ui-options-page/advanced-settings.php:237
msgid "Options"
msgstr ""

#: pro/options-page.php:73, pro/fields/class-acf-field-gallery.php:482, pro/post-types/acf-ui-options-page.php:173, pro/admin/views/acf-ui-options-page/advanced-settings.php:165
msgid "Update"
msgstr ""

#: pro/options-page.php:74, pro/post-types/acf-ui-options-page.php:174
msgid "Options Updated"
msgstr ""

#. translators: %1 A link to the updates page. %2 link to the pricing page
#: pro/updates.php:75
msgid "To enable updates, please enter your license key on the <a href=\"%1$s\">Updates</a> page. If you don't have a license key, please see <a href=\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""

#: pro/updates.php:71
msgid "To enable updates, please enter your license key on the <a href=\"%1$s\">Updates</a> page of the main site. If you don't have a license key, please see <a href=\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""

#: pro/updates.php:136
msgid "Your defined license key has changed, but an error occurred when deactivating your old license"
msgstr ""

#: pro/updates.php:133
msgid "Your defined license key has changed, but an error occurred when connecting to activation server"
msgstr ""

#: pro/updates.php:168
msgid "<strong>ACF PRO &mdash;</strong> Your license key has been activated successfully. Access to updates, support &amp; PRO features is now enabled."
msgstr ""

#: pro/updates.php:159
msgid "There was an issue activating your license key."
msgstr ""

#: pro/updates.php:155
msgid "An error occurred when connecting to activation server"
msgstr ""

#: pro/updates.php:258
msgid "The ACF activation service is temporarily unavailable. Please try again later."
msgstr ""

#: pro/updates.php:256
msgid "The ACF activation service is temporarily unavailable for scheduled maintenance. Please try again later."
msgstr ""

#: pro/updates.php:254
msgid "An upstream API error occurred when checking your ACF PRO license status. We will retry again shortly."
msgstr ""

#: pro/updates.php:224
msgid "You have reached the activation limit for the license."
msgstr ""

#: pro/updates.php:233, pro/updates.php:205
msgid "View your licenses"
msgstr ""

#: pro/updates.php:246
msgid "check again"
msgstr ""

#: pro/updates.php:250
msgid "%1$s or %2$s."
msgstr ""

#: pro/updates.php:210
msgid "Your license key has expired and cannot be activated."
msgstr ""

#: pro/updates.php:219
msgid "View your subscriptions"
msgstr ""

#: pro/updates.php:196
msgid "License key not found. Make sure you have copied your license key exactly as it appears in your receipt or your account."
msgstr ""

#: pro/updates.php:194
msgid "Your license key has been deactivated."
msgstr ""

#: pro/updates.php:192
msgid "Your license key has been activated successfully. Access to updates, support &amp; PRO features is now enabled."
msgstr ""

#. translators: %s an untranslatable internal upstream error message
#: pro/updates.php:262
msgid "An unknown error occurred while trying to communicate with the ACF activation service: %s."
msgstr ""

#: pro/updates.php:333, pro/updates.php:975
msgid "<strong>ACF PRO &mdash;</strong>"
msgstr ""

#: pro/updates.php:342
msgid "Check again"
msgstr ""

#: pro/updates.php:683
msgid "Could not connect to the activation server"
msgstr ""

#. translators: %s - URL to ACF updates page
#: pro/updates.php:753
msgid "Your license key is valid but not activated on this site. Please <a href=\"%s\">deactivate</a> and then reactivate the license."
msgstr ""

#: pro/updates.php:975
msgid "Your site URL has changed since last activating your license. We've automatically activated it for this site URL."
msgstr ""

#: pro/updates.php:967
msgid "Your site URL has changed since last activating your license, but we weren't able to automatically reactivate it: %s"
msgstr ""

#: pro/admin/admin-options-page.php:159
msgid "Publish"
msgstr ""

#: pro/admin/admin-options-page.php:162
msgid "No Custom Field Groups found for this options page. <a href=\"%s\">Create a Custom Field Group</a>"
msgstr ""

#: pro/admin/admin-options-page.php:258
msgid "Edit field group"
msgstr ""

#: pro/admin/admin-updates.php:52
msgid "<strong>Error</strong>. Could not connect to the update server"
msgstr ""

#: pro/admin/admin-updates.php:117, pro/admin/admin-updates.php:117, pro/admin/views/html-settings-updates.php:132
msgid "Updates"
msgstr ""

#. translators: %s the version of WordPress required for this ACF update
#: pro/admin/admin-updates.php:203
msgid "An update to ACF is available, but it is not compatible with your version of WordPress. Please upgrade to WordPress %s or newer to update ACF."
msgstr ""

#: pro/admin/admin-updates.php:224
msgid "<strong>Error</strong>. Could not authenticate update package. Please check again or deactivate and reactivate your ACF PRO license."
msgstr ""

#: pro/admin/admin-updates.php:214
msgid "<strong>Error</strong>. Your license for this site has expired or been deactivated. Please reactivate your ACF PRO license."
msgstr ""

#: pro/fields/class-acf-field-clone.php:22
msgctxt "noun"
msgid "Clone"
msgstr ""

#: pro/fields/class-acf-field-clone.php:24, pro/fields/class-acf-field-repeater.php:31
msgid "Allows you to select and display existing fields. It does not duplicate any fields in the database, but loads and displays the selected fields at run-time. The Clone field can either replace itself with the selected fields or display the selected fields as a group of subfields."
msgstr ""

#: pro/fields/class-acf-field-clone.php:723, pro/fields/class-acf-field-flexible-content.php:71
msgid "Fields"
msgstr ""

#: pro/fields/class-acf-field-clone.php:724
msgid "Select one or more fields you wish to clone"
msgstr ""

#: pro/fields/class-acf-field-clone.php:744
msgid "Display"
msgstr ""

#: pro/fields/class-acf-field-clone.php:745
msgid "Specify the style used to render the clone field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:750
msgid "Group (displays selected fields in a group within this field)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:751
msgid "Seamless (replaces this field with selected fields)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:760, pro/fields/class-acf-field-flexible-content.php:508, pro/fields/class-acf-field-flexible-content.php:571, pro/fields/class-acf-field-repeater.php:177
msgid "Layout"
msgstr ""

#: pro/fields/class-acf-field-clone.php:761
msgid "Specify the style used to render the selected fields"
msgstr ""

#: pro/fields/class-acf-field-clone.php:766, pro/fields/class-acf-field-flexible-content.php:584, pro/fields/class-acf-field-repeater.php:185, pro/locations/class-acf-location-block.php:22
msgid "Block"
msgstr ""

#: pro/fields/class-acf-field-clone.php:767, pro/fields/class-acf-field-flexible-content.php:583, pro/fields/class-acf-field-repeater.php:184
msgid "Table"
msgstr ""

#: pro/fields/class-acf-field-clone.php:768, pro/fields/class-acf-field-flexible-content.php:585, pro/fields/class-acf-field-repeater.php:186
msgid "Row"
msgstr ""

#: pro/fields/class-acf-field-clone.php:774
msgid "Labels will be displayed as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:779
msgid "Prefix Field Labels"
msgstr ""

#: pro/fields/class-acf-field-clone.php:789
msgid "Values will be saved as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:794
msgid "Prefix Field Names"
msgstr ""

#: pro/fields/class-acf-field-clone.php:891
msgid "Unknown field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:895
msgid "(no title)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:924
msgid "Unknown field group"
msgstr ""

#: pro/fields/class-acf-field-clone.php:928
msgid "All fields from %s field group"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:22
msgid "Flexible Content"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:24
msgid "Allows you to define, create and manage content with total control by creating layouts that contain subfields that content editors can choose from."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:24
msgid "We do not recommend using this field in ACF Blocks."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:33, pro/fields/class-acf-field-repeater.php:103, pro/fields/class-acf-field-repeater.php:297
msgid "Add Row"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:69, pro/fields/class-acf-field-flexible-content.php:866, pro/fields/class-acf-field-flexible-content.php:948
msgid "layout"
msgid_plural "layouts"
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-flexible-content.php:70
msgid "layouts"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:74, pro/fields/class-acf-field-flexible-content.php:865, pro/fields/class-acf-field-flexible-content.php:947
msgid "This field requires at least {min} {label} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:75
msgid "This field has a limit of {max} {label} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:78
msgid "{available} {label} {identifier} available (max {max})"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:79
msgid "{required} {label} {identifier} required (min {min})"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "Flexible Content requires at least 1 layout"
msgstr ""

#. translators: %s the button label used for adding a new layout.
#: pro/fields/class-acf-field-flexible-content.php:254
msgid "Click the \"%s\" button below to start creating your layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:374, pro/fields/class-acf-repeater-table.php:364
msgid "Drag to reorder"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:377
msgid "Add layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:378
msgid "Duplicate layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:379
msgid "Remove layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:380, pro/fields/class-acf-repeater-table.php:380
msgid "Click to toggle"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:516
msgid "Delete Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:517
msgid "Duplicate Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:518
msgid "Add New Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:518
msgid "Add Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:547
msgid "Label"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Name"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:602
msgid "Min"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:617
msgid "Max"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:658
msgid "Minimum Layouts"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:669
msgid "Maximum Layouts"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:680, pro/fields/class-acf-field-repeater.php:293
msgid "Button Label"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:1551, pro/fields/class-acf-field-repeater.php:912
msgid "%s must be of type array or null."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:1562
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-flexible-content.php:1578
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-gallery.php:22
msgid "Gallery"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:24
msgid "An interactive interface for managing a collection of attachments, such as images."
msgstr ""

#: pro/fields/class-acf-field-gallery.php:71
msgid "Add Image to Gallery"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:72
msgid "Maximum selection reached"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:281
msgid "Length"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:296
msgid "Edit"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:297, pro/fields/class-acf-field-gallery.php:450
msgid "Remove"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:313
msgid "Title"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:325
msgid "Caption"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:337
msgid "Alt Text"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:349, pro/admin/post-types/admin-ui-options-pages.php:117, pro/admin/views/acf-ui-options-page/advanced-settings.php:153
msgid "Description"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:459
msgid "Add to gallery"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:463
msgid "Bulk actions"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:464
msgid "Sort by date uploaded"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:465
msgid "Sort by date modified"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:466
msgid "Sort by title"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:467
msgid "Reverse current order"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:479
msgid "Close"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:507
msgid "Return Format"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:513
msgid "Image Array"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:514
msgid "Image URL"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:515
msgid "Image ID"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:523
msgid "Library"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:524
msgid "Limit the media library choice"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:529, pro/locations/class-acf-location-block.php:68
msgid "All"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:530
msgid "Uploaded to post"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:566
msgid "Minimum Selection"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:576
msgid "Maximum Selection"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:586
msgid "Minimum"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:587, pro/fields/class-acf-field-gallery.php:623
msgid "Restrict which images can be uploaded"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:590, pro/fields/class-acf-field-gallery.php:626
msgid "Width"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:601, pro/fields/class-acf-field-gallery.php:637
msgid "Height"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:613, pro/fields/class-acf-field-gallery.php:649
msgid "File size"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:622
msgid "Maximum"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:658
msgid "Allowed File Types"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:659
msgid "Comma separated list. Leave blank for all types"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:678
msgid "Insert"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:679
msgid "Specify where new attachments are added"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:683
msgid "Append to the end"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:684
msgid "Prepend to the beginning"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:692
msgid "Preview Size"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:786
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-repeater.php:29
msgid "Repeater"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:66, pro/fields/class-acf-field-repeater.php:461
msgid "Minimum rows not reached ({min} rows)"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:67
msgid "Maximum rows reached ({max} rows)"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:68
msgid "Error loading page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:69
msgid "Order will be assigned upon save"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:162
msgid "Sub Fields"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:195
msgid "Pagination"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:196
msgid "Useful for fields with a large number of rows."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:207
msgid "Rows Per Page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:208
msgid "Set the number of rows to be displayed on a page."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:240
msgid "Minimum Rows"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:251
msgid "Maximum Rows"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:281
msgid "Collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:282
msgid "Select a sub field to show when row is collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1049
msgid "Invalid nonce."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1054
msgid "Invalid field key or name."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1063
msgid "There was an error retrieving the field."
msgstr ""

#: pro/fields/class-acf-repeater-table.php:367
msgid "Click to reorder"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:400
msgid "Add row"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:401
msgid "Duplicate row"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:402
msgid "Remove row"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:446, pro/fields/class-acf-repeater-table.php:463, pro/fields/class-acf-repeater-table.php:464
msgid "Current Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:454, pro/fields/class-acf-repeater-table.php:455
msgid "First Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:458, pro/fields/class-acf-repeater-table.php:459
msgid "Previous Page"
msgstr ""

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:468
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:475, pro/fields/class-acf-repeater-table.php:476
msgid "Next Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:479, pro/fields/class-acf-repeater-table.php:480
msgid "Last Page"
msgstr ""

#: pro/locations/class-acf-location-block.php:73
msgid "No block types exist"
msgstr ""

#: pro/locations/class-acf-location-options-page.php:22
msgid "Options Page"
msgstr ""

#: pro/locations/class-acf-location-options-page.php:70
msgid "Select options page..."
msgstr ""

#: pro/locations/class-acf-location-options-page.php:74, pro/post-types/acf-ui-options-page.php:95, pro/admin/post-types/admin-ui-options-page.php:482
msgid "Add New Options Page"
msgstr ""

#: pro/post-types/acf-ui-options-page.php:92, pro/post-types/acf-ui-options-page.php:93, pro/admin/post-types/admin-ui-options-pages.php:94, pro/admin/post-types/admin-ui-options-pages.php:94
msgid "Options Pages"
msgstr ""

#: pro/post-types/acf-ui-options-page.php:94
msgid "Add New"
msgstr ""

#: pro/post-types/acf-ui-options-page.php:96
msgid "Edit Options Page"
msgstr ""

#: pro/post-types/acf-ui-options-page.php:97
msgid "New Options Page"
msgstr ""

#: pro/post-types/acf-ui-options-page.php:98
msgid "View Options Page"
msgstr ""

#: pro/post-types/acf-ui-options-page.php:99
msgid "Search Options Pages"
msgstr ""

#: pro/post-types/acf-ui-options-page.php:100
msgid "No Options Pages found"
msgstr ""

#: pro/post-types/acf-ui-options-page.php:101
msgid "No Options Pages found in Trash"
msgstr ""

#: pro/post-types/acf-ui-options-page.php:203
msgid "The menu slug must only contain lower case alphanumeric characters, underscores or dashes."
msgstr ""

#: pro/post-types/acf-ui-options-page.php:235
msgid "This Menu Slug is already in use by another ACF Options Page."
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:56
msgid "Options page deleted."
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:57
msgid "Options page updated."
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:60
msgid "Options page saved."
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:61
msgid "Options page submitted."
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:62
msgid "Options page scheduled for."
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:63
msgid "Options page draft updated."
msgstr ""

#. translators: %s options page name
#: pro/admin/post-types/admin-ui-options-page.php:83
msgid "%s options page updated"
msgstr ""

#. translators: %s options page name
#: pro/admin/post-types/admin-ui-options-page.php:85
msgid "Add fields to %s"
msgstr ""

#. translators: %s options page name
#: pro/admin/post-types/admin-ui-options-page.php:89
msgid "%s options page created"
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:102
msgid "Link existing field groups"
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:131
msgid "Post"
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:132
msgid "Posts"
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:133
msgid "Page"
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:134
msgid "Pages"
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:135
msgid "Default"
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:157
msgid "Basic Settings"
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:158
msgid "Advanced Settings"
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:283
msgctxt "post status"
msgid "Active"
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:283
msgctxt "post status"
msgid "Inactive"
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:361
msgid "No Parent"
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:450
msgid "The provided Menu Slug already exists."
msgstr ""

#: pro/admin/post-types/admin-ui-options-pages.php:118
msgid "Key"
msgstr ""

#: pro/admin/post-types/admin-ui-options-pages.php:122
msgid "Local JSON"
msgstr ""

#: pro/admin/post-types/admin-ui-options-pages.php:151
msgid "No description"
msgstr ""

#. translators: %s number of post types activated
#: pro/admin/post-types/admin-ui-options-pages.php:179
msgid "Options page activated."
msgid_plural "%s options pages activated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of post types deactivated
#: pro/admin/post-types/admin-ui-options-pages.php:186
msgid "Options page deactivated."
msgid_plural "%s options pages deactivated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of post types duplicated
#: pro/admin/post-types/admin-ui-options-pages.php:193
msgid "Options page duplicated."
msgid_plural "%s options pages duplicated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of post types synchronized
#: pro/admin/post-types/admin-ui-options-pages.php:200
msgid "Options page synchronized."
msgid_plural "%s options pages synchronized."
msgstr[0] ""
msgstr[1] ""

#: pro/admin/views/html-settings-updates.php:9
msgid "Deactivate License"
msgstr ""

#: pro/admin/views/html-settings-updates.php:9
msgid "Activate License"
msgstr ""

#: pro/admin/views/html-settings-updates.php:26
msgctxt "license status"
msgid "Inactive"
msgstr ""

#: pro/admin/views/html-settings-updates.php:34
msgctxt "license status"
msgid "Cancelled"
msgstr ""

#: pro/admin/views/html-settings-updates.php:32
msgctxt "license status"
msgid "Expired"
msgstr ""

#: pro/admin/views/html-settings-updates.php:30
msgctxt "license status"
msgid "Active"
msgstr ""

#: pro/admin/views/html-settings-updates.php:47
msgid "Subscription Status"
msgstr ""

#: pro/admin/views/html-settings-updates.php:45
msgid "License Status"
msgstr ""

#: pro/admin/views/html-settings-updates.php:60
msgid "Subscription Type"
msgstr ""

#: pro/admin/views/html-settings-updates.php:58
msgid "License Type"
msgstr ""

#: pro/admin/views/html-settings-updates.php:67
msgid "Lifetime - "
msgstr ""

#: pro/admin/views/html-settings-updates.php:81
msgid "Subscription Expires"
msgstr ""

#: pro/admin/views/html-settings-updates.php:79
msgid "Subscription Expired"
msgstr ""

#: pro/admin/views/html-settings-updates.php:118
msgid "Renew Subscription"
msgstr ""

#: pro/admin/views/html-settings-updates.php:136
msgid "License Information"
msgstr ""

#: pro/admin/views/html-settings-updates.php:170
msgid "License Key"
msgstr ""

#: pro/admin/views/html-settings-updates.php:191, pro/admin/views/html-settings-updates.php:157
msgid "Recheck License"
msgstr ""

#: pro/admin/views/html-settings-updates.php:142
msgid "Your license key is defined in wp-config.php."
msgstr ""

#: pro/admin/views/html-settings-updates.php:211
msgid "View pricing & purchase"
msgstr ""

#. translators: %s - link to ACF website
#: pro/admin/views/html-settings-updates.php:220
msgid "Don't have an ACF PRO license? %s"
msgstr ""

#: pro/admin/views/html-settings-updates.php:235
msgid "Update Information"
msgstr ""

#: pro/admin/views/html-settings-updates.php:242
msgid "Current Version"
msgstr ""

#: pro/admin/views/html-settings-updates.php:250
msgid "Latest Version"
msgstr ""

#: pro/admin/views/html-settings-updates.php:258
msgid "Update Available"
msgstr ""

#: pro/admin/views/html-settings-updates.php:265
msgid "No"
msgstr ""

#: pro/admin/views/html-settings-updates.php:263
msgid "Yes"
msgstr ""

#: pro/admin/views/html-settings-updates.php:272
msgid "Upgrade Notice"
msgstr ""

#: pro/admin/views/html-settings-updates.php:303
msgid "Check For Updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:300
msgid "Enter your license key to unlock updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:298
msgid "Update Plugin"
msgstr ""

#: pro/admin/views/html-settings-updates.php:296
msgid "Updates must be manually installed in this configuration"
msgstr ""

#: pro/admin/views/html-settings-updates.php:294
msgid "Update ACF in Network Admin"
msgstr ""

#: pro/admin/views/html-settings-updates.php:292
msgid "Please reactivate your license to unlock updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:290
msgid "Please upgrade WordPress to update ACF"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:20
msgid "Dashicon class name"
msgstr ""

#. translators: %s = "dashicon class name", link to the WordPress dashicon documentation.
#: pro/admin/views/acf-ui-options-page/advanced-settings.php:25
msgid "The icon used for the options page menu item in the admin dashboard. Can be a URL or %s to use for the icon."
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:59
msgid "Menu Icon"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:80
msgid "Menu Title"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:94
msgid "Learn more about menu positions."
msgstr ""

#. translators: %s - link to WordPress docs to learn more about menu positions.
#: pro/admin/views/acf-ui-options-page/advanced-settings.php:98, pro/admin/views/acf-ui-options-page/advanced-settings.php:104
msgid "The position in the menu where this page should appear. %s"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:108
msgid "The position in the menu where this child page should appear. The first child page is 0, the next is 1, etc."
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:115
msgid "Menu Position"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:129
msgid "Redirect to Child Page"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:130
msgid "When child pages exist for this parent page, this page will redirect to the first child page."
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:154
msgid "A descriptive summary of the options page."
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:163
msgid "Update Button Label"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:164
msgid "The label used for the submit button which updates the fields on the options page."
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:178
msgid "Updated Message"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:179
msgid "The message that is displayed after successfully updating the options page."
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:180
msgid "Updated Options"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:217
msgid "Capability"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:218
msgid "The capability required for this menu to be displayed to the user."
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:234
msgid "Data Storage"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:235
msgid "By default, the option page stores field data in the options table. You can make the page load field data from a post, user, or term."
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:238, pro/admin/views/acf-ui-options-page/advanced-settings.php:269
msgid "Custom Storage"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:258
msgid "Learn more about available settings."
msgstr ""

#. translators: %s = link to learn more about storage locations.
#: pro/admin/views/acf-ui-options-page/advanced-settings.php:263
msgid "Set a custom storage location. Can be a numeric post ID (123), or a string (`user_2`). %s"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:288
msgid "Autoload Options"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:289
msgid "Improve performance by loading the fields in the option records automatically when WordPress loads."
msgstr ""

#: pro/admin/views/acf-ui-options-page/basic-settings.php:20, pro/admin/views/acf-ui-options-page/create-options-page-modal.php:16
msgid "Page Title"
msgstr ""

#. translators: example options page name
#: pro/admin/views/acf-ui-options-page/basic-settings.php:22, pro/admin/views/acf-ui-options-page/create-options-page-modal.php:18
msgid "Site Settings"
msgstr ""

#: pro/admin/views/acf-ui-options-page/basic-settings.php:37, pro/admin/views/acf-ui-options-page/create-options-page-modal.php:33
msgid "Menu Slug"
msgstr ""

#: pro/admin/views/acf-ui-options-page/basic-settings.php:52, pro/admin/views/acf-ui-options-page/create-options-page-modal.php:47
msgid "Parent Page"
msgstr ""

#: pro/admin/views/acf-ui-options-page/basic-settings.php:72
msgid "Advanced Configuration"
msgstr ""

#: pro/admin/views/acf-ui-options-page/basic-settings.php:73
msgid "I know what I'm doing, show me all the options."
msgstr ""

#: pro/admin/views/acf-ui-options-page/create-options-page-modal.php:62
msgid "Cancel"
msgstr ""

#: pro/admin/views/acf-ui-options-page/create-options-page-modal.php:63
msgid "Done"
msgstr ""

#. translators: %s URL to ACF options pages documentation
#: pro/admin/views/acf-ui-options-page/list-empty.php:10
msgid "ACF <a href=\"%s\" target=\"_blank\">options pages</a> are custom admin pages for managing global settings via fields. You can create multiple pages and sub-pages."
msgstr ""

#. translators: %s url to getting started guide
#: pro/admin/views/acf-ui-options-page/list-empty.php:16
msgid "New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting started guide</a>."
msgstr ""

#: pro/admin/views/acf-ui-options-page/list-empty.php:32
msgid "Upgrade to ACF PRO to create options pages in just a few clicks"
msgstr ""

#: pro/admin/views/acf-ui-options-page/list-empty.php:30
msgid "Add Your First Options Page"
msgstr ""

#: pro/admin/views/acf-ui-options-page/list-empty.php:43
msgid "Learn More"
msgstr ""

#: pro/admin/views/acf-ui-options-page/list-empty.php:44
msgid "Upgrade to ACF PRO"
msgstr ""

#: pro/admin/views/acf-ui-options-page/list-empty.php:40
msgid "Add Options Page"
msgstr ""
