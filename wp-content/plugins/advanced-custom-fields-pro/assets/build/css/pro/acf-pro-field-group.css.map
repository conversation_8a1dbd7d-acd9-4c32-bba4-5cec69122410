{"version": 3, "file": "acf-pro-field-group.css", "mappings": ";;;AAAA,gBAAgB;ACAhB;;;;8FAAA;AAMA;AAOA;AAQA;AAgBA;;;;8FAAA;ACrCA;;;;8FAAA;ACAA;;;;+FAAA;AAOC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHkBF;AGfC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHiBF;AGfW;EACR;EACA;EACA;EACA;EACA;AHiBH;;AGZC;EACC;EACA;AHeF;AGZC;EACC;EACA;AHcF;AGVC;EACC;AHYF;AGRC;EACC;EACA;AHUF;AGRE;EACC;EACA;AHUH;AGPE;EACC;EACA;EACA;AHSH;AGNE;EACC;EACA;EACA;EAEC;AHOJ;AGHE;EACC;EACA;EAEC;AHIJ;AGAE;EACC;EACA;EAEC;AHCJ;AGGE;EACC;EACA;EAEC;AHFJ;AGME;;;EAGC;AHJH;AGQC;;EAEC;AHNF;AGaE;EACC;EACA;EACA;AHXH;;AGiBA;;;;+FAAA;AAMA;EAEC;EAOA;AHtBD;AGyBE;;;;EAIC;AHvBH;;AG8BA;;;;8EAAA;AAOE;EACC;EACA;EACA;AH7BH;AGgCE;EACC;EACA,cFtIQ;ADwGX;AGiCE;EACC;AH/BH,C", "sources": ["webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/pro/acf-pro-field-group.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_variables.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_mixins.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/pro/_field-group.scss"], "sourcesContent": ["@charset \"UTF-8\";\n/*--------------------------------------------------------------------------------------------\n*\n*\tVars\n*\n*--------------------------------------------------------------------------------------------*/\n/* colors */\n/* acf-field */\n/* responsive */\n/*--------------------------------------------------------------------------------------------\n*\n*\tACF 6 ↓\n*\n*--------------------------------------------------------------------------------------------*/\n/*--------------------------------------------------------------------------------------------\n*\n*  Mixins\n*\n*--------------------------------------------------------------------------------------------*/\n/*---------------------------------------------------------------------------------------------\n*\n*\tFlexible Content\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-field-setting-fc_layout .acf-toggle-fc-layout {\n  width: 34px;\n  height: 31px;\n  margin: 0;\n  padding: 0;\n  border: 0;\n  background: transparent;\n  cursor: pointer;\n  left: 20.83%;\n  right: 20.83%;\n  top: 33.33%;\n  bottom: 33.33%;\n}\n.acf-field-setting-fc_layout .toggle-indicator::before {\n  z-index: -1;\n  content: \"\";\n  display: inline-flex;\n  width: 20px;\n  height: 20px;\n  margin-left: -28px;\n  background-color: currentColor;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  -webkit-mask-image: url(../../../images/icons/icon-chevron-down.svg);\n  mask-image: url(../../../images/icons/icon-chevron-down.svg);\n}\n.rtl .acf-field-setting-fc_layout .toggle-indicator::before {\n  margin-left: 0px;\n  position: absolute;\n  top: 9px;\n  z-index: 100;\n  left: 8px;\n}\n\n.acf-field-setting-fc_layout .toggle-indicator.open::before {\n  -webkit-mask-image: url(../../../images/icons/icon-chevron-up.svg);\n  mask-image: url(../../../images/icons/icon-chevron-up.svg);\n}\n.acf-field-setting-fc_layout .toggle-indicator.closed::before {\n  -webkit-mask-image: url(../../../images/icons/icon-chevron-down.svg);\n  mask-image: url(../../../images/icons/icon-chevron-down.svg);\n}\n.acf-field-setting-fc_layout .acf-flexible-content-field-label-name {\n  padding-left: 5px;\n}\n.acf-field-setting-fc_layout .acf-fc-meta {\n  margin: 0 0 10px;\n  padding: 0;\n}\n.acf-field-setting-fc_layout .acf-fc-meta li {\n  margin: 0 0 10px;\n  padding: 0;\n}\n.acf-field-setting-fc_layout .acf-fc-meta .acf-fc-meta-display {\n  float: left;\n  width: 100%;\n  padding-right: 5px;\n}\n.acf-field-setting-fc_layout .acf-fc-meta .acf-fc-meta-left {\n  width: calc(50% - 4px);\n  float: left;\n  clear: left;\n  margin-right: 4px;\n}\n.acf-field-setting-fc_layout .acf-fc-meta .acf-fc-meta-right {\n  width: calc(50% - 4px);\n  float: left;\n  margin-left: 4px;\n}\n.acf-field-setting-fc_layout .acf-fc-meta .acf-fc-meta-min {\n  width: calc(25% - 5px);\n  float: left;\n  margin-right: 5px;\n}\n.acf-field-setting-fc_layout .acf-fc-meta .acf-fc-meta-max {\n  width: calc(25% - 10px);\n  float: left;\n  margin-left: 4px;\n}\n.acf-field-setting-fc_layout .acf-fc-meta .acf-fc-meta-label .acf-input-prepend,\n.acf-field-setting-fc_layout .acf-fc-meta .acf-fc-meta-name .acf-input-prepend,\n.acf-field-setting-fc_layout .acf-fc-meta .acf-fc-meta-display .acf-input-prepend {\n  min-width: 60px;\n}\n.acf-field-setting-fc_layout .acf-fc_draggable,\n.acf-field-setting-fc_layout .reorder-layout {\n  cursor: grab;\n}\n.acf-field-setting-fc_layout .acf-fl-actions a {\n  padding: 1px 0;\n  font-size: 13px;\n  line-height: 20px;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*\tClone\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-field-object-clone {\n  /* group */\n  /* seamless */\n}\n.acf-field-object-clone[data-display=seamless] .acf-field-setting-instructions,\n.acf-field-object-clone[data-display=seamless] .acf-field-setting-layout,\n.acf-field-object-clone[data-display=seamless] .acf-field-setting-wrapper,\n.acf-field-object-clone[data-display=seamless] .acf-field-setting-conditional_logic {\n  display: none;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Pro fields with inactive licenses.\n*\n*----------------------------------------------------------------------------*/\n.acf-pro-inactive-license .acf-pro-field-object .li-field-label:before {\n  -webkit-mask-image: url(\"../../../images/icons/icon-lock.svg\") !important;\n  mask-image: url(\"../../../images/icons/icon-lock.svg\") !important;\n  pointer-events: none;\n}\n.acf-pro-inactive-license .acf-pro-field-object .edit-field {\n  pointer-events: none;\n  color: #667085;\n}\n.acf-pro-inactive-license .acf-pro-field-object .row-options {\n  display: none;\n}", "/*--------------------------------------------------------------------------------------------\n*\n*\tVars\n*\n*--------------------------------------------------------------------------------------------*/\n\n/* colors */\n$acf_blue: #2a9bd9;\n$acf_notice: #2a9bd9;\n$acf_error: #d94f4f;\n$acf_success: #49ad52;\n$acf_warning: #fd8d3b;\n\n/* acf-field */\n$field_padding: 15px 12px;\n$field_padding_x: 12px;\n$field_padding_y: 15px;\n$fp: 15px 12px;\n$fy: 15px;\n$fx: 12px;\n\n/* responsive */\n$md: 880px;\n$sm: 640px;\n\n// Admin.\n$wp-card-border: #ccd0d4;\t\t\t// Card border.\n$wp-card-border-1: #d5d9dd;\t\t  // Card inner border 1: Structural (darker).\n$wp-card-border-2: #eeeeee;\t\t  // Card inner border 2: Fields (lighter).\n$wp-input-border: #7e8993;\t\t   // Input border.\n\n// Admin 3.8\n$wp38-card-border: #E5E5E5;\t\t  // Card border.\n$wp38-card-border-1: #dfdfdf;\t\t// Card inner border 1: Structural (darker).\n$wp38-card-border-2: #eeeeee;\t\t// Card inner border 2: Fields (lighter).\n$wp38-input-border: #dddddd;\t\t // Input border.\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tACF 6 ↓\n*\n*--------------------------------------------------------------------------------------------*/\n\n// Grays\n$gray-50:  #F9FAFB;\n$gray-100: #F2F4F7;\n$gray-200: #EAECF0;\n$gray-300: #D0D5DD;\n$gray-400: #98A2B3;\n$gray-500: #667085;\n$gray-600: #475467;\n$gray-700: #344054;\n$gray-800: #1D2939;\n$gray-900: #101828;\n\n// Blues\n$blue-50:  #EBF5FA;\n$blue-100: #D8EBF5;\n$blue-200: #A5D2E7;\n$blue-300: #6BB5D8;\n$blue-400: #399CCB;\n$blue-500: #0783BE;\n$blue-600: #066998;\n$blue-700: #044E71;\n$blue-800: #033F5B;\n$blue-900: #032F45;\n\n// Utility\n$color-info:\t#2D69DA;\n$color-success:\t#52AA59;\n$color-warning:\t#F79009;\n$color-danger:\t#D13737;\n\n$color-primary: $blue-500;\n$color-primary-hover: $blue-600;\n$color-secondary: $gray-500;\n$color-secondary-hover: $gray-400;\n\n// Gradients\n$gradient-pro: radial-gradient(141.77% 141.08% at 100.26% 99.25%, #0ECAD4 0%, #7A45E5 100%);\n\n// Border radius\n$radius-sm:\t4px;\n$radius-md: 6px;\n$radius-lg: 8px;\n$radius-xl: 12px;\n\n// Elevations / Box shadows\n$elevation-01: 0px 1px 2px rgba($gray-900, 0.10);\n\n// Input & button focus outline\n$outline: 3px solid $blue-50;\n\n// Link colours\n$link-color: $blue-500;\n\n// Responsive\n$max-width: 1440px;", "/*--------------------------------------------------------------------------------------------\n*\n*  Mixins\n*\n*--------------------------------------------------------------------------------------------*/\n@mixin clearfix() {\n\t&:after {\n\t\tdisplay: block;\n\t\tclear: both;\n\t\tcontent: \"\";\n\t}\n}\n\n@mixin border-box() {\n\t-webkit-box-sizing: border-box;\n\t-moz-box-sizing: border-box;\n\tbox-sizing: border-box;\n}\n\n@mixin centered() {\n\tposition: absolute;\n\ttop: 50%;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n}\n\n@mixin animate( $properties: 'all' ) {\n\t-webkit-transition: $properties 0.3s ease;  // Safari 3.2+, Chrome\n    -moz-transition: $properties 0.3s ease;  \t// Firefox 4-15\n    -o-transition: $properties 0.3s ease;  \t\t// Opera 10.5–12.00\n    transition: $properties 0.3s ease;  \t\t// Firefox 16+, Opera 12.50+\n}\n\n@mixin rtl() {\n\thtml[dir=\"rtl\"] & {\n\t\ttext-align: right;\n\t\t@content;\n\t}\n}\n\n@mixin wp-admin( $version: '3-8' ) {\n\t.acf-admin-#{$version} & {\n\t\t@content;\n\t}\n}", "/*---------------------------------------------------------------------------------------------\n*\n*\tFlexible Content\n*\n*---------------------------------------------------------------------------------------------*/\n\n.acf-field-setting-fc_layout {\n\t.acf-toggle-fc-layout {\n\t\twidth: 34px;\n\t\theight: 31px;\n\t\tmargin: 0;\n\t\tpadding: 0;\n\t\tborder: 0;\n\t\tbackground: transparent;\n\t\tcursor: pointer;\n\t\tleft: 20.83%;\n\t\tright: 20.83%;\n\t\ttop: 33.33%;\n\t\tbottom: 33.33%;\n\t}\n\n\t.toggle-indicator::before {\n\t\tz-index:-1;\n\t\tcontent: \"\";\n\t\tdisplay: inline-flex;\n\t\twidth: 20px;\n\t\theight: 20px;\n\t\tmargin-left: -28px;\n\t\tbackground-color: currentColor;\n\t\tborder: none;\n\t\tborder-radius: 0;\n\t\t-webkit-mask-size: contain;\n\t\tmask-size: contain;\n\t\t-webkit-mask-repeat: no-repeat;\n\t\tmask-repeat: no-repeat;\n\t\t-webkit-mask-position: center;\n\t\tmask-position: center;\n\t\t-webkit-mask-image: url(../../../images/icons/icon-chevron-down.svg);\n\t\tmask-image: url(../../../images/icons/icon-chevron-down.svg);\n\n\t\t@at-root .rtl #{&} {\n\t\t\tmargin-left: 0px;\n\t\t\tposition: absolute;\n\t\t\ttop: 9px;\n\t\t\tz-index: 100;\n\t\t\tleft: 8px;\n\t\t}\n\n\t}\n\n\t.toggle-indicator.open::before{\n\t\t-webkit-mask-image: url(../../../images/icons/icon-chevron-up.svg);\n\t\tmask-image: url(../../../images/icons/icon-chevron-up.svg);\n\t}\n\n\t.toggle-indicator.closed::before{\n\t\t-webkit-mask-image: url(../../../images/icons/icon-chevron-down.svg);\n\t\tmask-image: url(../../../images/icons/icon-chevron-down.svg);\n\t}\n\n\t// name label\n\t.acf-flexible-content-field-label-name {\n\t\tpadding-left: 5px;\n\t}\n\n\t// meta\n\t.acf-fc-meta {\n\t\tmargin: 0 0 10px;\n\t\tpadding: 0;\n\n\t\tli {\n\t\t\tmargin: 0 0 10px;\n\t\t\tpadding: 0;\n\t\t}\n\n\t\t.acf-fc-meta-display {\n\t\t\tfloat: left;\n\t\t\twidth: 100%;\n\t\t\tpadding-right: 5px;\n\t\t}\n\n\t\t.acf-fc-meta-left {\n\t\t\twidth: calc(50% - 4px);\n\t\t\tfloat: left;\n\t\t\tclear: left;\n\t\t\tmargin: {\n\t\t\t\tright: 4px;\n\t\t\t};\n\t\t}\n\n\t\t.acf-fc-meta-right {\n\t\t\twidth: calc(50% - 4px);\n\t\t\tfloat: left;\n\t\t\tmargin: {\n\t\t\t\tleft: 4px;\n\t\t\t};\n\t\t}\n\n\t\t.acf-fc-meta-min {\n\t\t\twidth: calc(25% - 5px);\n\t\t\tfloat: left;\n\t\t\tmargin: {\n\t\t\t\tright: 5px;\n\t\t\t};\n\t\t}\n\n\t\t.acf-fc-meta-max {\n\t\t\twidth: calc(25% - 10px);\n\t\t\tfloat: left;\n\t\t\tmargin: {\n\t\t\t\tleft: 4px;\n\t\t\t};\n\t\t}\n\n\t\t.acf-fc-meta-label .acf-input-prepend,\n\t\t.acf-fc-meta-name .acf-input-prepend,\n\t\t.acf-fc-meta-display .acf-input-prepend {\n\t\t\tmin-width: 60px;\n\t\t}\n\t}\n\n\t.acf-fc_draggable,\n\t.reorder-layout {\n\t\tcursor: grab;\n\t}\n\n\t// actions\n\t.acf-fl-actions {\n\t\t// visibility: hidden;\n\n\t\ta {\n\t\t\tpadding: 1px 0;\n\t\t\tfont-size: 13px;\n\t\t\tline-height: 20px;\n\t\t}\n\t}\n}\n\n\n/*---------------------------------------------------------------------------------------------\n*\n*\tClone\n*\n*---------------------------------------------------------------------------------------------*/\n\n.acf-field-object-clone {\n\n\t/* group */\n\t&[data-display=\"group\"] {\n\n\n\t}\n\n\n\t/* seamless */\n\t&[data-display=\"seamless\"] {\n\n\t\t.acf-field-setting-instructions,\n\t\t.acf-field-setting-layout,\n\t\t.acf-field-setting-wrapper,\n\t\t.acf-field-setting-conditional_logic {\n\t\t\tdisplay: none;\n\t\t}\n\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Pro fields with inactive licenses.\n*\n*----------------------------------------------------------------------------*/\n.acf-pro-inactive-license {\n\t.acf-pro-field-object {\n\t\t.li-field-label:before {\n\t\t\t-webkit-mask-image: url(\"../../../images/icons/icon-lock.svg\") !important;\n\t\t\tmask-image: url(\"../../../images/icons/icon-lock.svg\") !important;\n\t\t\tpointer-events: none;\n\t\t}\n\n\t\t.edit-field\t{\n\t\t\tpointer-events: none;\n\t\t\tcolor: $gray-500;\n\t\t}\n\n\t\t.row-options {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n}"], "names": [], "sourceRoot": ""}