(()=>{var t={1018:()=>{!function(t,e){"use strict";acf.hooks=new function(){var t={removeFilter:function(e,i){return"string"==typeof e&&n("filters",e,i),t},applyFilters:function(){var e=Array.prototype.slice.call(arguments),n=e.shift();return"string"==typeof n?o("filters",n,e):t},addFilter:function(e,n,o,r){return"string"==typeof e&&"function"==typeof n&&i("filters",e,n,o=parseInt(o||10,10),r),t},removeAction:function(e,i){return"string"==typeof e&&n("actions",e,i),t},doAction:function(){var e=Array.prototype.slice.call(arguments),n=e.shift();return"string"==typeof n&&o("actions",n,e),t},addAction:function(e,n,o,r){return"string"==typeof e&&"function"==typeof n&&i("actions",e,n,o=parseInt(o||10,10),r),t},storage:function(){return e}},e={actions:{},filters:{}};function n(t,n,i,o){if(e[t][n])if(i){var r,a=e[t][n];if(o)for(r=a.length;r--;){var s=a[r];s.callback===i&&s.context===o&&a.splice(r,1)}else for(r=a.length;r--;)a[r].callback===i&&a.splice(r,1)}else e[t][n]=[]}function i(t,n,i,o,r){var a={callback:i,priority:o,context:r},s=e[t][n];s?(s.push(a),s=function(t){for(var e,n,i,o=1,r=t.length;o<r;o++){for(e=t[o],n=o;(i=t[n-1])&&i.priority>e.priority;)t[n]=t[n-1],--n;t[n]=e}return t}(s)):s=[a],e[t][n]=s}function o(t,n,i){var o=e[t][n];if(!o)return"filters"===t&&i[0];var r=0,a=o.length;if("filters"===t)for(;r<a;r++)i[0]=o[r].callback.apply(o[r].context,i);else for(;r<a;r++)o[r].callback.apply(o[r].context,i);return"filters"!==t||i[0]}return t}}(window)},5751:()=>{var t;t=jQuery,acf.models.Modal=acf.Model.extend({data:{title:"",content:"",toolbar:""},events:{"click .acf-modal-close":"onClickClose"},setup:function(e){t.extend(this.data,e),this.$el=t(),this.render()},initialize:function(){this.open()},render:function(){var e=this.get("title"),n=this.get("content"),i=this.get("toolbar"),o=t(["<div>",'<div class="acf-modal">','<div class="acf-modal-title">',"<h2>"+e+"</h2>",'<button class="acf-modal-close" type="button"><span class="dashicons dashicons-no"></span></button>',"</div>",'<div class="acf-modal-content">'+n+"</div>",'<div class="acf-modal-toolbar">'+i+"</div>","</div>",'<div class="acf-modal-backdrop acf-modal-close"></div>',"</div>"].join(""));this.$el&&this.$el.replaceWith(o),this.$el=o,acf.doAction("append",o)},update:function(t){this.data=acf.parseArgs(t,this.data),this.render()},title:function(t){this.$(".acf-modal-title h2").html(t)},content:function(t){this.$(".acf-modal-content").html(t)},toolbar:function(t){this.$(".acf-modal-toolbar").html(t)},open:function(){t("body").append(this.$el)},close:function(){this.remove()},onClickClose:function(t,e){t.preventDefault(),this.close()},focus:function(){this.$el.find(".acf-icon").first().trigger("focus")},lockFocusToModal:function(e){let n=t("#wpwrap");n.length&&(n[0].inert=e,n.attr("aria-hidden",e))},returnFocusToOrigin:function(){this.data.openedBy instanceof t&&this.data.openedBy.closest("body").length>0&&this.data.openedBy.trigger("focus")}}),acf.newModal=function(t){return new acf.models.Modal(t)}},6891:()=>{var t,e,n;t=jQuery,e=/^(\S+)\s*(.*)$/,n=acf.Model=function(){this.cid=acf.uniqueId("acf"),this.data=t.extend(!0,{},this.data),this.setup.apply(this,arguments),this.$el&&!this.$el.data("acf")&&this.$el.data("acf",this);var e=function(){this.initialize(),this.addEvents(),this.addActions(),this.addFilters()};this.wait&&!acf.didAction(this.wait)?this.addAction(this.wait,e):e.apply(this)},t.extend(n.prototype,{id:"",cid:"",$el:null,data:{},busy:!1,changed:!1,events:{},actions:{},filters:{},eventScope:"",wait:!1,priority:10,get:function(t){return this.data[t]},has:function(t){return null!=this.get(t)},set:function(t,e,n){var i=this.get(t);return i==e||(this.data[t]=e,n||(this.changed=!0,this.trigger("changed:"+t,[e,i]),this.trigger("changed",[t,e,i]))),this},inherit:function(e){return e instanceof jQuery&&(e=e.data()),t.extend(this.data,e),this},prop:function(){return this.$el.prop.apply(this.$el,arguments)},setup:function(e){t.extend(this,e)},initialize:function(){},addElements:function(t){if(!(t=t||this.elements||null)||!Object.keys(t).length)return!1;for(var e in t)this.addElement(e,t[e])},addElement:function(t,e){this["$"+t]=this.$(e)},addEvents:function(t){if(!(t=t||this.events||null))return!1;for(var n in t){var i=n.match(e);this.on(i[1],i[2],t[n])}},removeEvents:function(t){if(!(t=t||this.events||null))return!1;for(var n in t){var i=n.match(e);this.off(i[1],i[2],t[n])}},getEventTarget:function(e,n){return e||this.$el||t(document)},validateEvent:function(e){return!this.eventScope||t(e.target).closest(this.eventScope).is(this.$el)},proxyEvent:function(e){return this.proxy((function(n){if(this.validateEvent(n)){var i=acf.arrayArgs(arguments).slice(1),o=[n,t(n.currentTarget)].concat(i);e.apply(this,o)}}))},on:function(t,e,n,i){var o,r,a,s,c;t instanceof jQuery?i?(o=t,r=e,a=n,s=i):(o=t,r=e,s=n):n?(r=t,a=e,s=n):(r=t,s=e),o=this.getEventTarget(o),"string"==typeof s&&(s=this.proxyEvent(this[s])),r=r+"."+this.cid,c=a?[r,a,s]:[r,s],o.on.apply(o,c)},off:function(t,e,n){var i,o,r,a;t instanceof jQuery?n?(i=t,o=e,r=n):(i=t,o=e):e?(o=t,r=e):o=t,i=this.getEventTarget(i),o=o+"."+this.cid,a=r?[o,r]:[o],i.off.apply(i,a)},trigger:function(t,e,n){var i=this.getEventTarget();return n?i.trigger.apply(i,arguments):i.triggerHandler.apply(i,arguments),this},addActions:function(t){if(!(t=t||this.actions||null))return!1;for(var e in t)this.addAction(e,t[e])},removeActions:function(t){if(!(t=t||this.actions||null))return!1;for(var e in t)this.removeAction(e,t[e])},addAction:function(t,e,n){n=n||this.priority,"string"==typeof e&&(e=this[e]),acf.addAction(t,e,n,this)},removeAction:function(t,e){acf.removeAction(t,this[e])},addFilters:function(t){if(!(t=t||this.filters||null))return!1;for(var e in t)this.addFilter(e,t[e])},addFilter:function(t,e,n){n=n||this.priority,"string"==typeof e&&(e=this[e]),acf.addFilter(t,e,n,this)},removeFilters:function(t){if(!(t=t||this.filters||null))return!1;for(var e in t)this.removeFilter(e,t[e])},removeFilter:function(t,e){acf.removeFilter(t,this[e])},$:function(t){return this.$el.find(t)},remove:function(){this.removeEvents(),this.removeActions(),this.removeFilters(),this.$el.remove()},setTimeout:function(t,e){return setTimeout(this.proxy(t),e)},time:function(){console.time(this.id||this.cid)},timeEnd:function(){console.timeEnd(this.id||this.cid)},show:function(){acf.show(this.$el)},hide:function(){acf.hide(this.$el)},proxy:function(e){return t.proxy(e,this)}}),n.extend=function(e){var n,i=this;return n=e&&e.hasOwnProperty("constructor")?e.constructor:function(){return i.apply(this,arguments)},t.extend(n,i),n.prototype=Object.create(i.prototype),t.extend(n.prototype,e),n.prototype.constructor=n,n},acf.models={},acf.getInstance=function(t){return t.data("acf")},acf.getInstances=function(e){var n=[];return e.each((function(){n.push(acf.getInstance(t(this)))})),n}},2700:()=>{var t,e;t=jQuery,e=acf.Model.extend({data:{text:"",type:"",timeout:0,dismiss:!0,target:!1,location:"before",close:function(){}},events:{"click .acf-notice-dismiss":"onClickClose"},tmpl:function(){return'<div class="acf-notice"></div>'},setup:function(e){t.extend(this.data,e),this.$el=t(this.tmpl())},initialize:function(){this.render(),this.show()},render:function(){this.type(this.get("type")),this.html("<p>"+this.get("text")+"</p>"),this.get("dismiss")&&(this.$el.append('<a href="#" class="acf-notice-dismiss acf-icon -cancel small"></a>'),this.$el.addClass("-dismiss"));var t=this.get("timeout");t&&this.away(t)},update:function(e){t.extend(this.data,e),this.initialize(),this.removeEvents(),this.addEvents()},show:function(){var t=this.get("target"),e=this.get("location");t&&("after"===e?t.append(this.$el):t.prepend(this.$el))},hide:function(){this.$el.remove()},away:function(t){this.setTimeout((function(){acf.remove(this.$el)}),t)},type:function(t){var e=this.get("type");e&&this.$el.removeClass("-"+e),this.$el.addClass("-"+t),"error"==t&&this.$el.addClass("acf-error-message")},html:function(t){this.$el.html(acf.escHtml(t))},text:function(t){this.$("p").html(acf.escHtml(t))},onClickClose:function(t,e){t.preventDefault(),this.get("close").apply(this,arguments),this.remove()}}),acf.newNotice=function(t){return"object"!=typeof t&&(t={text:t}),new e(t)},new acf.Model({wait:"prepare",priority:1,initialize:function(){t(".acf-admin-notice").each((function(){if(t(this).data("persisted")){let e=acf.getPreference("dismissed-notices");e&&"object"==typeof e&&e.includes(t(this).data("persist-id"))?t(this).remove():(t(this).show(),t(this).on("click",".notice-dismiss",(function(n){e=acf.getPreference("dismissed-notices"),e&&"object"==typeof e||(e=[]),e.push(t(this).closest(".acf-admin-notice").data("persist-id")),acf.setPreference("dismissed-notices",e)})))}}))}})},9340:()=>{jQuery,new acf.Model({events:{"click .acf-panel-title":"onClick"},onClick:function(t,e){t.preventDefault(),this.toggle(e.parent())},isOpen:function(t){return t.hasClass("-open")},toggle:function(t){this.isOpen(t)?this.close(t):this.open(t)},open:function(t){t.addClass("-open"),t.find(".acf-panel-title i").attr("class","dashicons dashicons-arrow-down")},close:function(t){t.removeClass("-open"),t.find(".acf-panel-title i").attr("class","dashicons dashicons-arrow-right")}})},4204:()=>{var t;t=jQuery,acf.models.Popup=acf.Model.extend({data:{title:"",content:"",width:0,height:0,loading:!1,openedBy:null},events:{'click [data-event="close"]':"onClickClose","click .acf-close-popup":"onClickClose",keydown:"onPressEscapeClose"},setup:function(e){t.extend(this.data,e),this.$el=t(this.tmpl())},initialize:function(){this.render(),this.open(),this.focus(),this.lockFocusToPopup(!0)},tmpl:function(){return['<div id="acf-popup" role="dialog" tabindex="-1">','<div class="acf-popup-box acf-box">','<div class="title"><h3></h3><a href="#" class="acf-icon -cancel grey" data-event="close" aria-label="'+acf.__("Close modal")+'"></a></div>','<div class="inner"></div>','<div class="loading"><i class="acf-loading"></i></div>',"</div>",'<div class="bg" data-event="close"></div>',"</div>"].join("")},render:function(){var t=this.get("title"),e=this.get("content"),n=this.get("loading"),i=this.get("width"),o=this.get("height");this.title(t),this.content(e),i&&this.$(".acf-popup-box").css("width",i),o&&this.$(".acf-popup-box").css("min-height",o),this.loading(n),acf.doAction("append",this.$el)},focus:function(){this.$el.find(".acf-icon").first().trigger("focus")},lockFocusToPopup:function(e){let n=t("#wpwrap");n.length&&(n[0].inert=e,n.attr("aria-hidden",e))},update:function(t){this.data=acf.parseArgs(t,this.data),this.render()},title:function(t){this.$(".title:first h3").html(t)},content:function(t){this.$(".inner:first").html(t)},loading:function(t){var e=this.$(".loading:first");t?e.show():e.hide()},open:function(){t("body").append(this.$el)},close:function(){this.lockFocusToPopup(!1),this.returnFocusToOrigin(),this.remove()},onClickClose:function(t,e){t.preventDefault(),this.close()},onPressEscapeClose:function(t){"Escape"===t.key&&this.close()},returnFocusToOrigin:function(){this.data.openedBy instanceof t&&this.data.openedBy.closest("body").length>0&&this.data.openedBy.trigger("focus")}}),acf.newPopup=function(t){return new acf.models.Popup(t)}},2177:()=>{!function(t,e){acf.newTooltip=function(t){return"object"!=typeof t&&(t={text:t}),t.confirmRemove!==e?(t.textConfirm=acf.__("Remove"),t.textCancel=acf.__("Cancel"),new i(t)):t.confirm!==e?new i(t):new n(t)};var n=acf.Model.extend({data:{text:"",timeout:0,target:null},tmpl:function(){return'<div class="acf-tooltip"></div>'},setup:function(e){t.extend(this.data,e),this.$el=t(this.tmpl())},initialize:function(){this.render(),this.show(),this.position();var e=this.get("timeout");e&&setTimeout(t.proxy(this.fade,this),e)},update:function(e){t.extend(this.data,e),this.initialize()},render:function(){this.html(this.get("text"))},show:function(){t("body").append(this.$el)},hide:function(){this.$el.remove()},fade:function(){this.$el.addClass("acf-fade-up"),this.setTimeout((function(){this.remove()}),250)},html:function(t){this.$el.html(t)},position:function(){var e=this.$el,n=this.get("target");if(n){e.removeClass("right left bottom top").css({top:0,left:0});var i=n.outerWidth(),o=n.outerHeight(),r=n.offset().top,a=n.offset().left,s=e.outerWidth(),c=e.outerHeight(),l=e.offset().top,u=r-c-l,f=a+i/2-s/2;f<10?(e.addClass("right"),f=a+i,u=r+o/2-c/2-l):f+s+10>t(window).width()?(e.addClass("left"),f=a-s,u=r+o/2-c/2-l):u-t(window).scrollTop()<10?(e.addClass("bottom"),u=r+o-l):e.addClass("top"),e.css({top:u,left:f})}}}),i=n.extend({data:{text:"",textConfirm:"",textCancel:"",target:null,targetConfirm:!0,confirm:function(){},cancel:function(){},context:!1},events:{'click [data-event="cancel"]':"onCancel",'click [data-event="confirm"]':"onConfirm"},addEvents:function(){acf.Model.prototype.addEvents.apply(this);var e=t(document),n=this.get("target");this.setTimeout((function(){this.on(e,"click","onCancel")})),this.get("targetConfirm")&&this.on(n,"click","onConfirm")},removeEvents:function(){acf.Model.prototype.removeEvents.apply(this);var e=t(document),n=this.get("target");this.off(e,"click"),this.off(n,"click")},render:function(){var t=[this.get("text")||acf.__("Are you sure?"),'<a href="#" data-event="confirm">'+(this.get("textConfirm")||acf.__("Yes"))+"</a>",'<a href="#" data-event="cancel">'+(this.get("textCancel")||acf.__("No"))+"</a>"].join(" ");this.html(t),this.$el.addClass("-confirm")},onCancel:function(t,e){t.preventDefault(),t.stopImmediatePropagation();var n=this.get("cancel"),i=this.get("context")||this;n.apply(i,arguments),this.remove()},onConfirm:function(t,e){t.preventDefault(),t.stopImmediatePropagation();var n=this.get("confirm"),i=this.get("context")||this;n.apply(i,arguments),this.remove()}});acf.models.Tooltip=n,acf.models.TooltipConfirm=i,new acf.Model({tooltip:!1,events:{"mouseenter .acf-js-tooltip":"showTitle","mouseup .acf-js-tooltip":"hideTitle","mouseleave .acf-js-tooltip":"hideTitle","focus .acf-js-tooltip":"showTitle","blur .acf-js-tooltip":"hideTitle","keyup .acf-js-tooltip":"onKeyUp"},showTitle:function(t,e){var n=e.attr("title");n&&(e.attr("title",""),this.tooltip?this.tooltip.update({text:n,target:e}):this.tooltip=acf.newTooltip({text:n,target:e}))},hideTitle:function(t,e){this.tooltip.hide(),e.attr("title",this.tooltip.get("text"))},onKeyUp:function(t,e){"Escape"===t.key&&this.hideTitle(t,e)}})}(jQuery)},6047:()=>{!function(t,e){var n={};window.acf=n,n.data={},n.get=function(t){return this.data[t]||null},n.has=function(t){return null!==this.get(t)},n.set=function(t,e){return this.data[t]=e,this};var i=0;n.uniqueId=function(t){var e=++i+"";return t?t+e:e},n.uniqueArray=function(t){return t.filter((function(t,e,n){return n.indexOf(t)===e}))};var o="";n.uniqid=function(t,e){var n;void 0===t&&(t="");var i=function(t,e){return e<(t=parseInt(t,10).toString(16)).length?t.slice(t.length-e):e>t.length?Array(e-t.length+1).join("0")+t:t};return o||(o=Math.floor(123456789*Math.random())),o++,n=t,n+=i(parseInt((new Date).getTime()/1e3,10),8),n+=i(o,5),e&&(n+=(10*Math.random()).toFixed(8).toString()),n},n.strReplace=function(t,e,n){return n.split(t).join(e)},n.strCamelCase=function(t){var e=t.match(/([a-zA-Z0-9]+)/g);return e?e.map((function(t,e){var n=t.charAt(0);return(0===e?n.toLowerCase():n.toUpperCase())+t.slice(1)})).join(""):""},n.strPascalCase=function(t){var e=n.strCamelCase(t);return e.charAt(0).toUpperCase()+e.slice(1)},n.strSlugify=function(t){return n.strReplace("_","-",t.toLowerCase())},n.strSanitize=function(t,n=!0){var i={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",Æ:"AE",Ç:"C",È:"E",É:"E",Ê:"E",Ë:"E",Ì:"I",Í:"I",Î:"I",Ï:"I",Ð:"D",Ñ:"N",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",Ù:"U",Ú:"U",Û:"U",Ü:"U",Ý:"Y",ß:"s",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",æ:"ae",ç:"c",è:"e",é:"e",ê:"e",ë:"e",ì:"i",í:"i",î:"i",ï:"i",ñ:"n",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",ù:"u",ú:"u",û:"u",ü:"u",ý:"y",ÿ:"y",Ā:"A",ā:"a",Ă:"A",ă:"a",Ą:"A",ą:"a",Ć:"C",ć:"c",Ĉ:"C",ĉ:"c",Ċ:"C",ċ:"c",Č:"C",č:"c",Ď:"D",ď:"d",Đ:"D",đ:"d",Ē:"E",ē:"e",Ĕ:"E",ĕ:"e",Ė:"E",ė:"e",Ę:"E",ę:"e",Ě:"E",ě:"e",Ĝ:"G",ĝ:"g",Ğ:"G",ğ:"g",Ġ:"G",ġ:"g",Ģ:"G",ģ:"g",Ĥ:"H",ĥ:"h",Ħ:"H",ħ:"h",Ĩ:"I",ĩ:"i",Ī:"I",ī:"i",Ĭ:"I",ĭ:"i",Į:"I",į:"i",İ:"I",ı:"i",Ĳ:"IJ",ĳ:"ij",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",Ĺ:"L",ĺ:"l",Ļ:"L",ļ:"l",Ľ:"L",ľ:"l",Ŀ:"L",ŀ:"l",Ł:"l",ł:"l",Ń:"N",ń:"n",Ņ:"N",ņ:"n",Ň:"N",ň:"n",ŉ:"n",Ō:"O",ō:"o",Ŏ:"O",ŏ:"o",Ő:"O",ő:"o",Œ:"OE",œ:"oe",Ŕ:"R",ŕ:"r",Ŗ:"R",ŗ:"r",Ř:"R",ř:"r",Ś:"S",ś:"s",Ŝ:"S",ŝ:"s",Ş:"S",ş:"s",Š:"S",š:"s",Ţ:"T",ţ:"t",Ť:"T",ť:"t",Ŧ:"T",ŧ:"t",Ũ:"U",ũ:"u",Ū:"U",ū:"u",Ŭ:"U",ŭ:"u",Ů:"U",ů:"u",Ű:"U",ű:"u",Ų:"U",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",ź:"z",Ż:"Z",ż:"z",Ž:"Z",ž:"z",ſ:"s",ƒ:"f",Ơ:"O",ơ:"o",Ư:"U",ư:"u",Ǎ:"A",ǎ:"a",Ǐ:"I",ǐ:"i",Ǒ:"O",ǒ:"o",Ǔ:"U",ǔ:"u",Ǖ:"U",ǖ:"u",Ǘ:"U",ǘ:"u",Ǚ:"U",ǚ:"u",Ǜ:"U",ǜ:"u",Ǻ:"A",ǻ:"a",Ǽ:"AE",ǽ:"ae",Ǿ:"O",ǿ:"o"," ":"_","'":"","?":"","/":"","\\":"",".":"",",":"","`":"",">":"","<":"",'"':"","[":"","]":"","|":"","{":"","}":"","(":"",")":""};return t=t.replace(/\W/g,(function(t){return i[t]!==e?i[t]:t})),n&&(t=t.toLowerCase()),t},n.strMatch=function(t,e){for(var n=0,i=Math.min(t.length,e.length),o=0;o<i&&t[o]===e[o];o++)n++;return n},n.strEscape=function(t){var e={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};return(""+t).replace(/[&<>"']/g,(function(t){return e[t]}))},n.strUnescape=function(t){var e={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"};return(""+t).replace(/&amp;|&lt;|&gt;|&quot;|&#39;/g,(function(t){return e[t]}))},n.escAttr=n.strEscape,n.escHtml=function(t){return(""+t).replace(/<script|<\/script/g,(function(t){return n.strEscape(t)}))},n.decode=function(e){return t("<textarea/>").html(e).text()},n.parseArgs=function(e,n){return"object"!=typeof e&&(e={}),"object"!=typeof n&&(n={}),t.extend({},n,e)},window.acfL10n==e&&(acfL10n={}),n.__=function(t){return acfL10n[t]||t},n._x=function(t,e){return acfL10n[t+"."+e]||acfL10n[t]||t},n._n=function(t,e,i){return 1==i?n.__(t):n.__(e)},n.isArray=function(t){return Array.isArray(t)},n.isObject=function(t){return"object"==typeof t};var r=function(t,e,i){var o=(e=e.replace("[]","[%%index%%]")).match(/([^\[\]])+/g);if(o)for(var r=o.length,a=t,s=0;s<r;s++){var c=String(o[s]);s==r-1?"%%index%%"===c?a.push(i):a[c]=i:("%%index%%"===o[s+1]?n.isArray(a[c])||(a[c]=[]):n.isObject(a[c])||(a[c]={}),a=a[c])}};n.serialize=function(t,i){var o={},a=n.serializeArray(t);i!==e&&(a=a.filter((function(t){return 0===t.name.indexOf(i)})).map((function(t){return t.name=t.name.slice(i.length),t})));for(var s=0;s<a.length;s++)r(o,a[s].name,a[s].value);return o},n.serializeArray=function(t){return t.find("select, textarea, input").serializeArray()},n.serializeForAjax=function(t){var e={};return n.serializeArray(t).map((function(t){"[]"===t.name.slice(-2)?(e[t.name]=e[t.name]||[],e[t.name].push(t.value)):e[t.name]=t.value})),e},n.addAction=function(t,e,i,o){return n.hooks.addAction.apply(this,arguments),this},n.removeAction=function(t,e){return n.hooks.removeAction.apply(this,arguments),this};var a={};n.doAction=function(t){return a[t]=1,n.hooks.doAction.apply(this,arguments),a[t]=0,this},n.doingAction=function(t){return 1===a[t]},n.didAction=function(t){return a[t]!==e},n.currentAction=function(){for(var t in a)if(a[t])return t;return!1},n.addFilter=function(t){return n.hooks.addFilter.apply(this,arguments),this},n.removeFilter=function(t){return n.hooks.removeFilter.apply(this,arguments),this},n.applyFilters=function(t){return n.hooks.applyFilters.apply(this,arguments)},n.arrayArgs=function(t){return Array.prototype.slice.call(t)};try{var s=JSON.parse(localStorage.getItem("acf"))||{}}catch(t){s={}}var c=function(t){return"this."===t.substr(0,5)&&(t=t.substr(5)+"-"+n.get("post_id")),t};n.getPreference=function(t){return t=c(t),s[t]||null},n.setPreference=function(t,e){t=c(t),null===e?delete s[t]:s[t]=e,localStorage.setItem("acf",JSON.stringify(s))},n.removePreference=function(t){n.setPreference(t,null)},n.remove=function(t){t instanceof jQuery&&(t={target:t}),t=n.parseArgs(t,{target:!1,endHeight:0,complete:function(){}}),n.doAction("remove",t.target),t.target.is("tr")?u(t):l(t)};var l=function(t){var e=t.target,n=e.height(),i=e.width(),o=e.css("margin"),r=e.outerHeight(!0),a=e.attr("style")+"";e.wrap('<div class="acf-temp-remove" style="height:'+r+'px"></div>');var s=e.parent();e.css({height:n,width:i,margin:o,position:"absolute"}),setTimeout((function(){s.css({opacity:0,height:t.endHeight})}),50),setTimeout((function(){e.attr("style",a),s.remove(),t.complete()}),301)},u=function(e){var n=e.target,i=n.height(),o=n.children().length,r=t('<td class="acf-temp-remove" style="padding:0; height:'+i+'px" colspan="'+o+'"></td>');n.addClass("acf-remove-element"),setTimeout((function(){n.html(r)}),251),setTimeout((function(){n.removeClass("acf-remove-element"),r.css({height:e.endHeight})}),300),setTimeout((function(){n.remove(),e.complete()}),451)};n.duplicate=function(e){e instanceof jQuery&&(e={target:e}),e=n.parseArgs(e,{target:!1,search:"",replace:"",rename:!0,before:function(t){},after:function(t,e){},append:function(t,e){t.after(e)}}),e.target=e.target||e.$el;var i=e.target;e.search=e.search||i.attr("data-id"),e.replace=e.replace||n.uniqid(),e.before(i),n.doAction("before_duplicate",i);var o=i.clone();return e.rename&&n.rename({target:o,search:e.search,replace:e.replace,replacer:"function"==typeof e.rename?e.rename:null}),o.removeClass("acf-clone"),o.find(".ui-sortable").removeClass("ui-sortable"),o.find("[data-select2-id]").removeAttr("data-select2-id"),o.find(".select2").remove(),o.find('.acf-is-subfields select[data-ui="1"]').each((function(){t(this).prop("id",t(this).prop("id").replace("acf_fields",n.uniqid("duplicated_")+"_acf_fields"))})),o.find(".acf-field-settings > .acf-tab-wrap").remove(),e.after(i,o),n.doAction("after_duplicate",i,o),e.append(i,o),n.doAction("duplicate",i,o),n.doAction("append",o),o},n.rename=function(t){t instanceof jQuery&&(t={target:t});var e=(t=n.parseArgs(t,{target:!1,destructive:!1,search:"",replace:"",replacer:null})).target;t.search||(t.search=e.attr("data-id")),t.replace||(t.replace=n.uniqid("acf")),t.replacer||(t.replacer=function(t,e,n,i){return e.replace(n,i)});var i=function(e){return function(n,i){return t.replacer(e,i,t.search,t.replace)}};if(t.destructive){var o=n.strReplace(t.search,t.replace,e.outerHTML());e.replaceWith(o)}else e.attr("data-id",t.replace),e.find('[id*="'+t.search+'"]').attr("id",i("id")),e.find('[for*="'+t.search+'"]').attr("for",i("for")),e.find('[name*="'+t.search+'"]').attr("name",i("name"));return e},n.prepareForAjax=function(t){return void 0===t.nonce&&(t.nonce=n.get("nonce")),t.post_id=n.get("post_id"),n.has("language")&&(t.lang=n.get("language")),n.applyFilters("prepare_for_ajax",t)},n.startButtonLoading=function(t){t.prop("disabled",!0),t.after(' <i class="acf-loading"></i>')},n.stopButtonLoading=function(t){t.prop("disabled",!1),t.next(".acf-loading").remove()},n.showLoading=function(t){t.append('<div class="acf-loading-overlay"><i class="acf-loading"></i></div>')},n.hideLoading=function(t){t.children(".acf-loading-overlay").remove()},n.updateUserSetting=function(e,i){var o={action:"acf/ajax/user_setting",name:e,value:i};t.ajax({url:n.get("ajaxurl"),data:n.prepareForAjax(o),type:"post",dataType:"html"})},n.val=function(t,e,n){var i=t.val();return e!==i&&(t.val(e),t.is("select")&&null===t.val()?(t.val(i),!1):(!0!==n&&t.trigger("change"),!0))},n.show=function(t,e){return e&&n.unlock(t,"hidden",e),!n.isLocked(t,"hidden")&&!!t.hasClass("acf-hidden")&&(t.removeClass("acf-hidden"),!0)},n.hide=function(t,e){return e&&n.lock(t,"hidden",e),!t.hasClass("acf-hidden")&&(t.addClass("acf-hidden"),!0)},n.isHidden=function(t){return t.hasClass("acf-hidden")},n.isVisible=function(t){return!n.isHidden(t)};var f=function(t,e){return!(t.hasClass("acf-disabled")||(e&&n.unlock(t,"disabled",e),n.isLocked(t,"disabled")||!t.prop("disabled")||(t.prop("disabled",!1),0)))};n.enable=function(e,n){if(e.attr("name"))return f(e,n);var i=!1;return e.find("[name]").each((function(){f(t(this),n)&&(i=!0)})),i};var d=function(t,e){return e&&n.lock(t,"disabled",e),!t.prop("disabled")&&(t.prop("disabled",!0),!0)};n.disable=function(e,n){if(e.attr("name"))return d(e,n);var i=!1;return e.find("[name]").each((function(){d(t(this),n)&&(i=!0)})),i},n.isset=function(t){for(var e=1;e<arguments.length;e++){if(!t||!t.hasOwnProperty(arguments[e]))return!1;t=t[arguments[e]]}return!0},n.isget=function(t){for(var e=1;e<arguments.length;e++){if(!t||!t.hasOwnProperty(arguments[e]))return null;t=t[arguments[e]]}return t},n.getFileInputData=function(t,e){var i=t.val();if(!i)return!1;var o={url:i},r=!!t[0].files.length&&n.isget(t[0].files,0);if(r)if(o.size=r.size,o.type=r.type,r.type.indexOf("image")>-1){var a=window.URL||window.webkitURL,s=new Image;s.onload=function(){o.width=this.width,o.height=this.height,e(o)},s.src=a.createObjectURL(r)}else e(o);else e(o)},n.isAjaxSuccess=function(t){return t&&t.success},n.getAjaxMessage=function(t){return n.isget(t,"data","message")},n.getAjaxError=function(t){return n.isget(t,"data","error")},n.getXhrError=function(t){if(t.responseJSON){if(t.responseJSON.message)return t.responseJSON.message;if(t.responseJSON.data&&t.responseJSON.data.error)return t.responseJSON.data.error}else if(t.statusText)return t.statusText;return""},n.renderSelect=function(t,e){var i=t.val(),o=[],r=function(t){var e="";return t.map((function(t){var i=t.text||t.label||"",a=t.id||t.value||"";o.push(a),t.children?e+='<optgroup label="'+n.escAttr(i)+'">'+r(t.children)+"</optgroup>":e+='<option value="'+n.escAttr(a)+'"'+(t.disabled?' disabled="disabled"':"")+">"+n.strEscape(i)+"</option>"})),e};return t.html(r(e)),o.indexOf(i)>-1&&t.val(i),t.val()};var h,p,g,v,m,y=function(t,e){return t.data("acf-lock-"+e)||[]},w=function(t,e,n){t.data("acf-lock-"+e,n)};n.lock=function(t,e,n){var i=y(t,e);i.indexOf(n)<0&&(i.push(n),w(t,e,i))},n.unlock=function(t,e,n){var i=y(t,e),o=i.indexOf(n);return o>-1&&(i.splice(o,1),w(t,e,i)),0===i.length},n.isLocked=function(t,e){return y(t,e).length>0},n.isGutenberg=function(){return!!(window.wp&&wp.data&&wp.data.select&&wp.data.select("core/editor"))},n.isGutenbergPostEditor=function(){return!!(window.wp&&wp.data&&wp.data.select&&wp.data.select("core/edit-post"))},n.objectToArray=function(t){return Object.keys(t).map((function(e){return t[e]}))},n.debounce=function(t,e){var n;return function(){var i=this,o=arguments;clearTimeout(n),n=setTimeout((function(){t.apply(i,o)}),e)}},n.throttle=function(t,e){var n=!1;return function(){n||(n=!0,setTimeout((function(){n=!1}),e),t.apply(this,arguments))}},n.isInView=function(t){t instanceof jQuery&&(t=t[0]);var e=t.getBoundingClientRect();return e.top!==e.bottom&&e.top>=0&&e.left>=0&&e.bottom<=(window.innerHeight||document.documentElement.clientHeight)&&e.right<=(window.innerWidth||document.documentElement.clientWidth)},n.onceInView=(h=[],p=0,g=function(){h.forEach((function(t){n.isInView(t.el)&&(t.callback.apply(this),m(t.id))}))},v=n.debounce(g,300),m=function(e){(h=h.filter((function(t){return t.id!==e}))).length||t(window).off("scroll resize",v).off("acfrefresh orientationchange",g)},function(e,i){e instanceof jQuery&&(e=e[0]),n.isInView(e)?i.apply(this):function(e,n){h.length||t(window).on("scroll resize",v).on("acfrefresh orientationchange",g),h.push({id:p++,el:e,callback:n})}(e,i)}),n.once=function(t){var n=0;return function(){return n++>0?t=e:t.apply(this,arguments)}},n.focusAttention=function(e){var i=1e3;e.addClass("acf-attention -focused"),n.isInView(e)||(t("body, html").animate({scrollTop:e.offset().top-t(window).height()/2},500),i+=500),setTimeout((function(){e.removeClass("-focused"),setTimeout((function(){e.removeClass("acf-attention")}),250)}),i)},n.onFocus=function(e,n){var i=!1,o=!1,r=function(){i=!0,setTimeout((function(){i=!1}),1),s(!0)},a=function(){i||s(!1)},s=function(i){o!==i&&(i?(t(document).on("click",a),e.on("blur","input, select, textarea",a)):(t(document).off("click",a),e.off("blur","input, select, textarea",a)),o=i,n(i))};e.on("click",r),e.on("focus","input, select, textarea",r)},n.disableForm=function(t){t.submitter&&t.submitter.classList.add("disabled")},t.fn.exists=function(){return t(this).length>0},t.fn.outerHTML=function(){return t(this).get(0).outerHTML},Array.prototype.indexOf||(Array.prototype.indexOf=function(e){return t.inArray(e,this)}),n.isNumeric=function(t){return!isNaN(parseFloat(t))&&isFinite(t)},n.refresh=n.debounce((function(){t(window).trigger("acfrefresh"),n.doAction("refresh")}),0),n.debug=function(){n.get("debug")&&console.log.apply(null,arguments)},t(document).ready((function(){n.doAction("ready")})),t(window).on("load",(function(){setTimeout((function(){n.doAction("load")}))})),t(window).on("beforeunload",(function(){n.doAction("unload")})),t(window).on("resize",(function(){n.doAction("resize")})),t(document).on("sortstart",(function(t,e){n.doAction("sortstart",e.item,e.placeholder)})),t(document).on("sortstop",(function(t,e){n.doAction("sortstop",e.item,e.placeholder)}))}(jQuery)}},e={};function n(i){var o=e[i];if(void 0!==o)return o.exports;var r=e[i]={exports:{}};return t[i](r,r.exports,n),r.exports}n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var i in e)n.o(e,i)&&!n.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),(()=>{"use strict";n(6047),n(1018),n(6891),n(4204),n(5751),n(9340),n(2700),n(2177)})()})();