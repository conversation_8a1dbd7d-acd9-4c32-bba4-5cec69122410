{"version": 3, "file": "acf-escaped-html-notice.js", "mappings": ";;;;;AAAA;AACA,CAAE,UAAWA,CAAC,EAAG;EAChB,MAAMC,OAAO,GAAGD,CAAC,CAAE,0BAA2B,CAAC;EAE/CC,OAAO,CAACC,EAAE,CAAE,OAAO,EAAE,wBAAwB,EAAE,UAAWC,CAAC,EAAG;IAC7DA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,MAAMC,KAAK,GAAGL,CAAC,CAAEG,CAAC,CAACG,MAAO,CAAC;IAC3B,MAAMC,QAAQ,GAAGF,KAAK,CACpBG,OAAO,CAAE,0BAA2B,CAAC,CACrCC,IAAI,CAAE,oBAAqB,CAAC;IAE9B,IAAKF,QAAQ,CAACG,EAAE,CAAE,SAAU,CAAC,EAAG;MAC/BH,QAAQ,CAACI,SAAS,CAAE,GAAI,CAAC;MACzBN,KAAK,CAACO,IAAI,CAAEC,uBAAuB,CAACC,YAAa,CAAC;IACnD,CAAC,MAAM;MACNP,QAAQ,CAACQ,OAAO,CAAE,GAAI,CAAC;MACvBV,KAAK,CAACO,IAAI,CAAEC,uBAAuB,CAACG,YAAa,CAAC;IACnD;EACD,CAAE,CAAC;AACJ,CAAC,EAAIC,MAAO,CAAC,C", "sources": ["webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/acf-escaped-html-notice.js"], "sourcesContent": ["/* global, acf_escaped_html_notice */\n( function ( $ ) {\n\tconst $notice = $( '.acf-escaped-html-notice' );\n\n\t$notice.on( 'click', '.acf-show-more-details', function ( e ) {\n\t\te.preventDefault();\n\n\t\tconst $link = $( e.target );\n\t\tconst $details = $link\n\t\t\t.closest( '.acf-escaped-html-notice' )\n\t\t\t.find( '.acf-error-details' );\n\n\t\tif ( $details.is( ':hidden' ) ) {\n\t\t\t$details.slideDown( 100 );\n\t\t\t$link.text( acf_escaped_html_notice.hide_details );\n\t\t} else {\n\t\t\t$details.slideUp( 100 );\n\t\t\t$link.text( acf_escaped_html_notice.show_details );\n\t\t}\n\t} );\n} )( jQuery );\n"], "names": ["$", "$notice", "on", "e", "preventDefault", "$link", "target", "$details", "closest", "find", "is", "slideDown", "text", "acf_escaped_html_notice", "hide_details", "slideUp", "show_details", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}