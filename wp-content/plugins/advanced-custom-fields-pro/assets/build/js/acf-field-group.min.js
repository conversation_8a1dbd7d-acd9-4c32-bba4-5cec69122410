(()=>{var e={7942:()=>{!function(e,t){var i=acf.getCompatibility(acf);i.field_group={save_field:function(e,i){i=i!==t?i:"settings",acf.getFieldObject(e).save(i)},delete_field:function(e,i){i=i===t||i,acf.getFieldObject(e).delete({animate:i})},update_field_meta:function(e,t,i){acf.getFieldObject(e).prop(t,i)},delete_field_meta:function(e,t){acf.getFieldObject(e).prop(t,null)}},i.field_group.field_object=acf.model.extend({type:"",o:{},$field:null,$settings:null,tag:function(e){var t=this.type,i=e.split("_");return i.splice(1,0,"field"),e=i.join("_"),t&&(e+="/type="+t),e},selector:function(){var e=".acf-field-object",t=this.type;return t&&(e+="-"+t,e=acf.str_replace("_","-",e)),e},_add_action:function(e,t){var i=this;acf.add_action(this.tag(e),(function(e){i.set("$field",e),i[t].apply(i,arguments)}))},_add_filter:function(e,t){var i=this;acf.add_filter(this.tag(e),(function(e){i.set("$field",e),i[t].apply(i,arguments)}))},_add_event:function(t,i){var n=this,a=t.substr(0,t.indexOf(" ")),l=t.substr(t.indexOf(" ")+1),s=this.selector();e(document).on(a,s+" "+l,(function(t){t.$el=e(this),t.$field=t.$el.closest(".acf-field-object"),n.set("$field",t.$field),n[i].apply(n,[t])}))},_set_$field:function(){this.o=this.$field.data(),this.$settings=this.$field.find("> .settings > table > tbody"),this.focus()},focus:function(){},setting:function(e){return this.$settings.find("> .acf-field-setting-"+e)}}),new acf.Model({actions:{open_field_object:"onOpenFieldObject",close_field_object:"onCloseFieldObject",add_field_object:"onAddFieldObject",duplicate_field_object:"onDuplicateFieldObject",delete_field_object:"onDeleteFieldObject",change_field_object_type:"onChangeFieldObjectType",change_field_object_label:"onChangeFieldObjectLabel",change_field_object_name:"onChangeFieldObjectName",change_field_object_parent:"onChangeFieldObjectParent",sortstop_field_object:"onChangeFieldObjectParent"},onOpenFieldObject:function(e){acf.doAction("open_field",e.$el),acf.doAction("open_field/type="+e.get("type"),e.$el),acf.doAction("render_field_settings",e.$el),acf.doAction("render_field_settings/type="+e.get("type"),e.$el)},onCloseFieldObject:function(e){acf.doAction("close_field",e.$el),acf.doAction("close_field/type="+e.get("type"),e.$el)},onAddFieldObject:function(e){acf.doAction("add_field",e.$el),acf.doAction("add_field/type="+e.get("type"),e.$el)},onDuplicateFieldObject:function(e){acf.doAction("duplicate_field",e.$el),acf.doAction("duplicate_field/type="+e.get("type"),e.$el)},onDeleteFieldObject:function(e){acf.doAction("delete_field",e.$el),acf.doAction("delete_field/type="+e.get("type"),e.$el)},onChangeFieldObjectType:function(e){acf.doAction("change_field_type",e.$el),acf.doAction("change_field_type/type="+e.get("type"),e.$el),acf.doAction("render_field_settings",e.$el),acf.doAction("render_field_settings/type="+e.get("type"),e.$el)},onChangeFieldObjectLabel:function(e){acf.doAction("change_field_label",e.$el),acf.doAction("change_field_label/type="+e.get("type"),e.$el)},onChangeFieldObjectName:function(e){acf.doAction("change_field_name",e.$el),acf.doAction("change_field_name/type="+e.get("type"),e.$el)},onChangeFieldObjectParent:function(e){acf.doAction("update_field_parent",e.$el)}})}(jQuery)},6298:()=>{var e,t;e=jQuery,t=acf.FieldSetting.extend({type:"",name:"conditional_logic",events:{"change .conditions-toggle":"onChangeToggle","click .add-conditional-group":"onClickAddGroup","focus .condition-rule-field":"onFocusField","change .condition-rule-field":"onChangeField","change .condition-rule-operator":"onChangeOperator","click .add-conditional-rule":"onClickAdd","click .remove-conditional-rule":"onClickRemove"},$rule:!1,scope:function(e){return this.$rule=e,this},ruleData:function(e,t){return this.$rule.data.apply(this.$rule,arguments)},$input:function(e){return this.$rule.find(".condition-rule-"+e)},$td:function(e){return this.$rule.find("td."+e)},$toggle:function(){return this.$(".conditions-toggle")},$control:function(){return this.$(".rule-groups")},$groups:function(){return this.$(".rule-group")},$rules:function(){return this.$(".rule")},$tabLabel:function(){return this.fieldObject.$el.find(".conditional-logic-badge")},$conditionalValueSelect:function(){return this.$(".condition-rule-value")},open:function(){var e=this.$control();e.show(),acf.enable(e)},close:function(){var e=this.$control();e.hide(),acf.disable(e)},render:function(){this.$toggle().prop("checked")?(this.$tabLabel().addClass("is-enabled"),this.renderRules(),this.open()):(this.$tabLabel().removeClass("is-enabled"),this.close())},renderRules:function(){var t=this;this.$rules().each((function(){t.renderRule(e(this))}))},renderRule:function(e){this.scope(e),this.renderField(),this.renderOperator(),this.renderValue()},renderField:function(){var e=[],t=this.fieldObject.cid,i=this.$input("field");acf.getFieldObjects().map((function(i){var n={id:i.getKey(),text:i.getLabel()};i.cid===t&&(n.text+=" "+acf.__("(this field)"),n.disabled=!0),acf.getConditionTypes({fieldType:i.getType()}).length||(n.disabled=!0);var a=i.getParents().length;n.text="- ".repeat(a)+n.text,e.push(n)})),e.length||e.push({id:"",text:acf.__("No toggle fields available")}),acf.renderSelect(i,e),this.ruleData("field",i.val())},renderOperator:function(){if(this.ruleData("field")){var e=this.$input("operator"),t=(e.val(),[]);null===e.val()&&acf.renderSelect(e,[{id:this.ruleData("operator"),text:""}]);var i=acf.findFieldObject(this.ruleData("field")),n=acf.getFieldObject(i);acf.getConditionTypes({fieldType:n.getType()}).map((function(e){t.push({id:e.prototype.operator,text:e.prototype.label})})),acf.renderSelect(e,t),this.ruleData("operator",e.val())}},renderValue:function(){if(!this.ruleData("field")||!this.ruleData("operator"))return;var t=this.$input("value"),i=this.$td("value"),n=t.val(),a=this.$rule[0].getAttribute("data-value"),l=acf.findFieldObject(this.ruleData("field")),s=acf.getFieldObject(l),o=acf.getConditionTypes({fieldType:s.getType(),operator:this.ruleData("operator")})[0].prototype.choices(s);let c;if(o instanceof jQuery&&o.data("acfSelect2Props")){if(c=t.clone(),c.is("input")){var r=t.attr("class");const i=e("<select></select>").addClass(r).val(a);c=i}acf.addAction("acf_conditional_value_rendered",(function(){acf.newSelect2(c,o.data("acfSelect2Props"))}))}else o instanceof Array?(this.$conditionalValueSelect().removeClass("select2-hidden-accessible"),c=e("<select></select>"),acf.renderSelect(c,o)):(this.$conditionalValueSelect().removeClass("select2-hidden-accessible"),c=e(o));t.detach(),i.html(c),setTimeout((function(){["class","name","id"].map((function(e){c.attr(e,t.attr(e))})),t.val(a),acf.doAction("acf_conditional_value_rendered")}),0),c.prop("disabled")||acf.val(c,n,!0),this.ruleData("value",c.val())},onChangeToggle:function(){this.render()},onClickAddGroup:function(e,t){this.addGroup()},addGroup:function(){var e=this.$(".rule-group:last"),t=acf.duplicate(e);t.find("h4").text(acf.__("or")),t.find("tr").not(":first").remove();var i=t.find("tr");this.renderRule(i),this.fieldObject.save()},onFocusField:function(e,t){this.renderField()},onChangeField:function(e,t){this.scope(t.closest(".rule")),this.ruleData("field",t.val()),this.renderOperator(),this.renderValue()},onChangeOperator:function(e,t){this.scope(t.closest(".rule")),this.ruleData("operator",t.val()),this.renderValue()},onClickAdd:function(e,t){var i=acf.duplicate(t.closest(".rule"));this.renderRule(i)},onClickRemove:function(e,t){var i=t.closest(".rule");this.fieldObject.save(),0==i.siblings(".rule").length&&i.closest(".rule-group").remove(),i.remove()}}),acf.registerFieldSetting(t),new acf.Model({actions:{duplicate_field_objects:"onDuplicateFieldObjects"},onDuplicateFieldObjects:function(t,i,n){var a={},l=e();t.map((function(e){a[e.get("prevKey")]=e.get("key"),l=l.add(e.$(".condition-rule-field"))})),l.each((function(){var t=e(this),i=t.val();i&&a[i]&&(t.find("option:selected").attr("value",a[i]),t.val(a[i]))}))}})},4770:()=>{var e;e=jQuery,acf.FieldObject=acf.Model.extend({eventScope:".acf-field-object",fieldTypeSelect2:!1,events:{"click .copyable":"onClickCopy","click .handle":"onClickEdit","click .close-field":"onClickEdit",'click a[data-key="acf_field_settings_tabs"]':"onChangeSettingsTab","click .delete-field":"onClickDelete","click .duplicate-field":"duplicate","click .move-field":"move","click .browse-fields":"browseFields","focus .edit-field":"onFocusEdit","blur .edit-field, .row-options a":"onBlurEdit","change .field-type":"onChangeType","change .field-required":"onChangeRequired","blur .field-label":"onChangeLabel","blur .field-name":"onChangeName",change:"onChange",changed:"onChanged"},data:{id:0,key:"",type:""},setup:function(e){this.$el=e,this.inherit(e),this.prop("ID"),this.prop("parent"),this.prop("menu_order")},$input:function(t){return e("#"+this.getInputId()+"-"+t)},$meta:function(){return this.$(".meta:first")},$handle:function(){return this.$(".handle:first")},$settings:function(){return this.$(".settings:first")},$setting:function(e){return this.$(".acf-field-settings:first .acf-field-setting-"+e)},$fieldTypeSelect:function(){return this.$(".field-type")},$fieldLabel:function(){return this.$(".field-label")},getParent:function(){return acf.getFieldObjects({child:this.$el,limit:1}).pop()},getParents:function(){return acf.getFieldObjects({child:this.$el})},getFields:function(){return acf.getFieldObjects({parent:this.$el})},getInputName:function(){return"acf_fields["+this.get("id")+"]"},getInputId:function(){return"acf_fields-"+this.get("id")},newInput:function(t,i){var n=this.getInputId(),a=this.getInputName();t&&(n+="-"+t,a+="["+t+"]");var l=e("<input />").attr({id:n,name:a,value:i});return this.$("> .meta").append(l),l},getProp:function(e){if(this.has(e))return this.get(e);var t=this.$input(e),i=t.length?t.val():null;return this.set(e,i,!0),i},setProp:function(e,t){var i=this.$input(e);return i.val(),i.length||(i=this.newInput(e,t)),null===t?i.remove():i.val(t),this.has(e)?this.set(e,t):this.set(e,t,!0),this},prop:function(e,t){return void 0!==t?this.setProp(e,t):this.getProp(e)},props:function(e){Object.keys(e).map((function(t){this.setProp(t,e[t])}),this)},getLabel:function(){var e=this.prop("label");return""===e&&(e=acf.__("(no label)")),e},getName:function(){return this.prop("name")},getType:function(){return this.prop("type")},getTypeLabel:function(){var e=this.prop("type"),t=acf.get("fieldTypes");return t[e]?t[e].label:e},getKey:function(){return this.prop("key")},initialize:function(){this.checkCopyable()},makeCopyable:function(e){return navigator.clipboard?'<span class="copyable">'+e+"</span>":'<span class="copyable copy-unsupported">'+e+"</span>"},checkCopyable:function(){navigator.clipboard||this.$el.find(".copyable").addClass("copy-unsupported")},initializeFieldTypeSelect2:function(){if(!this.fieldTypeSelect2&&!this.$fieldTypeSelect().hasClass("disable-select2")){try{e.fn.select2.amd.require("select2/compat/dropdownCss")}catch(e){return void console.warn("ACF was not able to load the full version of select2 due to a conflicting version provided by another plugin or theme taking precedence. Select2 fields may not work as expected.")}this.fieldTypeSelect2=acf.newSelect2(this.$fieldTypeSelect(),{field:!1,ajax:!1,multiple:!1,allowNull:!1,suppressFilters:!0,dropdownCssClass:"field-type-select-results",templateResult:function(t){if(t.loading||t.element&&"OPTGROUP"===t.element.nodeName)(i=e('<span class="acf-selection"></span>')).html(acf.strEscape(t.text));else var i=e('<i class="field-type-icon field-type-icon-'+t.id.replaceAll("_","-")+'"></i><span class="acf-selection has-icon">'+acf.strEscape(t.text)+"</span>");return i.data("element",t.element),i},templateSelection:function(t){var i=e('<i class="field-type-icon field-type-icon-'+t.id.replaceAll("_","-")+'"></i><span class="acf-selection has-icon">'+acf.strEscape(t.text)+"</span>");return i.data("element",t.element),i}}),this.fieldTypeSelect2.on("select2:open",(function(){e(".field-type-select-results input.select2-search__field").attr("placeholder",acf.__("Type to search..."))})),this.fieldTypeSelect2.on("change",(function(t){e(t.target).parents("ul:first").find("button.browse-fields").prop("disabled",!0)})),this.fieldTypeSelect2.$el.parent().on("keydown",".select2-selection.select2-selection--single",this.onKeyDownSelect)}},addProFields:function(){if(acf.get("is_pro")&&acf.get("isLicenseActive"))return;var e=this.$fieldTypeSelect();if(e.hasClass("acf-free-field-type"))return;const t=acf.get("PROFieldTypes");if("object"!=typeof t)return;const i=e.find('optgroup option[value="group"]').parent(),n=e.find('optgroup option[value="image"]').parent();for(const[a,l]of Object.entries(t)){const t="content"===l.category?n:i,s=t.children('[value="'+a+'"]'),o=`${acf.strEscape(l.label)} (${acf.strEscape(acf.__("PRO Only"))})`;s.length?(s.text(o),e.val()!==a&&s.attr("disabled","disabled")):t.append(`<option value="null" disabled="disabled">${o}</option>`)}e.addClass("acf-free-field-type")},render:function(){var e=this.$(".handle:first"),t=this.prop("menu_order"),i=this.getLabel(),n=this.prop("name"),a=this.getTypeLabel(),l=this.prop("key"),s=this.$input("required").prop("checked");e.find(".acf-icon").html(parseInt(t)+1),s&&(i+=' <span class="acf-required">*</span>'),e.find(".li-field-label strong a").html(i),e.find(".li-field-name").html(this.makeCopyable(acf.strSanitize(n)));const o=acf.strSlugify(this.getType());e.find(".field-type-label").text(" "+a),e.find(".field-type-icon").removeClass().addClass("field-type-icon field-type-icon-"+o),e.find(".li-field-key").html(this.makeCopyable(l)),acf.doAction("render_field_object",this)},refresh:function(){acf.doAction("refresh_field_object",this)},isOpen:function(){return this.$el.hasClass("open")},onClickCopy:function(t){if(t.stopPropagation(),!navigator.clipboard||e(t.target).is("input"))return;let i;i=e(t.target).hasClass("acf-input-wrap")?e(t.target).find("input").first().val():e(t.target).text().trim(),navigator.clipboard.writeText(i).then((()=>{e(t.target).closest(".copyable").addClass("copied"),setTimeout((function(){e(t.target).closest(".copyable").removeClass("copied")}),2e3)}))},onClickEdit:function(t){const i=e(t.target);acf.get("is_pro")&&!acf.get("isLicenseActive")&&!acf.get("isLicenseExpired")&&acf.get("PROFieldTypes").hasOwnProperty(this.getType())||i.parent().hasClass("row-options")&&!i.hasClass("edit-field")||(this.isOpen()?this.close():this.open())},onChangeSettingsTab:function(){const e=this.$el.children(".settings");acf.doAction("show",e)},onFocusEdit:function(t){e(t.target).closest("li").find(".row-options").addClass("active")},onBlurEdit:function(t){var i=e(t.target).closest("li").find(".row-options");setTimeout((function(){var t=e(document.activeElement).closest("li").find(".row-options");i.is(t)||i.removeClass("active")}),50)},open:function(){var e=this.$el.children(".settings");this.addProFields(),this.initializeFieldTypeSelect2(),acf.doAction("open_field_object",this),this.trigger("openFieldObject"),acf.doAction("show",e),this.hideEmptyTabs(),e.slideDown(),this.$el.addClass("open")},onKeyDownSelect:function(t){t.which>=186&&t.which<=222||[8,9,13,16,17,18,19,20,27,32,33,34,35,36,37,38,39,40,45,46,91,92,93,144,145].includes(t.which)||t.which>=112&&t.which<=123||e(this).closest(".select2-container").siblings("select:enabled").select2("open")},close:function(){var e=this.$el.children(".settings");e.slideUp(),this.$el.removeClass("open"),acf.doAction("close_field_object",this),this.trigger("closeFieldObject"),acf.doAction("hide",e)},serialize:function(){return acf.serialize(this.$el,this.getInputName())},save:function(e){e=e||"settings","settings"!==this.getProp("save")&&(this.setProp("save",e),this.$el.attr("data-save",e),acf.doAction("save_field_object",this,e))},submit:function(){var e=this.getInputName(),t=this.get("save");this.isOpen()&&this.close(),"settings"==t||("meta"==t?this.$('> .settings [name^="'+e+'"]').remove():this.$('[name^="'+e+'"]').remove()),acf.doAction("submit_field_object",this)},onChange:function(e,t){this.save(),acf.doAction("change_field_object",this)},onChanged:function(t,i,n,a){this.getType()===i.attr("data-type")&&e("button.acf-btn.browse-fields").prop("disabled",!1),"save"!=n&&(["menu_order","parent"].indexOf(n)>-1?this.save("meta"):this.save(),["menu_order","label","required","name","type","key"].indexOf(n)>-1&&this.render(),acf.doAction("change_field_object_"+n,this,a))},onChangeLabel:function(e,t){var i=t.val();if(this.set("label",i),""==this.prop("name")){var n=acf.applyFilters("generate_field_object_name",acf.strSanitize(i),this);this.prop("name",n)}},onChangeName:function(e,t){const i=acf.strSanitize(t.val(),!1);t.val(i),this.set("name",i),i.startsWith("field_")&&alert(acf.__('The string "field_" may not be used at the start of a field name'))},onChangeRequired:function(e,t){var i=t.prop("checked")?1:0;this.set("required",i)},delete:function(t){t=acf.parseArgs(t,{animate:!0});var i=this.prop("ID");if(i){var n=e("#_acf_delete_fields"),a=n.val()+"|"+i;n.val(a)}acf.doAction("delete_field_object",this),t.animate?this.removeAnimate():this.remove()},onClickDelete:function(e,t){if(e.shiftKey)return this.delete();this.$el.addClass("-hover"),acf.newTooltip({confirmRemove:!0,target:t,context:this,confirm:function(){this.delete()},cancel:function(){this.$el.removeClass("-hover")}})},removeAnimate:function(){var e=this,t=this.$el.parent(),i=acf.findFieldObjects({sibling:this.$el});acf.remove({target:this.$el,endHeight:i.length?0:50,complete:function(){e.remove(),acf.doAction("removed_field_object",e,t)}}),acf.doAction("remove_field_object",e,t)},duplicate:function(){var e=acf.uniqid("field_"),t=acf.duplicate({target:this.$el,search:this.get("id"),replace:e});t.attr("data-key",e);var i=acf.getFieldObject(t),n=i.prop("label"),a=i.prop("name"),l=a.split("_").pop(),s=acf.__("copy");if(acf.isNumeric(l)){var o=1*l+1;n=n.replace(l,o),a=a.replace(l,o)}else 0===l.indexOf(s)?(o=(o=1*l.replace(s,""))?o+1:2,n=n.replace(l,s+o),a=a.replace(l,s+o)):(n+=" ("+s+")",a+="_"+s);i.prop("ID",0),i.prop("label",n),i.prop("name",a),i.prop("key",e),this.isOpen()&&this.close(),i.open();var c=i.$setting("label input");setTimeout((function(){c.trigger("focus")}),251),acf.doAction("duplicate_field_object",this,i),acf.doAction("append_field_object",i)},wipe:function(){var e=this.get("id"),t=this.get("key"),i=acf.uniqid("field_");acf.rename({target:this.$el,search:e,replace:i}),this.set("id",i),this.set("prevId",e),this.set("prevKey",t),this.prop("key",i),this.prop("ID",0),this.$el.attr("data-key",i),this.$el.attr("data-id",i),acf.doAction("wipe_field_object",this)},move:function(){var t=function(e){return"settings"==e.get("save")},i=t(this);if(i||acf.getFieldObjects({parent:this.$el}).map((function(e){i=t(e)||e.changed})),i)alert(acf.__("This field cannot be moved until its changes have been saved"));else{var n=this.prop("ID"),a=this,l=!1,s=function(e){l.loading(!1),l.content(e),l.on("submit","form",o)},o=function(t,i){t.preventDefault(),acf.startButtonLoading(l.$(".button"));var a={action:"acf/field_group/move_field",field_id:n,field_group_id:l.$("select").val()};e.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(a),type:"post",dataType:"html",success:c})},c=function(e){l.content(e),wp.a11y&&wp.a11y.speak&&acf.__&&wp.a11y.speak(acf.__("Field moved to other group"),"polite"),l.$(".acf-close-popup").focus(),a.removeAnimate()};!function(){l=acf.newPopup({title:acf.__("Move Custom Field"),loading:!0,width:"300px",openedBy:a.$el.find(".move-field")});var t={action:"acf/field_group/move_field",field_id:n};e.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(t),type:"post",dataType:"html",success:s})}()}},browseFields:function(e,t){e.preventDefault(),acf.newBrowseFieldsModal({openedBy:this})},onChangeType:function(e,t){this.changeTimeout&&clearTimeout(this.changeTimeout),this.changeTimeout=this.setTimeout((function(){this.changeType(t.val())}),300)},changeType:function(t){var i=this.prop("type"),n=acf.strSlugify("acf-field-object-"+i),a=acf.strSlugify("acf-field-object-"+t);this.$el.removeClass(n).addClass(a),this.$el.attr("data-type",t),this.$el.data("type",t),this.has("xhr")&&this.get("xhr").abort();const l={};if(this.$el.find(".acf-field-settings:first > .acf-field-settings-main > .acf-field-type-settings").each((function(){let t=e(this).data("parent-tab"),i=e(this).children().removeData();l[t]=i,i.detach()})),this.set("settings-"+i,l),this.has("settings-"+t)){let e=this.get("settings-"+t);return this.showFieldTypeSettings(e),void this.set("type",t)}const s=e('<div class="acf-field"><div class="acf-input"><div class="acf-loading"></div></div></div>');this.$el.find(".acf-field-settings-main-general .acf-field-type-settings").before(s);const o={action:"acf/field_group/render_field_settings",field:this.serialize(),prefix:this.getInputName()};var c=e.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(o),type:"post",dataType:"json",context:this,success:function(e){acf.isAjaxSuccess(e)&&this.showFieldTypeSettings(e.data)},complete:function(){s.remove(),this.set("type",t)}});this.set("xhr",c)},showFieldTypeSettings:function(e){if("object"!=typeof e)return;const t=this;Object.keys(e).forEach((i=>{const n=t.$el.find(".acf-field-settings-main-"+i.replace("_","-")+" .acf-field-type-settings");let a="";["object","string"].includes(typeof e[i])&&(a=e[i]),n.prepend(a),acf.doAction("append",n)})),this.hideEmptyTabs()},updateParent:function(){var e=acf.get("post_id"),t=this.getParent();t&&(e=parseInt(t.prop("ID"))||t.prop("key")),this.prop("parent",e)},hideEmptyTabs:function(){const t=this.$settings();t.find(".acf-field-settings:first > .acf-field-settings-main").each((function(){const i=e(this),n=i.find(".acf-field-type-settings:first").data("parentTab"),a=t.find(".acf-settings-type-"+n).first();""===e.trim(i.text())?a.hide():a.is(":hidden")&&a.show()}))}})},7297:()=>{var e;e=jQuery,acf.findFieldObject=function(e){return acf.findFieldObjects({key:e,limit:1})},acf.findFieldObjects=function(t){t=t||{};var i=".acf-field-object",n=!1;return(t=acf.parseArgs(t,{id:"",key:"",type:"",limit:!1,list:null,parent:!1,sibling:!1,child:!1})).id&&(i+='[data-id="'+t.id+'"]'),t.key&&(i+='[data-key="'+t.key+'"]'),t.type&&(i+='[data-type="'+t.type+'"]'),n=t.list?t.list.children(i):t.parent?t.parent.find(i):t.sibling?t.sibling.siblings(i):t.child?t.child.parents(i):e(i),t.limit&&(n=n.slice(0,t.limit)),n},acf.getFieldObject=function(e){"string"==typeof e&&(e=acf.findFieldObject(e));var t=e.data("acf");return t||(t=acf.newFieldObject(e)),t},acf.getFieldObjects=function(t){var i=acf.findFieldObjects(t),n=[];return i.each((function(){var t=acf.getFieldObject(e(this));n.push(t)})),n},acf.newFieldObject=function(e){var t=new acf.FieldObject(e);return acf.doAction("new_field_object",t),t},new acf.Model({priority:5,initialize:function(){["prepare","ready","append","remove"].map((function(e){this.addFieldActions(e)}),this)},addFieldActions:function(e){var t=e+"_field_objects",i=e+"_field_object",n=e+"FieldObject";acf.addAction(e,(function(e){var i=acf.getFieldObjects({parent:e});if(i.length){var n=acf.arrayArgs(arguments);n.splice(0,1,t,i),acf.doAction.apply(null,n)}}),5),acf.addAction(t,(function(e){var t=acf.arrayArgs(arguments);t.unshift(i),e.map((function(e){t[1]=e,acf.doAction.apply(null,t)}))}),5),acf.addAction(i,(function(e){var t=acf.arrayArgs(arguments);t.unshift(i),["type","name","key"].map((function(n){t[0]=i+"/"+n+"="+e.get(n),acf.doAction.apply(null,t)})),t.splice(0,2),e.trigger(n,t)}),5)}}),new acf.Model({id:"fieldManager",events:{"submit #post":"onSubmit","mouseenter .acf-field-list":"onHoverSortable","click .add-field":"onClickAdd"},actions:{removed_field_object:"onRemovedField",sortstop_field_object:"onReorderField",delete_field_object:"onDeleteField",change_field_object_type:"onChangeFieldType",duplicate_field_object:"onDuplicateField"},onSubmit:function(e,t){acf.getFieldObjects().map((function(e){e.submit()}))},setFieldMenuOrder:function(e){this.renderFields(e.$el.parent())},onHoverSortable:function(e,t){t.hasClass("ui-sortable")||t.sortable({helper:function(e,t){return t.clone().find(":input").attr("name",(function(e,t){return"sort_"+parseInt(1e5*Math.random(),10).toString()+"_"+t})).end()},handle:".acf-sortable-handle",connectWith:".acf-field-list",start:function(e,i){var n=acf.getFieldObject(i.item);i.placeholder.height(i.item.height()),acf.doAction("sortstart_field_object",n,t)},update:function(e,i){var n=acf.getFieldObject(i.item);acf.doAction("sortstop_field_object",n,t)}})},onRemovedField:function(e,t){this.renderFields(t)},onReorderField:function(e,t){e.updateParent(),this.renderFields(t)},onDeleteField:function(e){e.getFields().map((function(e){e.delete({animate:!1})}))},onChangeFieldType:function(e){e.$el.find("button.browse-fields").prop("disabled",!1)},onDuplicateField:function(e,t){var i=t.getFields();i.length&&(i.map((function(e){e.wipe(),e.isOpen()&&e.open(),e.updateParent()})),acf.doAction("duplicate_field_objects",i,t,e)),this.setFieldMenuOrder(t)},renderFields:function(e){var t=acf.getFieldObjects({list:e});if(!t.length)return e.addClass("-empty"),void e.parents(".acf-field-list-wrap").first().addClass("-empty");e.removeClass("-empty"),e.parents(".acf-field-list-wrap").first().removeClass("-empty"),t.map((function(e,t){e.prop("menu_order",t)}))},onClickAdd:function(t,i){let n;n=i.hasClass("add-first-field")?i.parents(".acf-field-list").eq(0):i.parent().hasClass("acf-headerbar-actions")||i.parent().hasClass("no-fields-message-inner")?e(".acf-field-list:first"):i.parent().hasClass("acf-sub-field-list-header")?i.parents(".acf-input:first").find(".acf-field-list:first"):i.closest(".acf-tfoot").siblings(".acf-field-list"),this.addField(n)},addField:function(t){var i=e("#tmpl-acf-field").html(),n=e(i),a=n.data("id"),l=acf.uniqid("field_"),s=acf.duplicate({target:n,search:a,replace:l,append:function(e,i){t.append(i)}}),o=acf.getFieldObject(s);o.prop("key",l),o.prop("ID",0),o.prop("label",""),o.prop("name",""),s.attr("data-key",l),s.attr("data-id",l),o.updateParent();var c=o.$input("type");setTimeout((function(){t.hasClass("acf-auto-add-field")?t.removeClass("acf-auto-add-field"):c.trigger("focus")}),251),o.open(),this.renderFields(t),acf.doAction("add_field_object",o),acf.doAction("append_field_object",o)}})},2522:()=>{var e;e=jQuery,new acf.Model({id:"locationManager",wait:"ready",events:{"click .add-location-rule":"onClickAddRule","click .add-location-group":"onClickAddGroup","click .remove-location-rule":"onClickRemoveRule","change .refresh-location-rule":"onChangeRemoveRule"},initialize:function(){this.$el=e("#acf-field-group-options"),this.addProLocations(),this.updateGroupsClass()},addProLocations:function(){if(acf.get("is_pro")&&acf.get("isLicenseActive"))return;const e=acf.get("PROLocationTypes");if("object"!=typeof e)return;const t=this.$el.find("select.refresh-location-rule").find('optgroup[label="Forms"]'),i=` (${acf.__("PRO Only")})`;for(const[n,a]of Object.entries(e))acf.get("is_pro")?t.find("option[value="+n+"]").not(":selected").prop("disabled","disabled").text(`${acf.strEscape(a)}${acf.strEscape(i)}`):t.append(`<option value="null" disabled="disabled">${acf.strEscape(a)}${acf.strEscape(i)}</option>`);const n=this.$el.find("select.location-rule-value option[value=add_new_options_page]");n.length&&n.attr("disabled","disabled")},onClickAddRule:function(e,t){this.addRule(t.closest("tr"))},onClickRemoveRule:function(e,t){this.removeRule(t.closest("tr"))},onChangeRemoveRule:function(e,t){this.changeRule(t.closest("tr"))},onClickAddGroup:function(e,t){this.addGroup()},addRule:function(e){acf.duplicate(e),this.updateGroupsClass()},removeRule:function(e){0==e.siblings("tr").length?e.closest(".rule-group").remove():e.remove(),this.$(".rule-group:first").find("h4").text(acf.__("Show this field group if")),this.updateGroupsClass()},changeRule:function(t){var i=t.closest(".rule-group"),n=t.find("td.param select").attr("name").replace("[param]",""),a={action:"acf/field_group/render_location_rule"};a.rule=acf.serialize(t,n),a.rule.id=t.data("id"),a.rule.group=i.data("id"),acf.disable(t.find("td.value"));const l=this;e.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(a),type:"post",dataType:"html",success:function(e){e&&(t.replaceWith(e),l.addProLocations())}})},addGroup:function(){var e=this.$(".rule-group:last");$group2=acf.duplicate(e),$group2.find("h4").text(acf.__("or")),$group2.find("tr").not(":first").remove(),this.updateGroupsClass()},updateGroupsClass:function(){var e=this.$(".rule-group:last").closest(".rule-groups");e.find(".acf-table tr").length>1?e.addClass("rule-groups-multiple"):e.removeClass("rule-groups-multiple")}})},749:()=>{!function(e,t){var i=function(e){return acf.strPascalCase(e||"")+"FieldSetting"};acf.registerFieldSetting=function(e){var t=e.prototype,n=i(t.type+" "+t.name);this.models[n]=e},acf.newFieldSetting=function(e){var t=e.get("setting")||"",n=e.get("name")||"",a=i(t+" "+n),l=acf.models[a]||null;return null!==l&&new l(e)},acf.getFieldSetting=function(e){return e instanceof jQuery&&(e=acf.getField(e)),e.setting},new acf.Model({actions:{new_field:"onNewField"},onNewField:function(e){e.setting=acf.newFieldSetting(e)}}),acf.FieldSetting=acf.Model.extend({field:!1,type:"",name:"",wait:"ready",eventScope:".acf-field",events:{change:"render"},setup:function(t){var i=t.$el;this.$el=i,this.field=t,this.$fieldObject=i.closest(".acf-field-object"),this.fieldObject=acf.getFieldObject(this.$fieldObject),e.extend(this.data,t.data)},initialize:function(){this.render()},render:function(){}});var n=acf.FieldSetting.extend({type:"",name:"",render:function(){this.fieldObject.$setting("endpoint").find('input[type="checkbox"]:first').is(":checked")?this.fieldObject.$el.addClass("acf-field-is-endpoint"):this.fieldObject.$el.removeClass("acf-field-is-endpoint")}}),a=n.extend({type:"accordion",name:"endpoint"}),l=n.extend({type:"tab",name:"endpoint"});acf.registerFieldSetting(a),acf.registerFieldSetting(l);var s=acf.FieldSetting.extend({type:"",name:"",render:function(){var e=this.$('input[type="radio"]:checked');"other"!=e.val()&&this.$('input[type="text"]').val(e.val())}}),o=s.extend({type:"date_picker",name:"display_format"}),c=s.extend({type:"date_picker",name:"return_format"});acf.registerFieldSetting(o),acf.registerFieldSetting(c);var r=s.extend({type:"date_time_picker",name:"display_format"}),d=s.extend({type:"date_time_picker",name:"return_format"});acf.registerFieldSetting(r),acf.registerFieldSetting(d);var f=s.extend({type:"time_picker",name:"display_format"}),p=s.extend({type:"time_picker",name:"return_format"});acf.registerFieldSetting(f),acf.registerFieldSetting(p);var u=acf.FieldSetting.extend({type:"color_picker",name:"enable_opacity",render:function(){var e=this.fieldObject.$setting("return_format"),t=this.fieldObject.$setting("default_value"),i=e.find('input[type="radio"][value="string"]').parent("label").contents().last(),n=t.find('input[type="text"]'),a=acf.get("colorPickerL10n");this.field.val()?(i.replaceWith(a.rgba_string),n.attr("placeholder","rgba(255,255,255,0.8)")):(i.replaceWith(a.hex_string),n.attr("placeholder","#FFFFFF"))}});acf.registerFieldSetting(u)}(jQuery)},3319:()=>{var e;e=jQuery,new acf.Model({id:"fieldGroupManager",events:{"submit #post":"onSubmit",'click a[href="#"]':"onClick","click .acf-delete-field-group":"onClickDeleteFieldGroup","blur input#title":"validateTitle","input input#title":"validateTitle"},filters:{find_fields_args:"filterFindFieldArgs",find_fields_selector:"filterFindFieldsSelector"},initialize:function(){acf.addAction("prepare",this.maybeInitNewFieldGroup),acf.add_filter("select2_args",this.setBidirectionalSelect2Args),acf.add_filter("select2_ajax_data",this.setBidirectionalSelect2AjaxDataArgs)},setBidirectionalSelect2Args:function(t,i,n,a,l){var s;if("bidirectional_target"!==(null==a||null===(s=a.data)||void 0===s?void 0:s.call(a,"key")))return t;t.dropdownCssClass="field-type-select-results";try{e.fn.select2.amd.require("select2/compat/dropdownCss")}catch(e){console.warn("ACF was not able to load the full version of select2 due to a conflicting version provided by another plugin or theme taking precedence. Skipping styling of bidirectional settings."),delete t.dropdownCssClass}return t.templateResult=function(t){if(void 0!==t.element)return t;if(t.children)return t.text;if(t.loading||t.element&&"OPTGROUP"===t.element.nodeName)return(i=e('<span class="acf-selection"></span>')).html(acf.escHtml(t.text)),i;if(void 0===t.human_field_type||void 0===t.field_type||void 0===t.this_field)return t.text;var i=e('<i title="'+acf.escHtml(t.human_field_type)+'" class="field-type-icon field-type-icon-'+acf.escHtml(t.field_type.replaceAll("_","-"))+'"></i><span class="acf-selection has-icon">'+acf.escHtml(t.text)+"</span>");return t.this_field&&i.last().append('<span class="acf-select2-default-pill">'+acf.__("This Field")+"</span>"),i.data("element",t.element),i},t},setBidirectionalSelect2AjaxDataArgs:function(e,t,i,n,a){if("bidirectional_target"!==e.field_key)return e;const l=acf.findFieldObjects({child:n}),s=acf.getFieldObject(l);return e.field_key="_acf_bidirectional_target",e.parent_key=s.get("key"),e.field_type=s.get("type"),e.post_type=acf.getField(acf.findFields({parent:l,key:"post_type"})).val(),e},maybeInitNewFieldGroup:function(){e("#acf-field-group-fields > .inside > .acf-field-list-wrap.acf-auto-add-field").length&&(e(".acf-headerbar-actions .add-field").trigger("click"),e(".acf-title-wrap #title").trigger("focus"))},onSubmit:function(t,i){var n=e(".acf-title-wrap #title");n.val()||(t.preventDefault(),acf.unlockForm(i),n.trigger("focus"))},onClick:function(e){e.preventDefault()},onClickDeleteFieldGroup:function(e,t){e.preventDefault(),t.addClass("-hover"),acf.newTooltip({confirm:!0,target:t,context:this,text:acf.__("Move field group to trash?"),confirm:function(){window.location.href=t.attr("href")},cancel:function(){t.removeClass("-hover")}})},validateTitle:function(t,i){let n=e(".acf-publish");i.val()?(i.removeClass("acf-input-error"),n.removeClass("disabled"),e(".acf-publish").removeClass("disabled")):(i.addClass("acf-input-error"),n.addClass("disabled"),e(".acf-publish").addClass("disabled"))},filterFindFieldArgs:function(e){return e.visible=!0,e.parent&&(e.parent.hasClass("acf-field-object")||e.parent.hasClass("acf-browse-fields-modal-wrap")||e.parent.parents(".acf-field-object").length)&&(e.visible=!1,e.excludeSubFields=!0),e.parent&&e.parent.find(".acf-field-object.open").length&&(e.excludeSubFields=!1),e},filterFindFieldsSelector:function(e){return e+", .acf-field-acf-field-group-settings-tabs"}}),new acf.Model({id:"screenOptionsManager",wait:"prepare",events:{"change #acf-field-key-hide":"onFieldKeysChange","change #acf-field-settings-tabs":"onFieldSettingsTabsChange",'change [name="screen_columns"]':"render"},initialize:function(){var t=e("#adv-settings"),i=e("#acf-append-show-on-screen");t.find(".metabox-prefs").append(i.html()),t.find(".metabox-prefs br").remove(),i.remove(),this.$el=e("#screen-options-wrap"),this.render()},isFieldKeysChecked:function(){return this.$el.find("#acf-field-key-hide").prop("checked")},isFieldSettingsTabsChecked:function(){const e=this.$el.find("#acf-field-settings-tabs");return!!e.length&&e.prop("checked")},getSelectedColumnCount:function(){return this.$el.find('input[name="screen_columns"]:checked').val()},onFieldKeysChange:function(e,t){var i=this.isFieldKeysChecked()?1:0;acf.updateUserSetting("show_field_keys",i),this.render()},onFieldSettingsTabsChange:function(){const e=this.isFieldSettingsTabsChecked()?1:0;acf.updateUserSetting("show_field_settings_tabs",e),this.render()},render:function(){this.isFieldKeysChecked()?e("#acf-field-group-fields").addClass("show-field-keys"):e("#acf-field-group-fields").removeClass("show-field-keys"),this.isFieldSettingsTabsChecked()?(e("#acf-field-group-fields").removeClass("hide-tabs"),e(".acf-field-object").each((function(){const t=acf.getFields({type:"tab",parent:e(this),excludeSubFields:!0,limit:1});t.length&&t[0].tabs.set("initialized",!1),acf.doAction("show",e(this))}))):(e("#acf-field-group-fields").addClass("hide-tabs"),e(".acf-field-settings-main").removeClass("acf-hidden").prop("hidden",!1)),1==this.getSelectedColumnCount()?(e("body").removeClass("columns-2"),e("body").addClass("columns-1")):(e("body").removeClass("columns-1"),e("body").addClass("columns-2"))}}),new acf.Model({actions:{new_field:"onNewField"},onNewField:function(t){if(t.has("append")){var i=t.get("append"),n=t.$el.siblings('[data-name="'+i+'"]').first();if(n.length){var a=n.children(".acf-input"),l=a.children("ul");l.length||(a.wrapInner('<ul class="acf-hl"><li></li></ul>'),l=a.children("ul"));var s=t.$(".acf-input").html(),o=e("<li>"+s+"</li>");l.append(o),l.attr("data-cols",l.children().length),t.remove()}}}})}},t={};function i(n){var a=t[n];if(void 0!==a)return a.exports;var l=t[n]={exports:{}};return e[n](l,l.exports,i),l.exports}(()=>{"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(t,i,n){return(i=function(t){var i=function(t,i){if("object"!=e(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var a=n.call(t,"string");if("object"!=e(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==e(i)?i:i+""}(i))in t?Object.defineProperty(t,i,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[i]=n,t}function n(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function a(e){for(var i=1;i<arguments.length;i++){var a=null!=arguments[i]?arguments[i]:{};i%2?n(Object(a),!0).forEach((function(i){t(e,i,a[i])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):n(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}i(3319),i(4770),i(749),i(6298),i(7297),i(2522),i(7942),function(e,t,i){const n={data:{openedBy:null,currentFieldType:null,popularFieldTypes:["text","textarea","email","url","file","gallery","select","true_false","link","post_object","relationship","repeater","flexible_content","clone"]},events:{"click .acf-modal-close":"onClickClose","keydown .acf-browse-fields-modal":"onPressEscapeClose","click .acf-select-field":"onClickSelectField","click .acf-field-type":"onClickFieldType","changed:currentFieldType":"onChangeFieldType","input .acf-search-field-types":"onSearchFieldTypes","click .acf-browse-popular-fields":"onClickBrowsePopular"},setup:function(t){e.extend(this.data,t),this.$el=e(this.tmpl()),this.render()},initialize:function(){this.open(),this.lockFocusToModal(!0),this.$el.find(".acf-modal-title").focus(),i.doAction("show",this.$el)},tmpl:function(){return e("#tmpl-acf-browse-fields-modal").html()},getFieldTypes:function(e,t){let n;if(n=i.get("is_pro")?Object.values(i.get("fieldTypes")):Object.values(a(a({},i.get("fieldTypes")),i.get("PROFieldTypes"))),e){if("popular"===e)return n.filter((e=>this.get("popularFieldTypes").includes(e.name)));if("pro"===e)return n.filter((e=>e.pro));n=n.filter((t=>t.category===e))}return t&&(n=n.filter((e=>{const i=e.label.toLowerCase(),n=i.split(" ");let a=!1;return i.startsWith(t.toLowerCase())?a=!0:n.length>1&&n.forEach((e=>{e.startsWith(t.toLowerCase())&&(a=!0)})),a}))),n},render:function(){i.doAction("append",this.$el);const t=this.$el.find(".acf-field-types-tab"),n=this;t.each((function(){const t=e(this).data("category");n.getFieldTypes(t).forEach((t=>{e(this).append(n.getFieldTypeHTML(t))}))})),this.initializeFieldLabel(),this.initializeFieldType(),this.onChangeFieldType()},getFieldTypeHTML:function(e){const t=e.name.replaceAll("_","-");return`\n\t\t\t<a href="#" class="acf-field-type" data-field-type="${e.name}">\n\t\t\t\t${e.pro&&!i.get("is_pro")?'<span class="field-type-requires-pro not-pro"></span>':e.pro?'<span class="field-type-requires-pro"></span>':""}\n\t\t\t\t<i class="field-type-icon field-type-icon-${t}"></i>\n\t\t\t\t<span class="field-type-label">${e.label}</span>\n\t\t\t</a>\n\t\t\t`},decodeFieldTypeURL:function(e){return"string"!=typeof e?e:e.replaceAll("&#038;","&")},renderFieldTypeDesc:function(e){const t=this.getFieldTypes().filter((t=>t.name===e))[0]||{},n=i.parseArgs(t,{label:"",description:"",doc_url:!1,tutorial_url:!1,preview_image:!1,pro:!1});this.$el.find(".field-type-name").text(n.label),this.$el.find(".field-type-desc").text(n.description),n.doc_url?this.$el.find(".field-type-doc").attr("href",this.decodeFieldTypeURL(n.doc_url)).show():this.$el.find(".field-type-doc").hide(),n.tutorial_url?this.$el.find(".field-type-tutorial").attr("href",this.decodeFieldTypeURL(n.tutorial_url)).parent().show():this.$el.find(".field-type-tutorial").parent().hide(),n.preview_image?this.$el.find(".field-type-image").attr("src",n.preview_image).show():this.$el.find(".field-type-image").hide();const a=i.get("is_pro"),l=i.get("isLicenseActive"),s=this.$el.find(".acf-btn-pro"),o=this.$el.find(".field-type-upgrade-to-unlock");!n.pro||a&&l?(s.hide(),o.hide(),this.$el.find(".acf-insert-field-label").attr("disabled",!1),this.$el.find(".acf-select-field").show()):(s.show(),s.attr("href",s.data("urlBase")+e),o.show(),o.attr("href",o.data("urlBase")+e),this.$el.find(".acf-insert-field-label").attr("disabled",!0),this.$el.find(".acf-select-field").hide())},initializeFieldType:function(){var t;const i=this.get("openedBy"),n=null==i||null===(t=i.data)||void 0===t?void 0:t.type;n?this.set("currentFieldType",n):this.set("currentFieldType","text");const a=this.getFieldTypes();let l="";l=this.get("popularFieldTypes").includes(n)?"popular":a.find((e=>e.name===n)).category;const s=`.acf-modal-content .acf-tab-wrap a:contains('${l[0].toUpperCase()+l.slice(1)}')`;setTimeout((()=>{e(s).click()}),0)},initializeFieldLabel:function(){const e=this.get("openedBy").$fieldLabel().val(),t=this.$el.find(".acf-insert-field-label");e?t.val(e):t.val("")},updateFieldObjectFieldLabel:function(){const e=this.$el.find(".acf-insert-field-label").val(),t=this.get("openedBy");t.$fieldLabel().val(e),t.$fieldLabel().trigger("blur")},onChangeFieldType:function(){const e=this.get("currentFieldType");this.$el.find(".selected").removeClass("selected"),this.$el.find('.acf-field-type[data-field-type="'+e+'"]').addClass("selected"),this.renderFieldTypeDesc(e)},onSearchFieldTypes:function(t){const i=this.$el.find(".acf-browse-fields-modal"),n=this.$el.find(".acf-search-field-types").val(),a=this;let l,s="",o=[];if("string"==typeof n&&(l=n.trim(),o=this.getFieldTypes(!1,l)),l.length&&o.length?i.addClass("is-searching"):i.removeClass("is-searching"),!o.length)return i.addClass("no-results-found"),void this.$el.find(".acf-invalid-search-term").text(l);i.removeClass("no-results-found"),o.forEach((e=>{s+=a.getFieldTypeHTML(e)})),e(".acf-field-type-search-results").html(s),this.set("currentFieldType",o[0].name),this.onChangeFieldType()},onClickBrowsePopular:function(){this.$el.find(".acf-search-field-types").val("").trigger("input"),this.$el.find(".acf-tab-wrap a").first().trigger("click")},onClickSelectField:function(e){const t=this.get("openedBy");t.$fieldTypeSelect().val(this.get("currentFieldType")),t.$fieldTypeSelect().trigger("change"),this.updateFieldObjectFieldLabel(),this.close()},onClickFieldType:function(t){const i=e(t.currentTarget);this.set("currentFieldType",i.data("field-type"))},onClickClose:function(){this.close()},onPressEscapeClose:function(e){"Escape"===e.key&&this.close()},close:function(){this.lockFocusToModal(!1),this.returnFocusToOrigin(),this.remove()},focus:function(){this.$el.find("button").first().trigger("focus")}};i.models.browseFieldsModal=i.models.Modal.extend(n),i.newBrowseFieldsModal=e=>new i.models.browseFieldsModal(e)}(window.jQuery,0,window.acf)})()})();